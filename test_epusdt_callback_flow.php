<?php
/**
 * EPUSDT回调流程测试脚本
 * 模拟完整的支付流程：创建订单 -> 支付成功 -> 回调处理
 */

echo "=== EPUSDT回调流程测试 ===\n\n";

// 模拟数据库连接
try {
    $pdo = new PDO('mysql:host=127.0.0.1;dbname=tcyp;charset=utf8', 'tcyp', 'eDGSBtezJGfHx7eP');
    echo "✅ 数据库连接成功\n\n";
} catch (Exception $e) {
    echo "❌ 数据库连接失败: " . $e->getMessage() . "\n";
    exit;
}

// 1. 检查数据库表结构
echo "1. 检查数据库表结构\n";
echo "----------------------------\n";

try {
    $stmt = $pdo->query("DESCRIBE recharge");
    $fields = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    $required_fields = ['order_no', 'epusdt_order_id', 'money', 'mid', 'status'];
    $missing_fields = array_diff($required_fields, $fields);
    
    if (empty($missing_fields)) {
        echo "✅ recharge表结构完整\n";
        echo "包含字段: " . implode(', ', $fields) . "\n\n";
    } else {
        echo "❌ recharge表缺少字段: " . implode(', ', $missing_fields) . "\n";
        echo "请执行database_update_epusdt.sql中的SQL语句\n\n";
        
        // 自动执行缺失字段的添加
        if (in_array('epusdt_order_id', $missing_fields)) {
            echo "正在添加epusdt_order_id字段...\n";
            $pdo->exec("ALTER TABLE `recharge` ADD COLUMN `epusdt_order_id` VARCHAR(50) NULL COMMENT 'EPUSDT订单号' AFTER `order_no`");
            $pdo->exec("ALTER TABLE `recharge` ADD INDEX `idx_epusdt_order_id` (`epusdt_order_id`)");
            echo "✅ epusdt_order_id字段添加成功\n\n";
        }
    }
} catch (Exception $e) {
    echo "❌ 检查表结构失败: " . $e->getMessage() . "\n\n";
}

// 2. 创建测试订单
echo "2. 创建测试订单\n";
echo "----------------------------\n";

$test_order_no = 'R' . date('YmdHis') . rand(1000, 9999);
$test_epusdt_order_id = 'EPUSDT_' . time() . rand(100, 999);
$test_amount = 100.50;
$test_user_id = 1; // 假设用户ID为1

try {
    // 插入测试订单
    $stmt = $pdo->prepare("INSERT INTO recharge (order_no, epusdt_order_id, money, mid, status, create_time, update_time) VALUES (?, ?, ?, ?, 1, ?, ?)");
    $current_time = time();
    $stmt->execute([$test_order_no, $test_epusdt_order_id, $test_amount, $test_user_id, $current_time, $current_time]);
    
    echo "✅ 测试订单创建成功\n";
    echo "订单号: {$test_order_no}\n";
    echo "EPUSDT订单号: {$test_epusdt_order_id}\n";
    echo "金额: {$test_amount} USDT\n";
    echo "用户ID: {$test_user_id}\n\n";
} catch (Exception $e) {
    echo "❌ 创建测试订单失败: " . $e->getMessage() . "\n\n";
    exit;
}

// 3. 获取用户当前余额
echo "3. 获取用户当前余额\n";
echo "----------------------------\n";

try {
    $stmt = $pdo->prepare("SELECT money FROM member WHERE id = ?");
    $stmt->execute([$test_user_id]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($user) {
        $original_balance = $user['money'];
        echo "✅ 用户当前余额: {$original_balance}\n\n";
    } else {
        echo "❌ 用户不存在，创建测试用户...\n";
        $pdo->prepare("INSERT INTO member (id, money, create_time) VALUES (?, 0, ?)")->execute([$test_user_id, time()]);
        $original_balance = 0;
        echo "✅ 测试用户创建成功，余额: 0\n\n";
    }
} catch (Exception $e) {
    echo "❌ 获取用户余额失败: " . $e->getMessage() . "\n\n";
    $original_balance = 0;
}

// 4. 模拟EPUSDT回调数据
echo "4. 模拟EPUSDT回调处理\n";
echo "----------------------------\n";

$api_key = 'B81BA11A6528EC298C7DD88C144B8D882568BC1D';
$notify_url = 'https://jsdao.cc/api/callback/pay?gateway=Epusdt';
$redirect_url = 'https://vip.jsdao.cc/#/Mine';

// 构造回调数据
$callback_data = [
    'order_id' => $test_epusdt_order_id,
    'amount' => $test_amount,
    'status' => 2, // 2表示支付成功
    'notify_url' => $notify_url,
    'redirect_url' => $redirect_url
];

// 生成签名
$sign_string = 'amount=' . $callback_data['amount'] . '&notify_url=' . $callback_data['notify_url'] . '&order_id=' . $callback_data['order_id'] . '&redirect_url=' . $callback_data['redirect_url'] . $api_key;
$callback_data['signature'] = md5($sign_string);

echo "回调数据:\n";
echo json_encode($callback_data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n";
echo "签名字符串: {$sign_string}\n";
echo "签名: {$callback_data['signature']}\n\n";

// 5. 模拟回调处理逻辑
echo "5. 模拟回调处理逻辑\n";
echo "----------------------------\n";

try {
    // 开始事务
    $pdo->beginTransaction();
    
    // 查找订单
    $stmt = $pdo->prepare("SELECT * FROM recharge WHERE epusdt_order_id = ? AND status != 2");
    $stmt->execute([$test_epusdt_order_id]);
    $order = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$order) {
        echo "❌ 订单不存在或已处理\n";
        $pdo->rollback();
    } else {
        echo "✅ 找到订单: {$order['order_no']}\n";
        
        // 更新订单状态
        $stmt = $pdo->prepare("UPDATE recharge SET status = 2, pay_time = ?, update_time = ? WHERE order_no = ?");
        $pay_time = time();
        $update_result = $stmt->execute([$pay_time, $pay_time, $order['order_no']]);
        
        if ($update_result) {
            echo "✅ 订单状态更新成功\n";
            
            // 更新用户余额
            $stmt = $pdo->prepare("UPDATE member SET money = money + ? WHERE id = ?");
            $balance_result = $stmt->execute([$order['money'], $order['mid']]);
            
            if ($balance_result) {
                echo "✅ 用户余额更新成功\n";
                $pdo->commit();
                echo "✅ 回调处理完成！\n\n";
            } else {
                echo "❌ 用户余额更新失败\n";
                $pdo->rollback();
            }
        } else {
            echo "❌ 订单状态更新失败\n";
            $pdo->rollback();
        }
    }
} catch (Exception $e) {
    echo "❌ 回调处理异常: " . $e->getMessage() . "\n";
    $pdo->rollback();
}

// 6. 验证结果
echo "6. 验证处理结果\n";
echo "----------------------------\n";

try {
    // 检查订单状态
    $stmt = $pdo->prepare("SELECT * FROM recharge WHERE order_no = ?");
    $stmt->execute([$test_order_no]);
    $final_order = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // 检查用户余额
    $stmt = $pdo->prepare("SELECT money FROM member WHERE id = ?");
    $stmt->execute([$test_user_id]);
    $final_user = $stmt->fetch(PDO::FETCH_ASSOC);
    $final_balance = $final_user['money'];
    
    echo "最终订单状态: " . ($final_order['status'] == 2 ? '支付成功' : '未支付') . "\n";
    echo "原始余额: {$original_balance}\n";
    echo "最终余额: {$final_balance}\n";
    echo "余额变化: " . ($final_balance - $original_balance) . "\n";
    
    if ($final_order['status'] == 2 && ($final_balance - $original_balance) == $test_amount) {
        echo "✅ 回调处理完全正确！\n";
    } else {
        echo "❌ 回调处理有问题\n";
    }
    
} catch (Exception $e) {
    echo "❌ 验证结果失败: " . $e->getMessage() . "\n";
}

// 7. 清理测试数据
echo "\n7. 清理测试数据\n";
echo "----------------------------\n";

try {
    $pdo->prepare("DELETE FROM recharge WHERE order_no = ?")->execute([$test_order_no]);
    echo "✅ 测试订单已清理\n";
} catch (Exception $e) {
    echo "❌ 清理测试数据失败: " . $e->getMessage() . "\n";
}

echo "\n=== 测试完成 ===\n";
echo "如果所有步骤都显示✅，说明EPUSDT回调处理逻辑正常工作。\n";
echo "502错误不会影响实际的支付处理，只是前端状态显示的问题。\n";
?>
