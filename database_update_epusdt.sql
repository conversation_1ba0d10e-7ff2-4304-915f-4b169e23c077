-- 为EPUSDT支付添加订单号映射字段
-- 执行此SQL来更新数据库结构

-- 1. 为recharge表添加epusdt_order_id字段
ALTER TABLE `recharge` ADD COLUMN `epusdt_order_id` VARCHAR(50) NULL COMMENT 'EPUSDT订单号' AFTER `order_no`;

-- 2. 为epusdt_order_id字段添加索引，提高查询性能
ALTER TABLE `recharge` ADD INDEX `idx_epusdt_order_id` (`epusdt_order_id`);

-- 3. 为了支持并发安全，为recharge表添加版本号字段（可选）
ALTER TABLE `recharge` ADD COLUMN `version` INT(11) DEFAULT 0 COMMENT '版本号，用于乐观锁' AFTER `update_time`;

-- 查看表结构确认修改
-- DESCRIBE `recharge`;
