<?php
/**
 * 完全按照eppsost.php demo一模一样的测试
 */

// 完全复制demo的curl_request函数
function curl_request($url, $data=null, $method='post', $header = array("content-type: application/json"), $https=true, $timeout = 5){
    $method = strtoupper($method);
    $ch = curl_init();//初始化
    curl_setopt($ch, CURLOPT_URL, $url);//访问的URL
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);//只获取页面内容，但不输出
    if($https){
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);//https请求 不验证证书
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);//https请求 不验证HOST
    }
    if ($method != "GET") {
        if($method == 'POST'){
            curl_setopt($ch, CURLOPT_POST, true);//请求方式为post请求
        }
        if ($method == 'PUT' || strtoupper($method) == 'DELETE') {
            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $method); //设置请求方式
        }
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);//请求数据
    }
    curl_setopt($ch, CURLOPT_TIMEOUT, $timeout);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $header); //模拟的header头
    //curl_setopt($ch, CURLOPT_HEADER, false);//设置不需要头信息
    $result = curl_exec($ch);//执行请求
    curl_close($ch);//关闭curl，释放资源
    return $result;
}

// 完全复制demo的token函数
function token($length){
    $str = md5(time());
    $token = substr($str,15,$length);
    return $token;
}//随机数生成函数

echo "=== 完全按照eppsost.php demo一模一样的测试 ===\n\n";

// 完全按照demo的变量定义
$amount = (double)100.5;
$notify_url='https://jsdao.cc/api/callback/pay?gateway=Epusdt';//Epusdt的异步回调地址
$redirect_url='https://jsdao.cc/api/my/index';//Epusdt的同步跳转地址,付款成功后跳转到这里
$order_id=(string)token(10);//生成随机数用于订单号
$key='B81BA11A6528EC298C7DD88C144B8D882568BC1D';//Epusdt的自定义密钥

echo "测试参数:\n";
echo "amount: " . $amount . "\n";
echo "notify_url: " . $notify_url . "\n";
echo "redirect_url: " . $redirect_url . "\n";
echo "order_id: " . $order_id . "\n";
echo "key: " . $key . "\n\n";

// 完全按照demo的拼接方式
$str = 'amount='.$amount.'&notify_url='.$notify_url.'&order_id='.$order_id.'&redirect_url='.$redirect_url.$key;//拼接字符串用于MD5计算
echo "签名字符串: " . $str . "\n";

$signature = md5($str);//用MD5算法计算签名
echo "MD5签名: " . $signature . "\n\n";

// 完全按照demo的数据包生成
$data=json_encode(array( 'order_id' => $order_id,//生成数据包，用到了的数组转json的jsonencode
'amount' => $amount,
'notify_url' => $notify_url,
'redirect_url' => $redirect_url,
'signature' => $signature));

echo "请求数据包:\n";
echo $data . "\n\n";

// 完全按照demo的请求方式
$res=curl_request('https://pay.jsdao.cc/api/v1/order/create-transaction',$data,'post');//发起Curl请求并获取返回数据到变量

echo "原始响应:\n";
echo $res . "\n\n";

// 完全按照demo的响应处理
$arr = json_decode($res, true);//对返回数据进行json到数组的转换，用到了jsondecode

if ($arr && isset($arr['data'])) {
    $resdata=$arr['data'];//提取返回数据的数组中的data段落
    $payurl= $resdata['payment_url'];//提取返回数据的数组中的data段落中的支付链接
    $payamount=$resdata['actual_amount'];//提取返回数据的数组中的data段落中的转换后数值

    echo "=== 解析结果（完全按照demo输出） ===\n";
    echo '你的支付链接是 ';
    echo $payurl;
    echo "\n你的计划支付金额是";
    echo $amount;
    echo 'CNY';
    echo "\n你的实际交易是";
    echo $payamount;
    echo 'USD';
    echo "\n若上述为空则你没有填URL中的?n=*的参数且缺少Post数据";
    echo "\n本站基准汇率为1USD=6.25CNY。请勿使用ERC/DEX/BSC进行转账发起。";//部分交易所的地址有黑名单机制，用钱包可解。汇率在Epusdt那边设置。
    echo "\n为了辨识客户订单，请注意实际金额中的尾数不可忽略";//Epusdt使用了尾数来确认订单。注意本版本对接的是V0.0.1的Epusdt
    echo "\n支付页面链接: ".$payurl."\n";
    
    if (!empty($payurl)) {
        echo "\n✅ 创建支付订单成功！\n";
    } else {
        echo "\n❌ 获取支付链接失败\n";
    }
} else {
    echo "❌ 响应数据格式错误或请求失败\n";
    echo "完整响应: " . $res . "\n";
}

// 额外测试：使用demo中提到的最小参数
echo "\n" . str_repeat("=", 60) . "\n";
echo "=== 测试最小金额 ===\n";

$min_amount = (double)0.01;
$min_order_id = (string)token(10);
$min_str = 'amount='.$min_amount.'&notify_url='.$notify_url.'&order_id='.$min_order_id.'&redirect_url='.$redirect_url.$key;
$min_signature = md5($min_str);

$min_data = json_encode(array(
    'order_id' => $min_order_id,
    'amount' => $min_amount,
    'notify_url' => $notify_url,
    'redirect_url' => $redirect_url,
    'signature' => $min_signature
));

echo "最小金额测试数据: " . $min_data . "\n";
echo "签名字符串: " . $min_str . "\n";
echo "签名: " . $min_signature . "\n\n";

$min_res = curl_request('https://pay.jsdao.cc/api/v1/order/create-transaction', $min_data, 'post');
echo "最小金额测试响应: " . $min_res . "\n";

?>
