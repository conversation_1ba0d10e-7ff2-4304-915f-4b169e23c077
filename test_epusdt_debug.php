<?php
/**
 * EPUSDT API调试测试脚本
 */

echo "=== EPUSDT API调试测试 ===\n\n";

$api_url = 'https://pay.jsdao.cc';
$api_key = 'B81BA11A6528EC298C7DD88C144B8D882568BC1D';

// 测试不同的参数组合
$test_cases = [
    'case1' => [
        'name' => '标准参数（amount为数字）',
        'data' => [
            'order_id' => 'TEST_ORDER_' . time(),
            'amount' => 100.5,
            'notify_url' => 'https://jsdao.cc/api/callback/pay?gateway=Epusdt',
            'redirect_url' => 'https://jsdao.cc/api/my/index'
        ]
    ],
    'case2' => [
        'name' => '标准参数（amount为字符串）',
        'data' => [
            'order_id' => 'TEST_ORDER_' . time(),
            'amount' => '100.5',
            'notify_url' => 'https://jsdao.cc/api/callback/pay?gateway=Epusdt',
            'redirect_url' => 'https://jsdao.cc/api/my/index'
        ]
    ],
    'case3' => [
        'name' => '简化的回调URL',
        'data' => [
            'order_id' => 'TEST_ORDER_' . time(),
            'amount' => '100.5',
            'notify_url' => 'https://jsdao.cc/callback',
            'redirect_url' => 'https://jsdao.cc/success'
        ]
    ],
    'case4' => [
        'name' => '添加token字段（测试是否需要）',
        'data' => [
            'order_id' => 'TEST_ORDER_' . time(),
            'amount' => '100.5',
            'token' => 'TQn9Y2khEsLJW1ChVbFMSMeRDow5KcbLSE',
            'notify_url' => 'https://jsdao.cc/api/callback/pay?gateway=Epusdt',
            'redirect_url' => 'https://jsdao.cc/api/my/index'
        ]
    ]
];

foreach ($test_cases as $case_id => $test_case) {
    echo "=== {$test_case['name']} ===\n";
    
    $data = $test_case['data'];
    
    // 生成签名
    $signString = 'amount=' . $data['amount'] . 
                 '&notify_url=' . $data['notify_url'] . 
                 '&order_id=' . $data['order_id'] . 
                 '&redirect_url=' . $data['redirect_url'] . 
                 $api_key;
    
    $data['signature'] = md5($signString);
    
    echo "请求数据: " . json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES) . "\n";
    echo "签名字符串: " . $signString . "\n";
    echo "MD5签名: " . $data['signature'] . "\n";
    
    // 发送请求
    $response = sendTestRequest($api_url . '/api/v1/order/create-transaction', $data);
    
    echo "响应结果: " . $response . "\n";
    echo str_repeat("-", 80) . "\n\n";
}

function sendTestRequest($url, $data) {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Accept: application/json',
        'User-Agent: Mozilla/5.0 (compatible; EPUSDT-Client/1.0)'
    ]);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($response === false) {
        return "cURL错误: " . $error;
    }
    
    return "HTTP状态码: " . $httpCode . " | 响应: " . $response;
}

// 额外测试：尝试GET请求到根路径
echo "=== 额外测试：根路径访问 ===\n";
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $api_url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 5);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (compatible; EPUSDT-Client/1.0)');

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

if ($response === false) {
    echo "根路径访问失败: " . $error . "\n";
} else {
    echo "根路径HTTP状态码: " . $httpCode . "\n";
    echo "根路径响应: " . substr($response, 0, 200) . "\n";
}

?>
