<?php
/**
 * 测试男生素材API接口
 * 用于验证新增的男生素材功能是否正常工作
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 设置内容类型
header('Content-Type: text/html; charset=utf-8');

echo "<h1>男生素材API接口测试</h1>";

// 测试配置
$base_url = 'http://localhost'; // 根据实际情况修改
$test_id = 1; // 测试用的选妃ID

echo "<h2>1. 测试获取选妃详情（包含男生素材）</h2>";

// 测试xuanfeidata接口
$url = $base_url . '/api/xuanfei/xuanfeidata';
$data = array('id' => $test_id);

echo "<p><strong>请求URL:</strong> $url</p>";
echo "<p><strong>请求参数:</strong> " . json_encode($data) . "</p>";

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $url);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 30);
curl_setopt($ch, CURLOPT_HTTPHEADER, array(
    'Content-Type: application/x-www-form-urlencoded',
    'lang: yn_yu' // 设置语言头
));

$response = curl_exec($ch);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

echo "<p><strong>HTTP状态码:</strong> $http_code</p>";
echo "<p><strong>响应内容:</strong></p>";
echo "<pre>" . htmlspecialchars($response) . "</pre>";

echo "<hr>";

echo "<h2>2. 测试获取女生素材专用接口</h2>";

// 测试femaleImages接口
$url = $base_url . '/api/xuanfei/femaleImages';
$data = array('id' => $test_id);

echo "<p><strong>请求URL:</strong> $url</p>";
echo "<p><strong>请求参数:</strong> " . json_encode($data) . "</p>";

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $url);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 30);
curl_setopt($ch, CURLOPT_HTTPHEADER, array(
    'Content-Type: application/x-www-form-urlencoded',
    'lang: yn_yu' // 设置语言头
));

$response = curl_exec($ch);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

echo "<p><strong>HTTP状态码:</strong> $http_code</p>";
echo "<p><strong>响应内容:</strong></p>";
echo "<pre>" . htmlspecialchars($response) . "</pre>";

echo "<hr>";

echo "<h2>3. 测试获取男生素材专用接口</h2>";

// 测试maleImages接口
$url = $base_url . '/api/xuanfei/maleImages';
$data = array('id' => $test_id);

echo "<p><strong>请求URL:</strong> $url</p>";
echo "<p><strong>请求参数:</strong> " . json_encode($data) . "</p>";

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $url);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 30);
curl_setopt($ch, CURLOPT_HTTPHEADER, array(
    'Content-Type: application/x-www-form-urlencoded',
    'lang: yn_yu' // 设置语言头
));

$response = curl_exec($ch);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

echo "<p><strong>HTTP状态码:</strong> $http_code</p>";
echo "<p><strong>响应内容:</strong></p>";
echo "<pre>" . htmlspecialchars($response) . "</pre>";

echo "<hr>";

echo "<h2>4. 测试获取选妃列表（原有接口，仅女生素材）</h2>";

// 测试xuanfeilist接口
$url = $base_url . '/api/xuanfei/xuanfeilist';
$data = array('id' => 1); // 地区ID

echo "<p><strong>请求URL:</strong> $url</p>";
echo "<p><strong>请求参数:</strong> " . json_encode($data) . "</p>";

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $url);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 30);
curl_setopt($ch, CURLOPT_HTTPHEADER, array(
    'Content-Type: application/x-www-form-urlencoded',
    'lang: yn_yu' // 设置语言头
));

$response = curl_exec($ch);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

echo "<p><strong>HTTP状态码:</strong> $http_code</p>";
echo "<p><strong>响应内容:</strong></p>";
echo "<pre>" . htmlspecialchars($response) . "</pre>";

echo "<hr>";

echo "<h2>使用说明</h2>";
echo "<ul>";
echo "<li><strong>xuanfeidata接口:</strong> 获取单个选妃的详细信息，保持原有格式（仅女生素材）</li>";
echo "<li><strong>femaleImages接口:</strong> 专门获取指定选妃的女生素材图片列表</li>";
echo "<li><strong>maleImages接口:</strong> 专门获取指定选妃的男生素材图片列表</li>";
echo "<li><strong>xuanfeilist接口:</strong> 获取选妃列表，保持原有格式（仅女生素材）</li>";
echo "<li><strong>接口兼容性:</strong> 原有接口保持不变，新增专门的男生/女生素材接口</li>";
echo "</ul>";

echo "<h2>前端APP集成示例</h2>";
echo "<pre>";
echo "// 获取女生素材
fetch('/api/xuanfei/femaleImages', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'lang': 'yn_yu'
    },
    body: 'id=1'
})
.then(response => response.json())
.then(data => {
    if(data.code === 200) {
        console.log('女生素材图片:', data.data);
    }
});

// 获取男生素材
fetch('/api/xuanfei/maleImages', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'lang': 'yn_yu'
    },
    body: 'id=1'
})
.then(response => response.json())
.then(data => {
    if(data.code === 200) {
        console.log('男生素材图片:', data.data);
    }
});";
echo "</pre>";
?>
