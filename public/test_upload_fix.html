<!DOCTYPE html>
<html>
<head>
    <title>测试上传修复</title>
    <meta charset="UTF-8">
</head>
<body>
    <h1>测试文件上传修复</h1>
    
    <form id="uploadForm" enctype="multipart/form-data">
        <input type="file" name="file" accept="image/*" required>
        <button type="submit">上传图片</button>
    </form>
    
    <div id="result"></div>
    
    <script>
    document.getElementById('uploadForm').addEventListener('submit', function(e) {
        e.preventDefault();
        
        const formData = new FormData();
        const fileInput = document.querySelector('input[type="file"]');
        formData.append('file', fileInput.files[0]);
        
        fetch('/admin/system/upload', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            console.log('上传响应:', data);
            document.getElementById('result').innerHTML = 
                '<h3>上传结果:</h3>' +
                '<pre>' + JSON.stringify(data, null, 2) + '</pre>' +
                (data.data ? '<img src="' + data.data + '" style="max-width: 300px;">' : '');
        })
        .catch(error => {
            console.error('上传错误:', error);
            document.getElementById('result').innerHTML = '<p style="color: red;">上传失败: ' + error.message + '</p>';
        });
    });
    </script>
</body>
</html>
