<?php
/**
 * 上传配置检查脚本
 * 用于诊断服务器上传配置问题
 */

header('Content-Type: text/html; charset=utf-8');

echo "<h2>服务器上传配置检查</h2>";

// 检查PHP配置
echo "<h3>PHP 配置</h3>";
echo "<table border='1' cellpadding='5' cellspacing='0'>";
echo "<tr><th>配置项</th><th>当前值</th><th>建议值</th><th>状态</th></tr>";

$phpConfigs = [
    'file_uploads' => ['current' => ini_get('file_uploads'), 'recommended' => 'On'],
    'upload_max_filesize' => ['current' => ini_get('upload_max_filesize'), 'recommended' => '2048M'],
    'post_max_size' => ['current' => ini_get('post_max_size'), 'recommended' => '2048M'],
    'memory_limit' => ['current' => ini_get('memory_limit'), 'recommended' => '512M'],
    'max_execution_time' => ['current' => ini_get('max_execution_time'), 'recommended' => '300'],
    'max_input_time' => ['current' => ini_get('max_input_time'), 'recommended' => '300'],
    'max_input_vars' => ['current' => ini_get('max_input_vars'), 'recommended' => '3000'],
];

foreach ($phpConfigs as $key => $config) {
    $current = $config['current'];
    $recommended = $config['recommended'];
    
    // 简单的状态判断
    $status = '⚠️';
    if ($key === 'file_uploads') {
        $status = ($current == '1' || strtolower($current) == 'on') ? '✅' : '❌';
    } elseif (in_array($key, ['upload_max_filesize', 'post_max_size', 'memory_limit'])) {
        $currentBytes = parseSize($current);
        $recommendedBytes = parseSize($recommended);
        $status = ($currentBytes >= $recommendedBytes) ? '✅' : '❌';
    } elseif (in_array($key, ['max_execution_time', 'max_input_time', 'max_input_vars'])) {
        $status = (intval($current) >= intval($recommended)) ? '✅' : '❌';
    }
    
    echo "<tr>";
    echo "<td>{$key}</td>";
    echo "<td>{$current}</td>";
    echo "<td>{$recommended}</td>";
    echo "<td>{$status}</td>";
    echo "</tr>";
}

echo "</table>";

// 检查临时目录
echo "<h3>临时目录检查</h3>";
$tempDir = sys_get_temp_dir();
$uploadTempDir = ini_get('upload_tmp_dir') ?: $tempDir;

echo "<table border='1' cellpadding='5' cellspacing='0'>";
echo "<tr><th>项目</th><th>路径</th><th>状态</th></tr>";
echo "<tr><td>系统临时目录</td><td>{$tempDir}</td><td>" . (is_writable($tempDir) ? '✅ 可写' : '❌ 不可写') . "</td></tr>";
echo "<tr><td>上传临时目录</td><td>{$uploadTempDir}</td><td>" . (is_writable($uploadTempDir) ? '✅ 可写' : '❌ 不可写') . "</td></tr>";
echo "</table>";

// 检查磁盘空间
echo "<h3>磁盘空间检查</h3>";
$freeSpace = disk_free_space('.');
$totalSpace = disk_total_space('.');
$usedSpace = $totalSpace - $freeSpace;

echo "<table border='1' cellpadding='5' cellspacing='0'>";
echo "<tr><th>项目</th><th>大小</th></tr>";
echo "<tr><td>总空间</td><td>" . formatBytes($totalSpace) . "</td></tr>";
echo "<tr><td>已用空间</td><td>" . formatBytes($usedSpace) . "</td></tr>";
echo "<tr><td>可用空间</td><td>" . formatBytes($freeSpace) . "</td></tr>";
echo "</table>";

// 测试上传
echo "<h3>上传测试</h3>";
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['test_file'])) {
    $file = $_FILES['test_file'];
    echo "<div style='background: #f0f0f0; padding: 10px; margin: 10px 0;'>";
    echo "<strong>上传结果：</strong><br>";
    echo "文件名: " . $file['name'] . "<br>";
    echo "文件大小: " . formatBytes($file['size']) . "<br>";
    echo "文件类型: " . $file['type'] . "<br>";
    echo "临时文件: " . $file['tmp_name'] . "<br>";
    echo "错误代码: " . $file['error'] . " (" . getUploadErrorMessage($file['error']) . ")<br>";
    
    if ($file['error'] === UPLOAD_ERR_OK) {
        echo "<span style='color: green;'>✅ 上传成功！</span>";
    } else {
        echo "<span style='color: red;'>❌ 上传失败</span>";
    }
    echo "</div>";
} else {
    echo "<form method='post' enctype='multipart/form-data'>";
    echo "<input type='file' name='test_file' accept='video/*'>";
    echo "<input type='submit' value='测试上传'>";
    echo "</form>";
    echo "<p><small>选择一个小视频文件进行测试上传</small></p>";
}

// 建议
echo "<h3>配置建议</h3>";
echo "<div style='background: #fff3cd; padding: 15px; border: 1px solid #ffeaa7;'>";
echo "<h4>Nginx 配置建议：</h4>";
echo "<pre>
server {
    client_max_body_size 2048M;
    client_body_buffer_size 128k;
    client_body_timeout 300s;
    send_timeout 300s;
    
    location ~ \.php$ {
        fastcgi_read_timeout 300s;
        fastcgi_send_timeout 300s;
    }
}
</pre>";

echo "<h4>PHP 配置建议 (php.ini)：</h4>";
echo "<pre>
file_uploads = On
upload_max_filesize = 2048M
post_max_size = 2048M
memory_limit = 512M
max_execution_time = 300
max_input_time = 300
max_input_vars = 3000
</pre>";
echo "</div>";

// 辅助函数
function parseSize($size) {
    $unit = preg_replace('/[^bkmgtpezy]/i', '', $size);
    $size = preg_replace('/[^0-9\.]/', '', $size);
    if ($unit) {
        return round($size * pow(1024, stripos('bkmgtpezy', $unit[0])));
    } else {
        return round($size);
    }
}

function formatBytes($size, $precision = 2) {
    $base = log($size, 1024);
    $suffixes = array('B', 'KB', 'MB', 'GB', 'TB');
    return round(pow(1024, $base - floor($base)), $precision) . ' ' . $suffixes[floor($base)];
}

function getUploadErrorMessage($errorCode) {
    $errors = [
        UPLOAD_ERR_OK => '没有错误',
        UPLOAD_ERR_INI_SIZE => '文件大小超过了 php.ini 中 upload_max_filesize 的限制',
        UPLOAD_ERR_FORM_SIZE => '文件大小超过了表单中 MAX_FILE_SIZE 的限制',
        UPLOAD_ERR_PARTIAL => '文件只有部分被上传',
        UPLOAD_ERR_NO_FILE => '没有文件被上传',
        UPLOAD_ERR_NO_TMP_DIR => '找不到临时文件夹',
        UPLOAD_ERR_CANT_WRITE => '文件写入失败',
        UPLOAD_ERR_EXTENSION => '文件上传被扩展程序阻止'
    ];
    
    return $errors[$errorCode] ?? '未知上传错误';
}
?>
