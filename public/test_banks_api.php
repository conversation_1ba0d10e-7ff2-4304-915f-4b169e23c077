<?php
/**
 * 测试银行API接口
 * 用于验证数据库更新后的接口返回结果
 */

echo "<h2>银行API接口测试</h2>";

// 模拟API调用，直接查询数据库
try {
    // 数据库配置 - 请根据实际情况修改
    $host = 'localhost';
    $dbname = 'your_database_name'; // 请修改为实际数据库名
    $username = 'your_username';    // 请修改为实际用户名
    $password = 'your_password';    // 请修改为实际密码
    
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h3>bank_list 表数据：</h3>";
    $stmt = $pdo->query("SELECT * FROM bank_list WHERE status = 1 ORDER BY id");
    $bankList = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($bankList)) {
        echo "<p style='color: red;'>❌ bank_list 表中没有找到启用的银行数据</p>";
    } else {
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>ID</th><th>银行名称</th><th>银行代码</th><th>状态</th></tr>";
        foreach ($bankList as $bank) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($bank['id']) . "</td>";
            echo "<td>" . htmlspecialchars($bank['name']) . "</td>";
            echo "<td>" . htmlspecialchars($bank['code'] ?? 'N/A') . "</td>";
            echo "<td>" . ($bank['status'] == 1 ? '✅ 启用' : '❌ 禁用') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    echo "<h3>bank 表数据：</h3>";
    $stmt = $pdo->query("SELECT * FROM bank WHERE status = 1 ORDER BY id");
    $banks = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($banks)) {
        echo "<p style='color: red;'>❌ bank 表中没有找到启用的银行数据</p>";
    } else {
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>ID</th><th>标题</th><th>银行名称</th><th>银行代码</th><th>图标</th><th>状态</th></tr>";
        foreach ($banks as $bank) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($bank['id']) . "</td>";
            echo "<td>" . htmlspecialchars($bank['title'] ?? 'N/A') . "</td>";
            echo "<td>" . htmlspecialchars($bank['name']) . "</td>";
            echo "<td>" . htmlspecialchars($bank['code'] ?? 'N/A') . "</td>";
            echo "<td>" . htmlspecialchars($bank['thumb'] ?? 'N/A') . "</td>";
            echo "<td>" . ($bank['status'] == 1 ? '✅ 启用' : '❌ 禁用') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    echo "<h3>预期结果：</h3>";
    echo "<p>应该看到以下6家墨西哥银行：</p>";
    echo "<ul>";
    echo "<li>BBVA México</li>";
    echo "<li>Banamex</li>";
    echo "<li>Banorte</li>";
    echo "<li>Santander México</li>";
    echo "<li>HSBC México</li>";
    echo "<li>Banco Azteca</li>";
    echo "</ul>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>数据库连接失败：" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p>请修改此文件中的数据库连接配置。</p>";
}

echo "<hr>";
echo "<p><strong>注意：</strong>使用完毕后请删除此测试文件。</p>";
echo "<p><a href='../api/system/getBankList'>测试 getBankList 接口</a></p>";
echo "<p><a href='../api/bank/index'>测试 Bank/index 接口</a></p>";
?>
