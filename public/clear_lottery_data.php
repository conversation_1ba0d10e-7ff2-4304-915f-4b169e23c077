<?php
// 清理彩票数据库数据
echo "<h1>🗑️ 清理彩票数据库数据</h1>";

// 设置中国时区
date_default_timezone_set('Asia/Shanghai');

// 获取当前时间信息
$china_time = time();
$china_date = date('Ymd', $china_time);
$current_time_str = date('Y-m-d H:i:s', $china_time);

echo "<h2>📊 当前状态</h2>";
echo "<p><strong>当前中国时间:</strong> {$current_time_str}</p>";
echo "<p><strong>当前日期:</strong> {$china_date}</p>";

// 数据库配置 - 直接在这里配置
$db_config = [
    'hostname' => 'localhost',
    'database' => 'tcyp',
    'username' => 'tcyp',
    'password' => 'eDGSBtezJGfHx7eP',
    'charset' => 'utf8'
];

if (isset($_GET['action'])) {
    switch ($_GET['action']) {
        case 'clear_all_lottery':
            echo "<h3>🗑️ 清理所有开奖数据...</h3>";
            try {
                // 直接连接数据库
                $dsn = "mysql:host={$db_config['hostname']};dbname={$db_config['database']};charset={$db_config['charset']}";
                $pdo = new PDO($dsn, $db_config['username'], $db_config['password']);
                
                // 清理开奖记录表
                $sql = "DELETE FROM lottery_kj";
                $stmt = $pdo->prepare($sql);
                $result = $stmt->execute();
                $deleted_kj = $stmt->rowCount();
                
                echo "<p style='color:green;'>✅ 已删除 {$deleted_kj} 条开奖记录</p>";
                
                // 清理游戏投注记录（可选）
                if (isset($_GET['clear_game']) && $_GET['clear_game'] == '1') {
                    $sql = "DELETE FROM game";
                    $stmt = $pdo->prepare($sql);
                    $result = $stmt->execute();
                    $deleted_game = $stmt->rowCount();
                    echo "<p style='color:green;'>✅ 已删除 {$deleted_game} 条投注记录</p>";
                }
                
                // 清理预开奖记录（可选）
                if (isset($_GET['clear_yu']) && $_GET['clear_yu'] == '1') {
                    $sql = "DELETE FROM yulottery";
                    $stmt = $pdo->prepare($sql);
                    $result = $stmt->execute();
                    $deleted_yu = $stmt->rowCount();
                    echo "<p style='color:green;'>✅ 已删除 {$deleted_yu} 条预开奖记录</p>";
                }
                
                echo "<p style='color:blue;'>🎉 数据清理完成！</p>";
                
            } catch (Exception $e) {
                echo "<p style='color:red;'>❌ 操作失败: " . $e->getMessage() . "</p>";
            }
            echo "<p><a href='?' style='background:blue;color:white;padding:5px;text-decoration:none;'>返回主页</a></p>";
            break;
            
        case 'clear_today_only':
            echo "<h3>🗑️ 只清理今天的开奖数据...</h3>";
            try {
                $dsn = "mysql:host={$db_config['hostname']};dbname={$db_config['database']};charset={$db_config['charset']}";
                $pdo = new PDO($dsn, $db_config['username'], $db_config['password']);
                
                // 只删除今天的开奖记录
                $today = date('Ymd');
                $sql = "DELETE FROM lottery_kj WHERE expect LIKE ?";
                $stmt = $pdo->prepare($sql);
                $result = $stmt->execute([$today . '%']);
                $deleted = $stmt->rowCount();
                
                echo "<p style='color:green;'>✅ 已删除今天的 {$deleted} 条开奖记录</p>";
                
                // 只删除今天的投注记录（可选）
                if (isset($_GET['clear_game']) && $_GET['clear_game'] == '1') {
                    $sql = "DELETE FROM game WHERE expect LIKE ?";
                    $stmt = $pdo->prepare($sql);
                    $result = $stmt->execute([$today . '%']);
                    $deleted_game = $stmt->rowCount();
                    echo "<p style='color:green;'>✅ 已删除今天的 {$deleted_game} 条投注记录</p>";
                }
                
                echo "<p style='color:blue;'>🎉 今天的数据清理完成！</p>";
                
            } catch (Exception $e) {
                echo "<p style='color:red;'>❌ 操作失败: " . $e->getMessage() . "</p>";
            }
            echo "<p><a href='?' style='background:blue;color:white;padding:5px;text-decoration:none;'>返回主页</a></p>";
            break;
            
        case 'view_data':
            echo "<h3>👀 查看当前数据...</h3>";
            try {
                $dsn = "mysql:host={$db_config['hostname']};dbname={$db_config['database']};charset={$db_config['charset']}";
                $pdo = new PDO($dsn, $db_config['username'], $db_config['password']);
                
                // 查看开奖记录
                $sql = "SELECT COUNT(*) as total FROM lottery_kj";
                $stmt = $pdo->prepare($sql);
                $stmt->execute();
                $total_kj = $stmt->fetch()['total'];
                
                echo "<p><strong>开奖记录总数:</strong> {$total_kj}</p>";
                
                // 查看今天的开奖记录
                $today = date('Ymd');
                $sql = "SELECT * FROM lottery_kj WHERE expect LIKE ? ORDER BY expect DESC LIMIT 10";
                $stmt = $pdo->prepare($sql);
                $stmt->execute([$today . '%']);
                $today_records = $stmt->fetchAll(PDO::FETCH_ASSOC);
                
                echo "<h4>今天的开奖记录 (最新10条):</h4>";
                if ($today_records) {
                    echo "<table border='1' style='border-collapse:collapse; width:100%;'>";
                    echo "<tr><th>期号</th><th>开奖号码</th><th>开奖时间</th><th>create_time</th></tr>";
                    foreach ($today_records as $record) {
                        echo "<tr>";
                        echo "<td>{$record['expect']}</td>";
                        echo "<td>{$record['opencode']}</td>";
                        echo "<td>" . date('Y-m-d H:i:s', $record['create_time']) . "</td>";
                        echo "<td>{$record['create_time']}</td>";
                        echo "</tr>";
                    }
                    echo "</table>";
                } else {
                    echo "<p>暂无今天的开奖记录</p>";
                }
                
                // 查看投注记录
                $sql = "SELECT COUNT(*) as total FROM game";
                $stmt = $pdo->prepare($sql);
                $stmt->execute();
                $total_game = $stmt->fetch()['total'];
                
                echo "<p><strong>投注记录总数:</strong> {$total_game}</p>";
                
                // 查看今天的投注记录
                $sql = "SELECT COUNT(*) as total FROM game WHERE expect LIKE ?";
                $stmt = $pdo->prepare($sql);
                $stmt->execute([$today . '%']);
                $today_game = $stmt->fetch()['total'];
                
                echo "<p><strong>今天的投注记录:</strong> {$today_game}</p>";
                
            } catch (Exception $e) {
                echo "<p style='color:red;'>❌ 操作失败: " . $e->getMessage() . "</p>";
            }
            echo "<p><a href='?' style='background:blue;color:white;padding:5px;text-decoration:none;'>返回主页</a></p>";
            break;
            
        case 'regenerate_data':
            echo "<h3>🔄 重新生成开奖数据...</h3>";
            
            // 调用补充开奖API
            $url = "https://jsdao.cc/api/lotteryapi/updateNewKj";
            echo "<p>正在调用补充开奖API...</p>";
            $result = @file_get_contents($url);
            if ($result !== false) {
                echo "<p style='color:green;'>✅ 补充开奖API调用成功</p>";
                echo "<pre style='background:#f0f0f0;padding:10px; max-height:200px; overflow-y:scroll;'>" . htmlspecialchars($result) . "</pre>";
            } else {
                echo "<p style='color:red;'>❌ 补充开奖API调用失败</p>";
            }
            
            // 调用当前开奖API
            $url = "https://jsdao.cc/api/lotteryapi/kj";
            echo "<p>正在调用当前开奖API...</p>";
            $result = @file_get_contents($url);
            if ($result !== false) {
                echo "<p style='color:green;'>✅ 当前开奖API调用成功</p>";
                echo "<pre style='background:#f0f0f0;padding:10px; max-height:200px; overflow-y:scroll;'>" . htmlspecialchars($result) . "</pre>";
            } else {
                echo "<p style='color:red;'>❌ 当前开奖API调用失败</p>";
            }
            
            echo "<p style='color:blue;'>🎉 数据重新生成完成！</p>";
            echo "<p><a href='?' style='background:blue;color:white;padding:5px;text-decoration:none;'>返回主页</a></p>";
            break;
    }
    
} else {
    echo "<div style='margin:20px 0;'>";
    echo "<a href='?action=view_data' style='background:blue;color:white;padding:10px;margin:5px;text-decoration:none;display:inline-block;'>👀 查看当前数据</a>";
    echo "<a href='?action=clear_today_only' style='background:orange;color:white;padding:10px;margin:5px;text-decoration:none;display:inline-block;'>🗑️ 清理今天数据</a>";
    echo "<a href='?action=clear_all_lottery' style='background:red;color:white;padding:10px;margin:5px;text-decoration:none;display:inline-block;'>🗑️ 清理所有开奖数据</a>";
    echo "</div>";
    
    echo "<div style='margin:20px 0;'>";
    echo "<a href='?action=clear_today_only&clear_game=1' style='background:darkorange;color:white;padding:10px;margin:5px;text-decoration:none;display:inline-block;'>🗑️ 清理今天数据+投注</a>";
    echo "<a href='?action=clear_all_lottery&clear_game=1&clear_yu=1' style='background:darkred;color:white;padding:10px;margin:5px;text-decoration:none;display:inline-block;'>🗑️ 清理所有数据</a>";
    echo "</div>";
    
    echo "<div style='margin:20px 0;'>";
    echo "<a href='?action=regenerate_data' style='background:green;color:white;padding:10px;margin:5px;text-decoration:none;display:inline-block;'>🔄 重新生成数据</a>";
    echo "</div>";
    
    echo "<h3>📋 操作说明</h3>";
    echo "<ul>";
    echo "<li><strong>查看当前数据:</strong> 查看数据库中的开奖记录和投注记录数量</li>";
    echo "<li><strong>清理今天数据:</strong> 只删除今天的开奖记录</li>";
    echo "<li><strong>清理所有开奖数据:</strong> 删除所有开奖记录</li>";
    echo "<li><strong>清理今天数据+投注:</strong> 删除今天的开奖记录和投注记录</li>";
    echo "<li><strong>清理所有数据:</strong> 删除所有开奖、投注、预开奖记录</li>";
    echo "<li><strong>重新生成数据:</strong> 调用API重新生成开奖数据</li>";
    echo "</ul>";
    
    echo "<h3>🔍 建议操作顺序</h3>";
    echo "<ol>";
    echo "<li>先点击 <strong>查看当前数据</strong> 了解现状</li>";
    echo "<li>点击 <strong>清理今天数据</strong> 或 <strong>清理所有开奖数据</strong></li>";
    echo "<li>点击 <strong>重新生成数据</strong> 生成正确的开奖记录</li>";
    echo "<li>再次点击 <strong>查看当前数据</strong> 验证结果</li>";
    echo "</ol>";
    
    echo "<div style='background:#fff3cd;border:1px solid #ffeaa7;padding:10px;margin:20px 0;'>";
    echo "<h4>⚠️ 注意事项</h4>";
    echo "<ul>";
    echo "<li>清理数据操作不可逆，请谨慎操作</li>";
    echo "<li>如果有用户投注记录，建议只清理开奖数据，不要清理投注数据</li>";
    echo "<li>清理后需要重新生成数据才能正常使用</li>";
    echo "</ul>";
    echo "</div>";
}

echo "<hr>";
echo "<p><small>脚本执行时间: " . date('Y-m-d H:i:s') . "</small></p>";
?>
