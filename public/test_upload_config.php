<?php
/**
 * 测试上传配置
 * 访问此文件可以查看当前的上传配置状态
 */

echo "<h2>视频上传配置测试</h2>";

echo "<h3>PHP配置：</h3>";
echo "upload_max_filesize: " . ini_get('upload_max_filesize') . "<br>";
echo "post_max_size: " . ini_get('post_max_size') . "<br>";
echo "max_execution_time: " . ini_get('max_execution_time') . "<br>";
echo "max_input_time: " . ini_get('max_input_time') . "<br>";
echo "memory_limit: " . ini_get('memory_limit') . "<br>";

echo "<h3>目录权限检查：</h3>";
$videoDir = './video/files';
if (!is_dir($videoDir)) {
    if (mkdir($videoDir, 0755, true)) {
        echo "✅ 视频目录创建成功: $videoDir<br>";
    } else {
        echo "❌ 视频目录创建失败: $videoDir<br>";
    }
} else {
    echo "✅ 视频目录已存在: $videoDir<br>";
}

if (is_writable($videoDir)) {
    echo "✅ 视频目录可写<br>";
} else {
    echo "❌ 视频目录不可写<br>";
}

echo "<h3>存储模式：</h3>";
echo "✅ 当前配置：强制使用本地存储<br>";
echo "📁 视频存储路径：/video/files/<br>";

echo "<h3>文件大小限制：</h3>";
echo "✅ 最大上传大小：2GB (2147483648 字节)<br>";

echo "<h3>支持的视频格式：</h3>";
echo "✅ MP4, AVI, MOV, WMV, FLV, MKV, WEBM, M4V, 3GP<br>";

echo "<hr>";
echo "<p><strong>注意：</strong>如果上传仍然失败，请检查Web服务器配置并重启服务器。</p>";
echo "<p><a href='../admin/video/operation/add'>返回视频上传页面</a></p>";
?>
