<?php
// +----------------------------------------------------------------------
// | ThinkPHP [ WE CAN DO IT JUST THINK ]
// +----------------------------------------------------------------------
// | Copyright (c) 2006-2018 http://thinkphp.cn All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: liu21st <<EMAIL>>
// +----------------------------------------------------------------------

// [ 应用入口文件 ]
namespace think;

// 加载基础文件
require __DIR__ . '/../thinkphp/base.php';
date_default_timezone_set('Asia/Shanghai');
header('Access-Control-Allow-Origin:*');
header('Access-Control-Allow-Headers:*');
header('Access-Control-Request-Method:*');
if( $_SERVER['REQUEST_METHOD'] == 'OPTIONS'){
   exit;
}
// 支持事先使用静态方法设置Request对象和Config对象
define('APP_PATH', __DIR__ . '/../application/');
// 执行应用并响应
Container::get('app')->run()->send();
