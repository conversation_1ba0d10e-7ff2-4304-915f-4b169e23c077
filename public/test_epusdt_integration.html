<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EPUSDT集成测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; color: #155724; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; color: #721c24; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; color: #0c5460; }
        button { padding: 10px 15px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #0056b3; }
        input, textarea { width: 100%; padding: 8px; margin: 5px 0; border: 1px solid #ddd; border-radius: 3px; }
        .result { margin-top: 10px; padding: 10px; background: #f8f9fa; border-radius: 3px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>EPUSDT支付集成测试</h1>
        
        <div class="section info">
            <h3>测试说明</h3>
            <p>此页面用于测试EPUSDT支付集成功能，包括：</p>
            <ul>
                <li>获取充值配置</li>
                <li>创建充值订单</li>
                <li>查询订单状态</li>
                <li>获取充值记录</li>
            </ul>
            <p><strong>注意：</strong>测试前请确保已在后台正确配置EPUSDT设置。</p>
        </div>

        <div class="section">
            <h3>1. 获取充值配置</h3>
            <button onclick="getRechargeConfig()">获取充值配置</button>
            <div id="configResult" class="result"></div>
        </div>

        <div class="section">
            <h3>2. 创建充值订单</h3>
            <div>
                <label>用户Token (base64编码的用户ID):</label>
                <input type="text" id="userToken" placeholder="请输入用户Token">
            </div>
            <div>
                <label>充值金额:</label>
                <input type="number" id="amount" placeholder="请输入充值金额" value="10" step="0.01">
            </div>
            <button onclick="createOrder()">创建充值订单</button>
            <div id="orderResult" class="result"></div>
        </div>

        <div class="section">
            <h3>3. 查询订单状态</h3>
            <div>
                <label>订单号:</label>
                <input type="text" id="orderNo" placeholder="请输入订单号">
            </div>
            <button onclick="getOrderStatus()">查询订单状态</button>
            <div id="statusResult" class="result"></div>
        </div>

        <div class="section">
            <h3>4. 获取充值记录</h3>
            <button onclick="getRechargeHistory()">获取充值记录</button>
            <div id="historyResult" class="result"></div>
        </div>

        <div class="section">
            <h3>5. 后台配置链接</h3>
            <p><a href="/admin/epusdt_config/index" target="_blank">访问EPUSDT配置页面</a></p>
        </div>
    </div>

    <script>
        function makeRequest(url, method = 'GET', data = null, token = null) {
            const options = {
                method: method,
                headers: {
                    'Content-Type': 'application/json',
                }
            };
            
            if (token) {
                options.headers['token'] = token;
            }
            
            if (data && method !== 'GET') {
                options.body = JSON.stringify(data);
            }
            
            return fetch(url, options)
                .then(response => response.json())
                .catch(error => ({ error: error.message }));
        }

        function getRechargeConfig() {
            makeRequest('/api/recharge/getRechargeConfig')
                .then(data => {
                    document.getElementById('configResult').innerHTML = 
                        '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
                });
        }

        function createOrder() {
            const token = document.getElementById('userToken').value;
            const amount = document.getElementById('amount').value;
            
            if (!token || !amount) {
                alert('请填写用户Token和充值金额');
                return;
            }
            
            const data = {
                amount: parseFloat(amount),
                pay_way: 'Epusdt'
            };
            
            makeRequest('/api/recharge/createOrder', 'POST', data, token)
                .then(result => {
                    document.getElementById('orderResult').innerHTML = 
                        '<pre>' + JSON.stringify(result, null, 2) + '</pre>';
                });
        }

        function getOrderStatus() {
            const token = document.getElementById('userToken').value;
            const orderNo = document.getElementById('orderNo').value;
            
            if (!token || !orderNo) {
                alert('请填写用户Token和订单号');
                return;
            }
            
            const data = { order_no: orderNo };
            
            makeRequest('/api/recharge/getOrderStatus', 'POST', data, token)
                .then(result => {
                    document.getElementById('statusResult').innerHTML = 
                        '<pre>' + JSON.stringify(result, null, 2) + '</pre>';
                });
        }

        function getRechargeHistory() {
            const token = document.getElementById('userToken').value;
            
            if (!token) {
                alert('请填写用户Token');
                return;
            }
            
            makeRequest('/api/recharge/getRechargeHistory?page=1&limit=10', 'GET', null, token)
                .then(result => {
                    document.getElementById('historyResult').innerHTML = 
                        '<pre>' + JSON.stringify(result, null, 2) + '</pre>';
                });
        }
    </script>
</body>
</html>
