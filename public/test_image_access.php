<?php
// 测试图片访问
$testImages = [
    '/lottery/banner/20250627/09aac69840711ea6012fc2955ec274c1.jpg',
    '/lottery/banner/20250627/3599db5cdcaa5d32a288d0ff05a54fd1.jpg',
];

echo "<h2>图片访问测试</h2>";

foreach ($testImages as $imagePath) {
    $fullPath = $_SERVER['DOCUMENT_ROOT'] . $imagePath;
    echo "<h3>测试图片: {$imagePath}</h3>";
    echo "<p>完整路径: {$fullPath}</p>";
    echo "<p>文件是否存在: " . (file_exists($fullPath) ? '是' : '否') . "</p>";
    
    if (file_exists($fullPath)) {
        echo "<p>文件大小: " . filesize($fullPath) . " 字节</p>";
        echo "<p>文件权限: " . substr(sprintf('%o', fileperms($fullPath)), -4) . "</p>";
        echo "<img src='{$imagePath}' style='max-width: 300px; border: 1px solid #ccc;' alt='测试图片' />";
    }
    echo "<hr>";
}

// 测试当前目录结构
echo "<h3>当前目录结构</h3>";
$bannerDir = $_SERVER['DOCUMENT_ROOT'] . '/lottery/banner/';
if (is_dir($bannerDir)) {
    $dirs = scandir($bannerDir);
    foreach ($dirs as $dir) {
        if ($dir != '.' && $dir != '..' && is_dir($bannerDir . $dir)) {
            echo "<p>目录: {$dir}</p>";
            $files = scandir($bannerDir . $dir);
            foreach ($files as $file) {
                if ($file != '.' && $file != '..' && is_file($bannerDir . $dir . '/' . $file)) {
                    echo "<p>&nbsp;&nbsp;文件: {$file}</p>";
                }
            }
        }
    }
}
?>
