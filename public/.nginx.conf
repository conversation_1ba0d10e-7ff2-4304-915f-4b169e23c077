# Nginx配置文件 - 用于支持大文件上传
# 如果您使用的是Nginx服务器，请将以下配置添加到您的站点配置中

# 设置客户端请求体的最大大小为2GB
client_max_body_size 2048M;

# 设置客户端请求头的缓冲区大小
client_header_buffer_size 16k;
large_client_header_buffers 4 16k;

# 设置客户端请求体的缓冲区大小
client_body_buffer_size 128k;

# 设置客户端请求体的超时时间
client_body_timeout 300s;

# 设置客户端请求头的超时时间
client_header_timeout 300s;

# 设置发送响应给客户端的超时时间
send_timeout 300s;

# 代理相关配置（如果使用代理）
proxy_connect_timeout 300s;
proxy_send_timeout 300s;
proxy_read_timeout 300s;
proxy_buffer_size 64k;
proxy_buffers 4 32k;
proxy_busy_buffers_size 64k;
