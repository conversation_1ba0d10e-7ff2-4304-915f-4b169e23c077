<?php
/**
 * EPUSDT集成测试页面
 * 用于测试EPUSDT支付功能是否正常工作
 */

echo "<h2>EPUSDT支付集成测试</h2>";

// 数据库配置 - 请根据实际情况修改
$host = '127.0.0.1';
$dbname = 'tcyp'; // 请修改为实际数据库名
$username = 'tcyp';    // 请修改为实际用户名
$password = 'eDGSBtezJGfHx7eP';    // 请修改为实际密码

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h3>✅ 数据库连接成功</h3>";
    
    // 检查EPUSDT配置
    echo "<h3>EPUSDT配置检查：</h3>";
    $stmt = $pdo->query("SELECT * FROM system WHERE `key` = 'epusdt'");
    $epusdtConfig = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($epusdtConfig) {
        $config = json_decode($epusdtConfig['value'], true);
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>配置项</th><th>值</th><th>状态</th></tr>";
        
        $items = [
            'enabled' => '是否启用',
            'api_url' => 'API地址',
            'api_key' => 'API密钥',
            'min_amount' => '最小充值金额',
            'max_amount' => '最大充值金额'
        ];
        
        foreach ($items as $key => $label) {
            $value = isset($config[$key]) ? $config[$key] : '未设置';
            $status = '';
            
            if ($key === 'enabled') {
                $status = $value == 1 ? '✅ 已启用' : '❌ 未启用';
            } elseif ($key === 'api_url') {
                $status = !empty($value) ? '✅ 已设置' : '❌ 未设置';
            } elseif ($key === 'api_key') {
                $status = !empty($value) ? '✅ 已设置' : '❌ 未设置';
                $value = !empty($value) ? substr($value, 0, 10) . '...' : '未设置';
            } else {
                $status = is_numeric($value) && $value > 0 ? '✅ 正常' : '❌ 异常';
            }
            
            echo "<tr>";
            echo "<td>$label</td>";
            echo "<td>$value</td>";
            echo "<td>$status</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: red;'>❌ 未找到EPUSDT配置，请先在后台配置</p>";
    }
    
    // 检查支付类文件
    echo "<h3>支付类文件检查：</h3>";
    $epusdtFile = '../application/api/pay/Epusdt.php';
    if (file_exists($epusdtFile)) {
        echo "<p>✅ EPUSDT支付类文件存在</p>";
    } else {
        echo "<p style='color: red;'>❌ EPUSDT支付类文件不存在</p>";
    }
    
    // 检查充值接口文件
    $rechargeFile = '../application/api/controller/Recharge.php';
    if (file_exists($rechargeFile)) {
        echo "<p>✅ 充值接口文件存在</p>";
    } else {
        echo "<p style='color: red;'>❌ 充值接口文件不存在</p>";
    }
    
    // 检查后台配置文件
    $configFile = '../application/admin/controller/EpusdtConfig.php';
    if (file_exists($configFile)) {
        echo "<p>✅ 后台配置文件存在</p>";
    } else {
        echo "<p style='color: red;'>❌ 后台配置文件不存在</p>";
    }
    
    // 检查数据库表结构
    echo "<h3>数据库表检查：</h3>";
    
    // 检查recharge表
    $stmt = $pdo->query("DESCRIBE recharge");
    $rechargeFields = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    $requiredFields = ['id', 'order_no', 'money', 'mid', 'status', 'create_time', 'pay_time'];
    $missingFields = array_diff($requiredFields, $rechargeFields);
    
    if (empty($missingFields)) {
        echo "<p>✅ recharge表结构正常</p>";
    } else {
        echo "<p style='color: red;'>❌ recharge表缺少字段: " . implode(', ', $missingFields) . "</p>";
    }
    
    // 检查member表
    $stmt = $pdo->query("DESCRIBE member");
    $memberFields = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    if (in_array('money', $memberFields)) {
        echo "<p>✅ member表包含money字段</p>";
    } else {
        echo "<p style='color: red;'>❌ member表缺少money字段</p>";
    }
    
    echo "<h3>API接口测试：</h3>";
    echo "<p><a href='/api/recharge/getRechargeConfig' target='_blank'>测试获取充值配置接口</a></p>";
    echo "<p><a href='/admin/epusdt_config/index' target='_blank'>访问后台EPUSDT配置页面</a></p>";
    
    echo "<h3>回调地址：</h3>";
    $callbackUrl = (isset($_SERVER['HTTPS']) ? 'https' : 'http') . '://' . $_SERVER['HTTP_HOST'] . '/api/callback/pay/Epusdt';
    echo "<p>EPUSDT回调地址：<code>$callbackUrl</code></p>";
    echo "<p style='color: orange;'>⚠️ 请在EPUSDT后台配置此回调地址</p>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>数据库连接失败：" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p>请修改此文件中的数据库连接配置。</p>";
}

echo "<hr>";
echo "<p><strong>注意：</strong>使用完毕后请删除此测试文件。</p>";
?>
