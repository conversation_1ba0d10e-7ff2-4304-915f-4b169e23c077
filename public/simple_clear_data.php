<?php
// 简单的数据清理工具
echo "<h1>🗑️ 简单数据清理工具</h1>";

// 设置中国时区
date_default_timezone_set('Asia/Shanghai');

// 获取当前时间信息
$china_time = time();
$china_date = date('Ymd', $china_time);
$current_time_str = date('Y-m-d H:i:s', $china_time);

echo "<h2>📊 当前状态</h2>";
echo "<p><strong>当前中国时间:</strong> {$current_time_str}</p>";
echo "<p><strong>当前日期:</strong> {$china_date}</p>";

if (isset($_GET['action'])) {
    switch ($_GET['action']) {
        case 'sql_clear_all':
            echo "<h3>📋 SQL清理命令</h3>";
            echo "<p>请在数据库管理工具中执行以下SQL命令：</p>";
            echo "<div style='background:#f8f9fa;border:1px solid #dee2e6;padding:15px;margin:10px 0;'>";
            echo "<h4>清理所有开奖数据：</h4>";
            echo "<code style='display:block;background:#e9ecef;padding:10px;margin:5px 0;'>DELETE FROM lottery_kj;</code>";
            echo "<h4>清理所有投注数据（可选）：</h4>";
            echo "<code style='display:block;background:#e9ecef;padding:10px;margin:5px 0;'>DELETE FROM game;</code>";
            echo "<h4>清理预开奖数据（可选）：</h4>";
            echo "<code style='display:block;background:#e9ecef;padding:10px;margin:5px 0;'>DELETE FROM yulottery;</code>";
            echo "</div>";
            echo "<p><a href='?' style='background:blue;color:white;padding:5px;text-decoration:none;'>返回主页</a></p>";
            break;
            
        case 'sql_clear_today':
            echo "<h3>📋 SQL清理今天数据命令</h3>";
            echo "<p>请在数据库管理工具中执行以下SQL命令：</p>";
            echo "<div style='background:#f8f9fa;border:1px solid #dee2e6;padding:15px;margin:10px 0;'>";
            echo "<h4>清理今天的开奖数据：</h4>";
            echo "<code style='display:block;background:#e9ecef;padding:10px;margin:5px 0;'>DELETE FROM lottery_kj WHERE expect LIKE '{$china_date}%';</code>";
            echo "<h4>清理今天的投注数据（可选）：</h4>";
            echo "<code style='display:block;background:#e9ecef;padding:10px;margin:5px 0;'>DELETE FROM game WHERE expect LIKE '{$china_date}%';</code>";
            echo "</div>";
            echo "<p><a href='?' style='background:blue;color:white;padding:5px;text-decoration:none;'>返回主页</a></p>";
            break;
            
        case 'regenerate_data':
            echo "<h3>🔄 重新生成开奖数据...</h3>";
            
            // 调用补充开奖API
            $url = "https://jsdao.cc/api/lotteryapi/updateNewKj";
            echo "<p>正在调用补充开奖API...</p>";
            $result = @file_get_contents($url);
            if ($result !== false) {
                echo "<p style='color:green;'>✅ 补充开奖API调用成功</p>";
                echo "<details><summary>查看API响应</summary>";
                echo "<pre style='background:#f0f0f0;padding:10px; max-height:200px; overflow-y:scroll;'>" . htmlspecialchars($result) . "</pre>";
                echo "</details>";
            } else {
                echo "<p style='color:red;'>❌ 补充开奖API调用失败</p>";
            }
            
            echo "<br>";
            
            // 调用当前开奖API
            $url = "https://jsdao.cc/api/lotteryapi/kj";
            echo "<p>正在调用当前开奖API...</p>";
            $result = @file_get_contents($url);
            if ($result !== false) {
                echo "<p style='color:green;'>✅ 当前开奖API调用成功</p>";
                echo "<details><summary>查看API响应</summary>";
                echo "<pre style='background:#f0f0f0;padding:10px; max-height:200px; overflow-y:scroll;'>" . htmlspecialchars($result) . "</pre>";
                echo "</details>";
            } else {
                echo "<p style='color:red;'>❌ 当前开奖API调用失败</p>";
            }
            
            echo "<p style='color:blue;'>🎉 数据重新生成完成！</p>";
            echo "<p><a href='?' style='background:blue;color:white;padding:5px;text-decoration:none;'>返回主页</a></p>";
            break;
            
        case 'test_frontend':
            echo "<h3>🧪 测试前端接口...</h3>";
            $url = "https://jsdao.cc/api/Lottery/getLotteryInfo?key=game1";
            
            // 使用 curl 获取响应
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'token: ' . base64_encode('1') // 假设用户ID为1
            ]);
            $result = curl_exec($ch);
            $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);
            
            echo "<p><strong>HTTP状态码:</strong> {$http_code}</p>";
            echo "<p><strong>接口响应:</strong></p>";
            echo "<pre style='background:#f0f0f0;padding:10px;'>" . htmlspecialchars($result) . "</pre>";
            
            // 尝试解析JSON
            $data = json_decode($result, true);
            if ($data && isset($data['data'])) {
                echo "<h4>解析后的数据:</h4>";
                echo "<p><strong>当前期号:</strong> " . ($data['data']['now_expect'] ?? '未知') . "</p>";
                echo "<p><strong>下期期号:</strong> " . ($data['data']['next_expect'] ?? '未知') . "</p>";
                echo "<p><strong>倒计时:</strong> " . ($data['data']['second'] ?? '未知') . " 秒</p>";
                if (isset($data['data']['opencode'])) {
                    echo "<p><strong>开奖号码:</strong> " . implode(',', $data['data']['opencode']) . "</p>";
                }
            }
            echo "<p><a href='?' style='background:blue;color:white;padding:5px;text-decoration:none;'>返回主页</a></p>";
            break;
    }
    
} else {
    echo "<div style='margin:20px 0;'>";
    echo "<a href='?action=sql_clear_today' style='background:orange;color:white;padding:10px;margin:5px;text-decoration:none;display:inline-block;'>📋 获取清理今天数据SQL</a>";
    echo "<a href='?action=sql_clear_all' style='background:red;color:white;padding:10px;margin:5px;text-decoration:none;display:inline-block;'>📋 获取清理所有数据SQL</a>";
    echo "</div>";
    
    echo "<div style='margin:20px 0;'>";
    echo "<a href='?action=regenerate_data' style='background:green;color:white;padding:10px;margin:5px;text-decoration:none;display:inline-block;'>🔄 重新生成数据</a>";
    echo "<a href='?action=test_frontend' style='background:blue;color:white;padding:10px;margin:5px;text-decoration:none;display:inline-block;'>🧪 测试前端接口</a>";
    echo "</div>";
    
    echo "<h3>📋 操作说明</h3>";
    echo "<ul>";
    echo "<li><strong>获取清理SQL:</strong> 生成SQL命令，您可以在phpMyAdmin或其他数据库管理工具中执行</li>";
    echo "<li><strong>重新生成数据:</strong> 调用API重新生成开奖数据</li>";
    echo "<li><strong>测试前端接口:</strong> 检查前端接口是否正常返回数据</li>";
    echo "</ul>";
    
    echo "<h3>🔍 建议操作顺序</h3>";
    echo "<ol>";
    echo "<li>点击 <strong>获取清理今天数据SQL</strong> 或 <strong>获取清理所有数据SQL</strong></li>";
    echo "<li>复制SQL命令到数据库管理工具中执行</li>";
    echo "<li>点击 <strong>重新生成数据</strong> 生成正确的开奖记录</li>";
    echo "<li>点击 <strong>测试前端接口</strong> 验证结果</li>";
    echo "</ol>";
    
    echo "<div style='background:#fff3cd;border:1px solid #ffeaa7;padding:10px;margin:20px 0;'>";
    echo "<h4>⚠️ 注意事项</h4>";
    echo "<ul>";
    echo "<li>请在数据库管理工具（如phpMyAdmin）中执行SQL命令</li>";
    echo "<li>清理数据操作不可逆，请谨慎操作</li>";
    echo "<li>如果有用户投注记录，建议只清理开奖数据</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div style='background:#d1ecf1;border:1px solid #bee5eb;padding:10px;margin:20px 0;'>";
    echo "<h4>💡 快速修复方案</h4>";
    echo "<p>如果您只是想快速修复时间问题，建议：</p>";
    echo "<ol>";
    echo "<li>点击 <strong>获取清理今天数据SQL</strong></li>";
    echo "<li>在数据库中执行清理命令</li>";
    echo "<li>点击 <strong>重新生成数据</strong></li>";
    echo "</ol>";
    echo "</div>";
}

echo "<hr>";
echo "<p><small>脚本执行时间: " . date('Y-m-d H:i:s') . "</small></p>";
?>
