<?php
// 清理重复的 public 目录文件
header('Content-Type: text/html; charset=utf-8');

echo "<h2>清理重复的 public 目录文件</h2>";

$wrongPath = __DIR__ . '/public/lottery/banner/';
$correctPath = __DIR__ . '/lottery/banner/';

echo "<h3>检查错误路径</h3>";
echo "<p><strong>错误路径:</strong> {$wrongPath}</p>";
echo "<p><strong>正确路径:</strong> {$correctPath}</p>";

if (is_dir($wrongPath)) {
    echo "<p style='color: orange;'>发现错误的 public/public 目录结构</p>";
    
    // 确保正确的目录存在
    if (!is_dir($correctPath)) {
        mkdir($correctPath, 0755, true);
        echo "<p style='color: green;'>创建正确的目录: {$correctPath}</p>";
    }
    
    // 移动文件
    $files = glob($wrongPath . '*');
    $movedCount = 0;
    
    foreach ($files as $file) {
        if (is_file($file)) {
            $fileName = basename($file);
            $newPath = $correctPath . $fileName;
            
            if (rename($file, $newPath)) {
                echo "<p style='color: green;'>移动文件: {$fileName}</p>";
                $movedCount++;
            } else {
                echo "<p style='color: red;'>移动失败: {$fileName}</p>";
            }
        } elseif (is_dir($file)) {
            $dirName = basename($file);
            $newDirPath = $correctPath . $dirName;
            
            // 递归移动目录
            if (moveDirectory($file, $newDirPath)) {
                echo "<p style='color: green;'>移动目录: {$dirName}</p>";
                $movedCount++;
            } else {
                echo "<p style='color: red;'>移动目录失败: {$dirName}</p>";
            }
        }
    }
    
    echo "<p><strong>总共移动了 {$movedCount} 个文件/目录</strong></p>";
    
    // 尝试删除空的错误目录
    if (count(glob($wrongPath . '*')) === 0) {
        if (rmdir($wrongPath)) {
            echo "<p style='color: green;'>删除空的错误目录</p>";
        }
    }
    
} else {
    echo "<p style='color: green;'>没有发现错误的 public/public 目录</p>";
}

echo "<h3>当前文件列表</h3>";
if (is_dir($correctPath)) {
    $files = glob($correctPath . '*');
    if ($files) {
        echo "<ul>";
        foreach ($files as $file) {
            $fileName = basename($file);
            $webPath = '/lottery/banner/' . $fileName;
            if (is_file($file)) {
                echo "<li><a href='{$webPath}' target='_blank'>{$fileName}</a> (" . filesize($file) . " 字节)</li>";
            } else {
                echo "<li><strong>{$fileName}/</strong> (目录)</li>";
            }
        }
        echo "</ul>";
    } else {
        echo "<p>目录为空</p>";
    }
} else {
    echo "<p>正确的目录不存在</p>";
}

function moveDirectory($source, $destination) {
    if (!is_dir($source)) {
        return false;
    }
    
    if (!is_dir($destination)) {
        mkdir($destination, 0755, true);
    }
    
    $files = glob($source . '/*');
    foreach ($files as $file) {
        $fileName = basename($file);
        $newPath = $destination . '/' . $fileName;
        
        if (is_file($file)) {
            if (!rename($file, $newPath)) {
                return false;
            }
        } elseif (is_dir($file)) {
            if (!moveDirectory($file, $newPath)) {
                return false;
            }
        }
    }
    
    return rmdir($source);
}

echo "<hr><p><a href='test_upload.php'>测试上传</a> | <a href='../admin/banner/operation/add'>返回Banner管理</a></p>";
?>
