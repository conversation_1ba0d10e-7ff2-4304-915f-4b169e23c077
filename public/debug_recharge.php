<?php
/**
 * 充值功能调试脚本
 * 用于诊断充值订单创建失败的问题
 */

echo "<h2>充值功能调试</h2>";

// 数据库配置
$host = '127.0.0.1';
$dbname = 'tcyp';
$username = 'tcyp';
$password = 'eDGSBtezJGfHx7eP';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<p style='color: green;'>✅ 数据库连接成功</p>";
    
    // 1. 检查recharge表结构
    echo "<h3>1. 检查recharge表结构：</h3>";
    $stmt = $pdo->query("DESCRIBE recharge");
    $fields = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>字段名</th><th>类型</th><th>是否为空</th><th>键</th><th>默认值</th><th>额外</th></tr>";
    foreach ($fields as $field) {
        echo "<tr>";
        echo "<td>{$field['Field']}</td>";
        echo "<td>{$field['Type']}</td>";
        echo "<td>{$field['Null']}</td>";
        echo "<td>{$field['Key']}</td>";
        echo "<td>{$field['Default']}</td>";
        echo "<td>{$field['Extra']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // 2. 检查必需字段
    echo "<h3>2. 检查必需字段：</h3>";
    $requiredFields = ['id', 'order_no', 'money', 'mid', 'type', 'status', 'create_time', 'update_time'];
    $existingFields = array_column($fields, 'Field');
    
    foreach ($requiredFields as $field) {
        if (in_array($field, $existingFields)) {
            echo "<p style='color: green;'>✅ $field 字段存在</p>";
        } else {
            echo "<p style='color: red;'>❌ $field 字段缺失</p>";
        }
    }
    
    // 3. 测试插入数据
    echo "<h3>3. 测试插入数据：</h3>";
    
    $testData = [
        'order_no' => 'TEST_' . date('YmdHis') . rand(10, 99),
        'money' => 100.50,
        'mid' => 81, // 使用调试信息中的用户ID
        'type' => 1,
        'status' => 0,
        'create_time' => time(),
        'update_time' => time()
    ];
    
    // 检查是否有version字段
    if (in_array('version', $existingFields)) {
        $testData['version'] = 0;
        echo "<p>✅ 包含version字段</p>";
    }
    
    echo "<p>测试数据：</p>";
    echo "<pre>" . json_encode($testData, JSON_PRETTY_PRINT) . "</pre>";
    
    // 构建SQL
    $fields_str = implode(', ', array_keys($testData));
    $placeholders = ':' . implode(', :', array_keys($testData));
    $sql = "INSERT INTO recharge ($fields_str) VALUES ($placeholders)";
    
    echo "<p>SQL语句：</p>";
    echo "<pre>$sql</pre>";
    
    try {
        $stmt = $pdo->prepare($sql);
        $result = $stmt->execute($testData);
        
        if ($result) {
            $insertId = $pdo->lastInsertId();
            echo "<p style='color: green;'>✅ 数据插入成功，ID: $insertId</p>";
            
            // 查询刚插入的数据
            $stmt = $pdo->prepare("SELECT * FROM recharge WHERE id = ?");
            $stmt->execute([$insertId]);
            $insertedData = $stmt->fetch(PDO::FETCH_ASSOC);
            
            echo "<p>插入的数据：</p>";
            echo "<pre>" . json_encode($insertedData, JSON_PRETTY_PRINT) . "</pre>";
            
            // 清理测试数据
            $stmt = $pdo->prepare("DELETE FROM recharge WHERE id = ?");
            $stmt->execute([$insertId]);
            echo "<p style='color: blue;'>🧹 测试数据已清理</p>";
            
        } else {
            echo "<p style='color: red;'>❌ 数据插入失败</p>";
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ 插入数据时出错：" . $e->getMessage() . "</p>";
    }
    
    // 4. 检查用户是否存在
    echo "<h3>4. 检查用户信息：</h3>";
    $stmt = $pdo->prepare("SELECT id, username FROM member WHERE id = ?");
    $stmt->execute([81]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($user) {
        echo "<p style='color: green;'>✅ 用户存在：ID={$user['id']}, 用户名={$user['username']}</p>";
    } else {
        echo "<p style='color: red;'>❌ 用户不存在（ID=81）</p>";
    }
    
    // 5. 检查最近的充值记录
    echo "<h3>5. 最近的充值记录：</h3>";
    $stmt = $pdo->query("SELECT * FROM recharge ORDER BY create_time DESC LIMIT 5");
    $recentRecords = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if ($recentRecords) {
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>ID</th><th>订单号</th><th>金额</th><th>用户ID</th><th>状态</th><th>创建时间</th></tr>";
        foreach ($recentRecords as $record) {
            echo "<tr>";
            echo "<td>{$record['id']}</td>";
            echo "<td>{$record['order_no']}</td>";
            echo "<td>{$record['money']}</td>";
            echo "<td>{$record['mid']}</td>";
            echo "<td>{$record['status']}</td>";
            echo "<td>" . date('Y-m-d H:i:s', $record['create_time']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>暂无充值记录</p>";
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ 数据库连接失败：" . $e->getMessage() . "</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ 发生错误：" . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<p><strong>注意：</strong>使用完毕后请删除此调试文件。</p>";
?>
