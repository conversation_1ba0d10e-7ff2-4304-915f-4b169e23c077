<?php
// 调试文件访问问题
header('Content-Type: text/html; charset=utf-8');

echo "<h2>文件访问调试工具</h2>";

// 要检查的文件路径
$testFile = '209bea2e6415aa6fe4c8125d1a916d94.jpg';
$relativePath = '/lottery/banner/20250629/' . $testFile;
$fullPath = $_SERVER['DOCUMENT_ROOT'] . $relativePath;

echo "<h3>基本信息</h3>";
echo "<p><strong>测试文件:</strong> {$testFile}</p>";
echo "<p><strong>相对路径:</strong> {$relativePath}</p>";
echo "<p><strong>完整路径:</strong> {$fullPath}</p>";
echo "<p><strong>文档根目录:</strong> " . $_SERVER['DOCUMENT_ROOT'] . "</p>";
echo "<p><strong>当前脚本目录:</strong> " . __DIR__ . "</p>";

echo "<h3>文件检查</h3>";
echo "<p><strong>文件是否存在:</strong> " . (file_exists($fullPath) ? '是' : '否') . "</p>";

if (file_exists($fullPath)) {
    echo "<p><strong>文件大小:</strong> " . filesize($fullPath) . " 字节</p>";
    echo "<p><strong>文件权限:</strong> " . substr(sprintf('%o', fileperms($fullPath)), -4) . "</p>";
    echo "<p><strong>文件所有者:</strong> " . fileowner($fullPath) . "</p>";
    echo "<p><strong>文件组:</strong> " . filegroup($fullPath) . "</p>";
    echo "<p><strong>是否可读:</strong> " . (is_readable($fullPath) ? '是' : '否') . "</p>";
    echo "<p><strong>MIME类型:</strong> " . mime_content_type($fullPath) . "</p>";
    
    // 尝试显示图片
    echo "<h3>图片预览</h3>";
    echo "<img src='{$relativePath}' style='max-width: 300px; border: 1px solid #ccc;' onerror=\"this.style.display='none'; this.nextSibling.style.display='block';\" />";
    echo "<p style='display:none; color:red;'>图片加载失败 - Web服务器无法访问</p>";
    
    // 提供直接下载链接
    echo "<p><a href='{$relativePath}' target='_blank'>直接访问图片</a></p>";
    
    // 尝试通过PHP读取并输出图片
    echo "<h3>通过PHP读取图片</h3>";
    echo "<a href='debug_file_access.php?show_image=1&file=" . urlencode($testFile) . "' target='_blank'>通过PHP显示图片</a>";
    
} else {
    echo "<p style='color: red;'>文件不存在！</p>";
    
    // 检查目录是否存在
    $dirPath = dirname($fullPath);
    echo "<p><strong>目录路径:</strong> {$dirPath}</p>";
    echo "<p><strong>目录是否存在:</strong> " . (is_dir($dirPath) ? '是' : '否') . "</p>";
    
    if (is_dir($dirPath)) {
        echo "<p><strong>目录权限:</strong> " . substr(sprintf('%o', fileperms($dirPath)), -4) . "</p>";
        echo "<h4>目录中的文件:</h4>";
        $files = scandir($dirPath);
        foreach ($files as $file) {
            if ($file != '.' && $file != '..') {
                echo "<p>- {$file}</p>";
            }
        }
    }
}

// 检查Web服务器配置
echo "<h3>Web服务器信息</h3>";
echo "<p><strong>服务器软件:</strong> " . $_SERVER['SERVER_SOFTWARE'] . "</p>";
echo "<p><strong>PHP版本:</strong> " . PHP_VERSION . "</p>";
echo "<p><strong>当前用户:</strong> " . get_current_user() . "</p>";

// 检查.htaccess文件
$htaccessPath = __DIR__ . '/.htaccess';
echo "<h3>.htaccess检查</h3>";
echo "<p><strong>.htaccess文件是否存在:</strong> " . (file_exists($htaccessPath) ? '是' : '否') . "</p>";
if (file_exists($htaccessPath)) {
    echo "<p><strong>.htaccess内容:</strong></p>";
    echo "<pre>" . htmlspecialchars(file_get_contents($htaccessPath)) . "</pre>";
}

// 如果请求显示图片
if (isset($_GET['show_image']) && isset($_GET['file'])) {
    $imageFile = $_GET['file'];
    $imagePath = $_SERVER['DOCUMENT_ROOT'] . '/lottery/banner/20250629/' . $imageFile;
    
    if (file_exists($imagePath)) {
        $mimeType = mime_content_type($imagePath);
        header('Content-Type: ' . $mimeType);
        header('Content-Length: ' . filesize($imagePath));
        readfile($imagePath);
        exit;
    } else {
        header('HTTP/1.0 404 Not Found');
        echo "文件不存在";
        exit;
    }
}

// 测试其他已知存在的图片
echo "<h3>测试其他图片</h3>";
$testDir = $_SERVER['DOCUMENT_ROOT'] . '/lottery/banner/20250627';
if (is_dir($testDir)) {
    $files = scandir($testDir);
    $count = 0;
    foreach ($files as $file) {
        if ($file != '.' && $file != '..' && $count < 3) {
            $testPath = '/lottery/banner/20250627/' . $file;
            echo "<p>测试文件: <a href='{$testPath}' target='_blank'>{$testPath}</a></p>";
            $count++;
        }
    }
}
?>
