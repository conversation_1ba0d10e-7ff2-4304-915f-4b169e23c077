<?php
// 简单的上传测试脚本，绕过ThinkPHP
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['code' => 405, 'msg' => '只允许POST请求']);
    exit;
}

if (!isset($_FILES['file']) || $_FILES['file']['error'] !== UPLOAD_ERR_OK) {
    echo json_encode(['code' => 400, 'msg' => '文件上传失败']);
    exit;
}

$file = $_FILES['file'];

// 检查文件类型
$allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
if (!in_array($file['type'], $allowedTypes)) {
    echo json_encode(['code' => 400, 'msg' => '不支持的文件类型']);
    exit;
}

// 生成保存路径
$uploadDir = __DIR__ . '/base/ico/' . date('Y/m/d') . '/';
if (!is_dir($uploadDir)) {
    mkdir($uploadDir, 0755, true);
}

// 生成文件名
$extension = pathinfo($file['name'], PATHINFO_EXTENSION);
$filename = date('Ymd') . '/' . uniqid() . '.' . $extension;
$fullPath = $uploadDir . basename($filename);

// 移动文件
if (move_uploaded_file($file['tmp_name'], $fullPath)) {
    // 生成URL路径
    $urlPath = '/base/ico/' . date('Y/m/d') . '/' . basename($filename);
    
    echo json_encode([
        'code' => 200,
        'msg' => '上传成功',
        'data' => $urlPath
    ]);
} else {
    echo json_encode(['code' => 500, 'msg' => '文件保存失败']);
}
?>
