(function(t){function e(e){for(var n,o,r=e[0],l=e[1],c=e[2],u=0,h=[];u<r.length;u++)o=r[u],Object.prototype.hasOwnProperty.call(i,o)&&i[o]&&h.push(i[o][0]),i[o]=0;for(n in l)Object.prototype.hasOwnProperty.call(l,n)&&(t[n]=l[n]);d&&d(e);while(h.length)h.shift()();return s.push.apply(s,c||[]),a()}function a(){for(var t,e=0;e<s.length;e++){for(var a=s[e],n=!0,r=1;r<a.length;r++){var l=a[r];0!==i[l]&&(n=!1)}n&&(s.splice(e--,1),t=o(o.s=a[0]))}return t}var n={},i={app:0},s=[];function o(e){if(n[e])return n[e].exports;var a=n[e]={i:e,l:!1,exports:{}};return t[e].call(a.exports,a,a.exports,o),a.l=!0,a.exports}o.m=t,o.c=n,o.d=function(t,e,a){o.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:a})},o.r=function(t){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},o.t=function(t,e){if(1&e&&(t=o(t)),8&e)return t;if(4&e&&"object"===typeof t&&t&&t.__esModule)return t;var a=Object.create(null);if(o.r(a),Object.defineProperty(a,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var n in t)o.d(a,n,function(e){return t[e]}.bind(null,n));return a},o.n=function(t){var e=t&&t.__esModule?function(){return t["default"]}:function(){return t};return o.d(e,"a",e),e},o.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},o.p="/";var r=window["webpackJsonp"]=window["webpackJsonp"]||[],l=r.push.bind(r);r.push=e,r=r.slice();for(var c=0;c<r.length;c++)e(r[c]);var d=l;s.push([0,"chunk-vendors"]),a()})({0:function(t,e,a){t.exports=a("56d7")},"035d":function(t,e,a){"use strict";a("577f")},"06cb":function(t,e,a){"use strict";a("f873")},"0f8b":function(t,e,a){},1:function(t,e){},1357:function(t,e,a){},1476:function(t,e,a){},"1ede":function(t,e,a){},2:function(t,e){},"24ab":function(t,e,a){t.exports=a.p+"img/es_spa.b8c75e82.png"},"29ad":function(t,e,a){"use strict";a("74f4")},3:function(t,e){},3169:function(t,e,a){"use strict";a("7165")},3455:function(t,e,a){"use strict";a("7257")},"377e":function(t,e,a){},"390f":function(t,e,a){"use strict";a("1ede")},3919:function(t,e,a){"use strict";a("c8ca")},"3b7d":function(t,e,a){},"3ef5":function(t,e,a){"use strict";a("8c36")},4:function(t,e){},4462:function(t,e,a){"use strict";a("d1fe")},"45f5":function(t,e,a){"use strict";a("f30b")},4804:function(t,e,a){},"48a6":function(t,e,a){},"4ddd":function(t){t.exports=JSON.parse('{"auth":{"login":"Log In","username_place":"Silahkan Masukkan Nama Pengguna","pwd_place":"Silahkan Masukkan Kata Sandi","forgetpwd":"Lupa Kata Sandi","no_account":"Belum punya akun? Daftar sekarang","register":"Sign In","invite_code_place":"Kode Undangan","agreement_place":"saya telah mengetahui dan menyetujui persyaratan peembuatan akun","agreement":"Silakan periksa perjanjian pembukaan rekening di bawah ini！"},"recharge":{"recharge":"isi ulang","curr_balance":"saldo saat ini","input_money":"Silakan masukkan jumlah isi ulang","pay_way":"Silakan pilih cara pembayaran"},"foorter":{"index":"halaman Depan","subscribe":"simpanan","video":"video","my":"profil saya"},"my":{"title":"Hiburan Badger Madu","recharge":"isi ulang","withdraw":"menarik uang","my_balance":"dompet saya","detail":"Detail","balance":"keseimbangan","my_statement":"laporan pribadi","account_detail":"Detail akun","task_record":"Catatan tugas","personal_center":"Pusat pribadi","information_announcement":"pengumuman","online_service":"layanan online","finish_task":"Silakan lengkapi daftar tugas sebelum masuk","contact":"Menghubung","service_time":"Layanan khusus untuk Anda 7*24 jam sehari","sys_notice":"pemberitahuan sistem"},"index":{"task":"Tugas yang direkomendasikan","more":"detail","hot":"Peringkat popularitas","recmonmand":"Rekomendasi populer","all":"semua","loading":"Mendapatkan..."},"reservation":{"hall":"Aula kegiatan","refresh":"Penyegaran berhasil","money_err":"Salah jumlah","choose_num":"Silakan pilih nomor!","balance_enough":"Jika saldo tidak mencukupi, silakan menghubungi layanan pelanggan untuk mengisi ulang.！","contact_admin":"Silakan hubungi administrator untuk menerima tugas ini!","prize_succ":"Pengundian lotere berhasil, nomor terbitan:","task_list":"daftar misi","available_balance":"Saldo Tersedia","counselor":"Silakan menghubungi penasihat atau resepsionis","clear_order":"Hapus pesanan","submit":"kirim","num":"Masalah","win_num":"Angka pemenang","curr_choose":"Pemilihan nomor saat ini","per_price":"Jumlah per taruhan","price_place":"Silakan masukkan jumlahnya","unit":"Rp","total":"total","note":"Catatan","money":"Jumlah taruhan","order_time":"waktu pemesanan","settle_time":"waktu penyelesaian","no_choose":"Tidak dipilih","big":"besar","small":"Kecil","double":"pasangan","single":"satu","win_formula":"Rumus perhitungan keuntungan: jumlah kemenangan - jumlah tugas"},"concubine":{"concubine":"Kota","city":"daftar kota","city_tip":"Platform ini memiliki periferal paling otentik + pendamping bisnis + semangat lokal di seluruh jaringan.Untuk memastikan privasi pribadi setiap pengguna, pelanggan hanya dapat bergabung melalui rekomendasi nama asli dengan menghubungi resepsionis atau anggota senior platform.","price":"panduan","pri_resource":"Sumber daya apa yang tersedia?","pri_obj":"Selebriti internet, model, pramugari, model muda, mahasiswa, Anda tidak dapat membayangkannya, tetapi itu tidak akan mungkin terjadi tanpa platform ini","pri_service":"Area Pelayanan?","pric_service_one":"Seks bebas di kota yang sama, melalui udara di lokasi mana pun di seluruh negeri, tersedia secara lokal di kota-kota lapis pertama dan kedua di Tiongkok, dan di kota-kota lapis ketiga, Anda juga dapat membuat janji dengan menghubungi resepsionis.","pric_service_two":"Platform ini memiliki periferal paling otentik + pendamping bisnis + semangat lokal di seluruh jaringan.Untuk memastikan privasi pribadi setiap pengguna, pelanggan hanya dapat bergabung melalui rekomendasi nama asli dengan menghubungi resepsionis atau anggota senior platform."},"withdraw":{"with_center":"Pusat penarikan","with_record":"Catatan penarikan","with_money":"Jumlah penarikan","recharge_money":"Jumlah isi ulang","task_money":"Jumlah tugas","win_money":"Jumlah kemenangan","single_limit":"Batas transaksi tunggal","low":"terendah","heigh":"Paling tinggi","with_num":"Jumlah penarikan: Maksimum penarikan dalam satu hari","number":"Kelas dua","with_tip":"Waktu kedatangan: Waktu kedatangan umum adalah sekitar 5 menit, dan waktu kedatangan tercepat adalah 2 menit.","limit_desc":"Batasi deskripsi","immediately_withdraw":"Tarik uang segera","empty_data":"Datanya kosong","money":"Jumlah","desc":"menjelaskan","submit_time":"Waktu penyerahan","check_time":"waktu peninjauan","with_service":"Kata sandi penarikan telah ditetapkan, silakan hubungi layanan pelanggan jika Anda perlu mengubahnya."},"video":{"video":"kumpulan video","play":"bermain","no_more":"tidak lagi","num_play":"diputar","account_out":"Akun offline","buy":"Silakan isi ulang untuk menonton video"},"setting":{"setting":"mempersiapkan","base_setting":"Informasi dasar","login_pwd":"kata sandi masuk","finance_pwd":"Kata sandi dana","language":"pilihan bahasa","logout":"keluar","avatar":"avatar","choose_avatar":"Pilih Avatar","ok":"Tentu","cancel":"Membatalkan","real_name":"nama sebenarnya","modify_real_name":"Ganti nama asli","name_place":"silakan masukkan nama asli Anda","save":"menyimpan","name":"Nama","name_tip":"Demi keamanan akun Anda, nama asli Anda harus sesuai dengan nama pada kartu bank yang terikat padanya.","repect":"Tolong jangan ulangi pengaturan!","sex":"jenis kelamin","sex_place":"modifikasi gender","man":"pria","female":"perempuan","unkown":"tidak dikenal","no_setting":"tidak diatur","y_setting":"Telah di atur","bindinged":"Melompat","no":"tidak ada","login_pwd_tip":"Ubah kata sandi masuk","old_pwd":"password lama","new_pwd":"kata sandi baru","old_pwd_tip":"Silakan masukkan kata sandi lama Anda","new_pwd_tip":"Silakan masukkan kata sandi baru Anda","new_again_tip":"Silakan masukkan kata sandi baru Anda lagi","prefect":"Kata sandi penarikan berhasil disetel","pwd_error":"Kedua input kata sandi tidak konsisten","set_money_pwd":"Tetapkan kata sandi dana","money_place":"Silakan masukkan kata sandi dana","money_again_place":"Silakan masukkan kata sandi dana lagi","correct_money":"Silakan isi jumlah yang benar","contact_recharge":"Silakan hubungi layanan pelanggan untuk mengisi ulang","set_bank":"Silakan siapkan kartu pembayaran","forbid":"Fitur dinonaktifkan","log_reg":"masuk Daftar","more_service":"Masuk untuk menikmati layanan lebih lanjut！","bind_bank_info":"informasi akun bank","bank_info":"Informasi kartu bank","add_bank":"Tambahkan kartu bank","bind_bank_tip":"Tip: Harap ikat ke bank komersial besar. Jika Anda perlu memodifikasinya, silakan hubungi layanan pelanggan online.","fill_bank":"Isi kartu pembayaran","fill_bank_tip":"Silakan masukkan informasi kartu pembayaran Anda","band_account":"Nomor kartu bank","set_name_bank":"Silakan tentukan nama Anda sebelum mengikat kartu bank！","set_pwd_bank":"Silakan atur kata sandi penarikan sebelum mengikat kartu bank！","band_name":"nama Bank","band_account_tip":"Silakan masukkan nomor kartu bank Anda yang sebenarnya","band_name_tip":"Silakan pilih bank","bank_warn":"Pengguna yang terhormat, untuk menjamin keamanan dana Anda, harap ikat nama asli Anda dan tetapkan kata sandi penarikan. Jika nama Anda tidak sesuai dengan nama pembukaan rekening, Anda tidak akan dapat menarik uang.","bank_ok":"Konfirmasikan pengikatan kartu","username":"Nama","username_place":"silakan masukkan nama asli Anda","mobile":"Nomor telepon","mobile_place":"Silakan ketikkan nomor telepon Anda"}}')},5021:function(t,e,a){},"550c":function(t,e,a){"use strict";a("8d50")},"56d7":function(t,e,a){"use strict";a.r(e);var n=a("2b0e"),i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{attrs:{id:"app"}},[a("router-view"),a("Footer")],1)},s=[],o=function(){var t=this,e=t.$createElement,a=t._self._c||e;return t.show?a("van-tabbar",{attrs:{"active-color":"#7e5678",border:!0,"inactive-color":"#979799"},model:{value:t.active,callback:function(e){t.active=e},expression:"active"}},[a("van-tabbar-item",{attrs:{to:"/Home"},scopedSlots:t._u([{key:"icon",fn:function(e){return[a("img",{directives:[{name:"show",rawName:"v-show",value:0!==t.show,expression:"show !== 0"}],attrs:{src:e.active?"/img/footer/indexed.jpg":"/img/footer/index.jpg",alt:t.$t("foorter.index")}}),a("img",{directives:[{name:"show",rawName:"v-show",value:0===t.show,expression:"show === 0"}],class:t.$t("foorter.index"),staticStyle:{height:"4rem"},attrs:{src:e.active?"/img/footer/indexed.jpg":"/img/footer/index.jpg",alt:t.$t("foorter.index")}})]}}],null,!1,2500365802)},[a("span",[t._v(t._s(t.$t("foorter.index")))])]),a("van-tabbar-item",{attrs:{to:"/Game"},scopedSlots:t._u([{key:"icon",fn:function(e){return[a("img",{directives:[{name:"show",rawName:"v-show",value:1!==t.show,expression:"show !== 1"}],attrs:{src:e.active?"/img/footer/subscribed.jpg":"/img/footer/subscribe.jpg",alt:t.$t("foorter.subscribe")}}),a("img",{directives:[{name:"show",rawName:"v-show",value:1===t.show,expression:"show === 1"}],class:t.$t("foorter.subscribe"),staticStyle:{height:"4rem"},attrs:{src:e.active?"/img/footer/subscribed.jpg":"/img/footer/subscribe.jpg",alt:t.$t("foorter.subscribe")}})]}}],null,!1,3360176764)},[a("span",[t._v(t._s(t.$t("foorter.subscribe")))])]),a("van-tabbar-item",{attrs:{to:"/Choose"},scopedSlots:t._u([{key:"icon",fn:function(t){return[a("img",{staticClass:"tui",attrs:{src:(t.active,"/img/footer/beauty.52660ad1.png")}})]}}],null,!1,242860205)},[a("span")]),a("van-tabbar-item",{attrs:{to:"/Video"},scopedSlots:t._u([{key:"icon",fn:function(e){return[a("img",{directives:[{name:"show",rawName:"v-show",value:3!==t.show,expression:"show !== 3"}],attrs:{src:e.active?"/img/footer/videoed.jpg":"/img/footer/video.jpg",alt:t.$t("foorter.video")}}),a("img",{directives:[{name:"show",rawName:"v-show",value:3===t.show,expression:"show === 3"}],class:t.$t("foorter.video"),staticStyle:{height:"4rem"},attrs:{src:e.active?"/img/footer/videoed.jpg":"/img/footer/video.jpg",alt:t.$t("foorter.video")}})]}}],null,!1,2099147333)},[a("span",[t._v(t._s(t.$t("foorter.video")))])]),a("van-tabbar-item",{attrs:{to:"/Mine"},scopedSlots:t._u([{key:"icon",fn:function(e){return[a("img",{directives:[{name:"show",rawName:"v-show",value:4!==t.show,expression:"show !== 4"}],attrs:{src:e.active?"/img/footer/myed.jpg":"/img/footer/my.jpg",alt:t.$t("foorter.my")}}),a("img",{directives:[{name:"show",rawName:"v-show",value:4===t.show,expression:"show === 4"}],class:t.$t("foorter.my"),staticStyle:{height:"4rem"},attrs:{src:e.active?"/img/footer/myed.jpg":"/img/footer/my.jpg",alt:t.$t("foorter.my")}})]}}],null,!1,882088928)},[a("span",[t._v(t._s(t.$t("foorter.my")))])])],1):t._e()},r=[],l={data(){return{show:!1,active:0}},methods:{},watch:{$route(t){"home"==t.name?(this.active=0,this.show=!0):"game"==t.name?(this.active=1,this.show=!0):"choose"==t.name?(this.active=2,this.show=!0):"video"==t.name?(this.active=3,this.show=!0):"mine"==t.name?(this.active=4,this.show=!0):this.show=!1}},created(){"home"==this.$route.name?(this.active=0,this.show=!0):"game"==this.$route.name?(this.active=1,this.show=!0):"choose"==this.$route.name?(this.active=2,this.show=!0):"video"==this.$route.name?(this.active=3,this.show=!0):"mine"==this.$route.name?(this.active=4,this.show=!0):this.show=!1}},c=l,d=(a("b4cb"),a("2877")),u=Object(d["a"])(c,o,r,!1,null,"70a51920",null),h=u.exports,m={name:"app",components:{Footer:h},data(){return{status:0}},methods:{getBaseInfo(){this.$http({method:"get",url:"base_info"}).then(t=>{localStorage.getItem("token")||this.$router.push({path:"/Login"}),this.$store.commit("setBaseInfoValue",t.data)})}},created(){this.getBaseInfo()}},p=m,g=(a("3169"),Object(d["a"])(p,i,s,!1,null,null,null)),_=g.exports,v=a("b970"),f=(a("157a"),a("8c4f")),b=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"home-container"},[a("div",{staticClass:"linear-bg"}),a("div",{staticClass:"home-scroll"},[a("div",{staticClass:"banner"},[a("swiper",{staticClass:"banner_swiper",attrs:{options:t.bannerSwiperOption}},t._l(t.banners,(function(e,n){return a("swiper-slide",{key:n},[a("van-image",{staticClass:"banner_img",attrs:{round:"",src:e.url},scopedSlots:t._u([{key:"loading",fn:function(){return[a("van-loading",{attrs:{type:"circular"}})]},proxy:!0}],null,!0)})],1)})),1)],1),a("div",{staticClass:"notice-bar"},[a("van-notice-bar",{staticClass:"notice-swipe",attrs:{"left-icon":"bullhorn-o",background:"#ffffff",color:"#f487e0",text:this.notice}}),a("div",{staticClass:"linear-gradient"})],1),a("div",{staticClass:"hot-game"},[a("div",{staticClass:"hot-title-div"},[a("div",[a("span",[t._v(t._s(t.$t("index.task")))])]),a("div",{on:{click:function(e){return t.gotoMenu("/Game")}}},[a("span",[t._v(t._s(t.$t("index.more")))]),a("van-icon",{attrs:{name:"arrow",color:"#979799"}})],1)]),a("div",{staticClass:"hot-items-div"},[a("van-grid",{attrs:{border:!1,"column-num":4,"icon-size":20}},t._l(t.gameitem,(function(e,n){return a("van-grid-item",{key:n,on:{click:function(a){return t.toLottery(e.key,e.id)}}},[a("van-image",{staticClass:"game_item_img",attrs:{src:e.ico},scopedSlots:t._u([{key:"loading",fn:function(){return[a("van-loading",{attrs:{type:"circular"}})]},proxy:!0}],null,!0)}),a("span",[t._v(t._s(e.name))])],1)})),1)],1)]),a("van-pull-refresh",{attrs:{"pulling-text":"Sao chép nhanh quy trình thả xuống","loosing-text":"Sedang Merefresh","loading-text":"Proses pemuatan cepat","success-text":"Proses pemuatan berhasil"},on:{refresh:t.onRefresh},model:{value:t.isLoading,callback:function(e){t.isLoading=e},expression:"isLoading"}},[a("div",{staticClass:"hot-recommend"},[a("div",{staticClass:"hot-title-div"},[a("div",[a("span",[t._v(t._s(t.$t("index.hot")))])]),a("div",[a("span",{on:{click:function(e){return t.gotoMenu("/Video")}}},[t._v(t._s(t.$t("index.more")))]),a("van-icon",{attrs:{name:"arrow",color:"#979799"}})],1)]),a("div",{staticClass:"movie_list_0"},[a("swiper",{staticClass:"movie_swiper",attrs:{options:t.movielistSwiperOption}},t._l(t.movielist_0,(function(e,n){return a("swiper-slide",{key:n},[a("van-image",{staticClass:"movie_cover",attrs:{round:"",src:e.cover},on:{click:function(a){return t.toPlayVideo(e.id)}},scopedSlots:t._u([{key:"loading",fn:function(){return[a("van-loading",{attrs:{type:"circular"}})]},proxy:!0}],null,!0)}),0===n?a("img",{staticClass:"hot",attrs:{src:"/img/home/<USER>"}}):t._e(),1===n?a("img",{staticClass:"hot",attrs:{src:"/img/home/<USER>"}}):t._e(),2===n?a("img",{staticClass:"hot",attrs:{src:"/img/home/<USER>"}}):t._e(),a("div",{staticClass:"movie-list-item-bottom"},[a("div",{staticClass:"movie-time-div"},[a("span",[t._v(t._s(e.title))]),a("div",{staticClass:"van-count-down"},[t._v(t._s(e.time))])])])],1)})),1)],1),a("div",{staticClass:"hot-title-div"},[a("div",[a("span",[t._v(t._s(t.$t("index.more")))])]),a("div",{on:{click:function(e){return t.gotoMenu("/Video")}}},[a("span",[t._v(t._s(t.$t("index.recmonmand")))]),a("van-icon",{attrs:{name:"arrow",size:"25",color:"#979799"}})],1)]),a("div",{staticClass:"movie_list_1"},[t._l(t.movielist_1,(function(e,n){return a("div",{key:n,staticClass:"movie-list-item",on:{click:function(a){return t.toPlayVideo(e.id)}}},[a("van-image",{staticClass:"cover_img",attrs:{round:"",src:e.cover},scopedSlots:t._u([{key:"loading",fn:function(){return[a("van-loading",{attrs:{type:"circular"}})]},proxy:!0}],null,!0)}),a("div",{staticClass:"movie-list-item-bottom"},[a("div",{staticClass:"movie-time-div"},[a("span",[t._v(t._s(e.title))]),a("span",[t._v(t._s(t.$t("video.play"))+":"+t._s(e.count))])])])],1)})),a("div",{staticClass:"hot-recommend-more",on:{click:function(e){return t.gotoMenu("/Video")}}},[t._v(t._s(t.$t("index.more")))])],2)])])],1)])},k=[],y={data(){return{notice:this.$t("index.loading"),banners:[{}],basicData:[],gameitem:[{},{},{},{}],movielist_0:[{},{},{},{}],movielist_1:[{},{},{},{},{},{},{},{}],isLoading:!1,movielistSwiperOption:{slidesPerView:"auto",spaceBetween:0,slidesPerGroup:1},bannerSwiperOption:{effect:"coverflow",grabCursor:!0,centeredSlides:!0,slidesPerView:"auto",speed:800,autoplay:!0,coverflowEffect:{rotate:50,stretch:10,depth:100,modifier:1,slideShadows:!0}}}},methods:{gotoMenu(t){this.$router.replace(t)},toLottery(t,e){localStorage.getItem("token")?this.$router.push({path:"/Lottery?key="+t+"&id="+e}):this.$router.push({path:"/Login"})},toPlayVideo(t){localStorage.getItem("token")?this.$router.push({path:"/PlayVideo?id="+t}):this.$router.push({path:"/Login"})},onRefresh(){setTimeout(()=>{this.getBasicConfig(),this.isLoading=!1,this.$toast("Penyegaran berhasil")},500)},getBasicConfig(){this.$http({method:"get",url:"sys_config"}).then(t=>{this.basicData=t.data,console.info(t),this.getNotice(this.basicData),this.getBanner(this.basicData),this.getGameItem(),this.getMovieList_0(this.basicData),this.getMovieList_1(this.basicData)})},getNotice(t){this.notice=t.notice},getGameItem(){this.$http({method:"get",url:"lottery_hot"}).then(t=>{this.gameitem=t.data})},getMovieList_0(t){this.movielist_0=t.movielist_0},getMovieList_1(t){this.movielist_1=t.movielist_1},getBanner(t){this.banners=t.banners}},mounted(){},created(){this.getBasicConfig()}},w=y,$=(a("89f6"),Object(d["a"])(w,b,k,!1,null,"da78b76e",null)),C=$.exports,x=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"mine page"},[a("div",{staticClass:"page-bg"}),a("div",{staticClass:"wrapper"},[a("van-pull-refresh",{attrs:{"pulling-text":"Sao chép nhanh quy trình thả xuống","loosing-text":"Sedang Merefresh","loading-text":"Proses pemuatan cepat","success-text":"Proses pemuatan berhasil"},on:{refresh:t.onRefresh},model:{value:t.isLoading,callback:function(e){t.isLoading=e},expression:"isLoading"}},[a("div",{staticClass:"header"},[a("van-nav-bar",{staticClass:"nav-bar",scopedSlots:t._u([{key:"right",fn:function(){return[a("van-icon",{attrs:{name:"setting-o",color:"#fff"},on:{click:function(e){return t.showSetting()}}})]},proxy:!0}])}),a("div",{staticClass:"user-wrapper",on:{click:function(e){return t.doLogin()}}},[a("div",{staticClass:"login-content"},[a("p",{staticClass:"login-btn"},[t._v(t._s(this.userInfo.username)+" "),a("img",{staticStyle:{width:"20px",height:"20px",margin:"0px 5px"},attrs:{src:"img/vip.png"}}),a("span",{staticStyle:{"font-weight":"bold"}},[t._v(t._s(this.userInfo.vip))])]),a("p",{staticClass:"login-label"},[t._v(t._s(this.userInfo.ip))])])])],1),a("div",{staticClass:"content"},[a("div",{staticClass:"finance"},[a("div",{staticClass:"finance-item",on:{click:function(e){return t.toService()}}},[a("van-icon",{staticClass:"icon",attrs:{name:"peer-pay"}}),a("span",{staticClass:"text"},[t._v(t._s(t.$t("my.recharge")))])],1),a("div",{staticClass:"line"}),a("div",{staticClass:"finance-item",on:{click:function(e){return t.doWithdrawal()}}},[a("van-icon",{staticClass:"icon",attrs:{name:"idcard"}}),a("span",{staticClass:"text"},[t._v(t._s(t.$t("my.withdraw")))])],1)]),this.userInfo.money?a("div",{staticClass:"wallet"},[a("div",{staticClass:"part-1 van-hairline--bottom"},[a("p",{staticClass:"flex-1 font-28 font-primary-color"},[t._v(t._s(t.$t("my.my_balance")))]),a("span",{staticClass:"font-28 font-gray"},[t._v(t._s(t.$t("my.detail")))]),a("van-icon",{staticClass:"font-gray",staticStyle:{"font-size":"28px"},attrs:{name:"arrow"}})],1),a("div",{staticClass:"part-2"},[a("p",{staticClass:"balance van-ellipsis"},[t._v(t._s(this.userInfo.money))]),a("span",{staticClass:"font-28 font-gray"},[t._v("Saldo (Rp)")]),a("div",{staticClass:"refresh-btn",on:{click:function(e){return t.refresh()}}},[a("van-icon",{attrs:{name:"replay"}})],1)]),a("div",{staticClass:"part-2"},[a("p",{staticClass:"balance van-ellipsis"},[t._v(t._s(this.userInfo.score))]),a("span",{staticClass:"font-28 font-gray"},[t._v("Nilai kredit")]),a("div",{staticClass:"refresh-btn",on:{click:function(e){return t.refresh()}}},[a("van-icon",{attrs:{name:"replay"}})],1)])]):t._e(),a("div",{staticClass:"menu",style:{marginTop:t.menu_top+"px"}},[a("div",{staticClass:"menu-item",on:{click:function(e){return t.$router.push({path:"/Personalreport"})}}},[a("van-image",{staticClass:"menu-item-icon",attrs:{src:"img/mine/baobiao.svg"},scopedSlots:t._u([{key:"loading",fn:function(){return[a("van-loading",{attrs:{type:"spinner"}})]},proxy:!0}])}),a("span",{staticClass:"menu-item-label"},[t._v(t._s(t.$t("my.my_statement")))])],1),a("div",{staticClass:"menu-item",on:{click:function(e){return t.exit()}}},[a("van-image",{staticClass:"menu-item-icon",attrs:{src:"img/mine/mingxi.svg"},scopedSlots:t._u([{key:"loading",fn:function(){return[a("van-loading",{attrs:{type:"spinner"}})]},proxy:!0}])}),a("span",{staticClass:"menu-item-label"},[t._v(t._s(t.$t("my.account_detail")))])],1),a("div",{staticClass:"menu-item",on:{click:function(e){return t.$router.push({path:"/GameRecord"})}}},[a("van-image",{staticClass:"menu-item-icon",attrs:{src:"img/mine/youxi.svg"},scopedSlots:t._u([{key:"loading",fn:function(){return[a("van-loading",{attrs:{type:"spinner"}})]},proxy:!0}])}),a("span",{staticClass:"menu-item-label"},[t._v(t._s(t.$t("my.task_record")))])],1),a("div",{staticClass:"menu-item",on:{click:function(e){return t.$router.push({path:"/Infomation"})}}},[a("van-image",{staticClass:"menu-item-icon",attrs:{src:"img/mine/user.svg"},scopedSlots:t._u([{key:"loading",fn:function(){return[a("van-loading",{attrs:{type:"spinner"}})]},proxy:!0}])}),a("span",{staticClass:"menu-item-label"},[t._v(t._s(t.$t("my.personal_center")))])],1),a("div",{staticClass:"menu-item",on:{click:function(e){return t.toNotice()}}},[a("van-image",{staticClass:"menu-item-icon",attrs:{src:"img/mine/gonggao.svg"},scopedSlots:t._u([{key:"loading",fn:function(){return[a("van-loading",{attrs:{type:"spinner"}})]},proxy:!0}])}),a("span",{staticClass:"menu-item-label"},[t._v(t._s(t.$t("my.information_announcement")))])],1),a("div",{staticClass:"menu-item",on:{click:function(e){return t.toService()}}},[a("van-image",{staticClass:"menu-item-icon",attrs:{src:"img/mine/kefu_1.svg"},scopedSlots:t._u([{key:"loading",fn:function(){return[a("van-loading",{attrs:{type:"spinner"}})]},proxy:!0}])}),a("span",{staticClass:"menu-item-label"},[t._v(t._s(t.$t("my.online_service")))])],1)])])])],1)])},S=[],I={data(){return{userInfo:{},menu_top:40,isLoading:!1}},methods:{refresh(){this.isLoading=!0,setTimeout(()=>{this.isLoading=!1,localStorage.getItem("token")?this.$toast(this.$t("reservation.refresh")):this.$router.push({path:"/Login"})},500)},exit(){this.$toast(this.$t("my.finish_task"))},showSetting(){localStorage.getItem("token")?this.$router.push({path:"/Setting"}):this.$router.push({path:"/Login"})},toNotice(){localStorage.getItem("token")?this.$router.push({path:"/Notice"}):this.$router.push({path:"/Login"})},onRefresh(){setTimeout(()=>{this.isLoading=!1,localStorage.getItem("token")?(this.getUserInfo(),this.$toast(this.$t("reservation.refresh"))):this.$router.push({path:"/Login"})},500)},doLogin(){localStorage.getItem("token")?this.$router.push({path:"/Infomation"}):this.$router.push({path:"/Login"})},doPay(){this.$router.push({name:"Recharge",params:{balance:this.userInfo.money}})},doWithdrawal(){this.$http({method:"get",url:"user_get_bank"}).then(t=>{t.data.is_bank?this.$router.push("withdraw"):(this.$router.push("Setbank"),this.$toast.fail(this.$t("setting.set_bank")))})},toService(){1==this.$store.getters.getBaseInfo.iskefu?this.$router.push("ServiceOnline"):this.$toast.fail(this.$t("setting.forbid"))},getUserInfo(){this.$http({method:"get",url:"user_info"}).then(t=>{200===t.code?(this.userInfo=t.data,this.menu_top=15,1!==this.userInfo.status&&(this.$toast(this.$t("video.account_out")),localStorage.clear(),this.$router.push({path:"/Login"}))):401===t.code&&this.$toast(t.msg)})}},created(){localStorage.getItem("token")?this.getUserInfo():(this.userInfo.username=this.$t("setting.log_reg"),this.userInfo.ip=this.$t("setting.more_service"),this.userInfo.header_img="img/mine/avatar.png")}},L=I,P=(a("06cb"),Object(d["a"])(L,x,S,!1,null,"3ce0ac4a",null)),j=P.exports,T=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"convention-hall page"},[a("van-nav-bar",{staticClass:"nav-bar",attrs:{title:t.$t("concubine.concubine")}}),a("div",{staticClass:"convention-item"},[a("van-tabs",{attrs:{animated:"",sticky:"","line-width":"100px",swipeable:!0}},[a("van-tab",{attrs:{title:t.$t("concubine.city")}},[a("div",{staticClass:"card"},[t._v(t._s(t.$t("concubine.city_tip")))]),a("div",{staticClass:"address"},t._l(t.addlist,(function(e,n){return a("van-cell-group",{key:n},[a("van-cell",{on:{click:function(a){return t.addgo(e)}}},[t._v(t._s(e.name))])],1)})),1)]),a("van-tab",{attrs:{title:t.$t("concubine.price")}},[a("div",{staticClass:"card"},[t._v(t._s(t.$t("concubine.city_tip")))]),a("div",{staticClass:"rig-box"},[a("p",{staticClass:"rig-title"},[t._v(t._s(t.$t("concubine.pri_resource")))]),a("p",{staticClass:"rig-content"},[t._v(t._s(t.$t("concubine.pri_obj")))]),a("p",{staticClass:"rig-title"},[t._v(t._s(t.$t("concubine.pri_service")))]),a("p",{staticClass:"rig-content"},[t._v(t._s(t.$t("concubine.pric_service_one")))]),a("p",{staticClass:"rig-content"},[t._v(t._s(t.$t("concubine.pric_service_two")))])])])],1)],1)],1)},B=[],V={data(){return{addlist:[]}},methods:{addgo(t){localStorage.getItem("token")?this.$router.push({path:"/list?id="+t.id+"&name="+t.name}):this.$router.push({path:"/Login"})},getAddress(){this.$http({method:"get",url:"address_list"}).then(t=>{this.addlist=t.data,console.log(t.data)})}},created(){this.getAddress()}},N=V,R=(a("550c"),Object(d["a"])(N,T,B,!1,null,"2c7616d6",null)),M=R.exports,O=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"container page"},[a("van-nav-bar",{staticClass:"nav-bar",attrs:{title:this.vod_name},scopedSlots:t._u([{key:"left",fn:function(){return[a("van-icon",{attrs:{name:"arrow-left",color:"#fff"},on:{click:function(e){return t.back()}}})]},proxy:!0}])}),a("div",{staticClass:"right"},[a("van-pull-refresh",{staticClass:"list-wrapper",attrs:{"pulling-text":"Sao chép nhanh quy trình thả xuống","loosing-text":"Menyegarkan halaman","loading-text":"Proses pemuatan cepat","success-text":"Proses pemuatan berhasil",border:"false"},on:{refresh:t.onRefresh},model:{value:t.isLoading,callback:function(e){t.isLoading=e},expression:"isLoading"}},[a("van-grid",{attrs:{"column-num":2,gutter:10}},t._l(t.datalist,(function(e,n){return a("van-grid-item",{key:n,on:{click:function(a){return t.profile(e.id)}}},[a("van-image",{staticClass:"game_item_img",attrs:{src:e.img_url},scopedSlots:t._u([{key:"loading",fn:function(){return[a("van-loading",{attrs:{type:"circular"}})]},proxy:!0}],null,!0)}),a("span",{staticClass:"rig-name"},[t._v(t._s(e.xuanfei_name))])],1)})),1)],1)],1)],1)},U=[],D={data(){return{vod_name:"北京",isLoading:!1,datalist:[]}},methods:{back(){this.$router.push({path:"Choose"})},onRefresh(){setTimeout(()=>{this.$toast(this.$t("reservation.refresh")),this.isLoading=!1},500)},profile(t){this.$router.push({path:"/profile?id="+t+"&name="+this.vod_name+"&adsid="+this.$route.query.id})},getxuanfeilist(){this.$http({method:"get",url:"xuanfeilist",data:{id:this.$route.query.id}}).then(t=>{this.datalist=t.data})}},created(){this.vod_name=this.$route.query.name,this.getxuanfeilist()}},E=D,K=(a("5e45"),Object(d["a"])(E,O,U,!1,null,"ced6c35c",null)),A=K.exports,z=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"page"},[a("van-nav-bar",{staticClass:"nav-bar",attrs:{title:t.$t("concubine.concubine")},scopedSlots:t._u([{key:"left",fn:function(){return[a("van-icon",{attrs:{name:"arrow-left",color:"#fff"},on:{click:function(e){return t.back()}}})]},proxy:!0}])}),a("div",{staticClass:"box"},[a("p",{staticClass:"name"},[t._v(t._s(this.xuanfeidata.xuanfei_name))]),a("p",{staticClass:"title"},[t._v(t._s(this.xuanfeidata.vo_title))]),t._l(t.xuanfeidata.img_url,(function(t,e){return a("van-image",{key:e,attrs:{width:"98%",fit:"contain",height:"100%",src:t}})})),a("van-button",{staticClass:"button",attrs:{round:"true",color:"linear-gradient(to right, #7f5778 , #e5c2a0)"},on:{click:t.yuyue}},[t._v(t._s(t.$t("foorter.subscribe")))])],2)],1)},q=[],H={data(){return{xuanfeidata:[]}},methods:{back(){this.$router.push({path:"list?id="+this.$route.query.adsid+"&name="+this.$route.query.name})},getxuanfeidata(){this.$http({method:"get",url:"xuanfeidata",data:{id:this.$route.query.id}}).then(t=>{this.xuanfeidata=t.data})},yuyue(){this.$toast(this.$t("reservation.counselor"))}},created(){this.getxuanfeidata()}},J=H,W=(a("c835"),Object(d["a"])(J,z,q,!1,null,null,null)),G=W.exports,F=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"movie-hall page"},[a("van-nav-bar",{staticClass:"nav-bar",attrs:{title:t.$t("video.video")}}),a("van-tabs",{attrs:{animated:"",swipeable:""},on:{change:t.OnChange},model:{value:t.active,callback:function(e){t.active=e},expression:"active"}},t._l(t.videolitem,(function(t,e){return a("van-tab",{key:e,attrs:{title:t.name,name:t.key}})})),1),a("swiper",{ref:"swiper",staticClass:"video_swiper",attrs:{options:t.videoSwiperOption},on:{slideChange:t.itemChange}},t._l(t.videolitem,(function(e,n){return a("swiper-slide",{key:n},[a("div",{staticClass:"movie-list-tab"},[a("van-pull-refresh",{attrs:{"pulling-text":"Sao chép nhanh quy trình thả xuống","loosing-text":"Menyegarkan halaman","loading-text":"Proses pemuatan cepat","success-text":"Proses pemuatan berhasil"},on:{refresh:t.onRefresh},model:{value:t.isLoading,callback:function(e){t.isLoading=e},expression:"isLoading"}},[a("div",{staticClass:"hot-recommend-div"},[a("van-list",{attrs:{finished:t.finished,"immediate-check":!1,"finished-text":t.$t("video.no_more")},on:{load:t.onLoad},model:{value:t.loading,callback:function(e){t.loading=e},expression:"loading"}},[a("div",{staticClass:"list-item"},t._l(t.videolist,(function(e,n){return a("div",{key:n,staticClass:"movie-list-item",on:{click:function(a){return t.toPlayVideo(e.id)}}},[a("van-image",{staticClass:"cover_img",attrs:{round:"",src:e.vod_pic},scopedSlots:t._u([{key:"loading",fn:function(){return[a("van-loading",{attrs:{type:"circular"}})]},proxy:!0}],null,!0)}),a("div",{staticClass:"movie-list-item-bottom"},[a("div",{staticClass:"movie-time-div"},[a("span",[t._v(t._s(e.vod_name))]),a("span",[t._v(t._s(t.$t("video.play"))+":"+t._s(e.count))])])])],1)})),0)])],1)])],1)])})),1)],1)},X=[],Y=a("d399"),Q={data(){return{active:0,isLoading:!1,count:0,loading:!1,finished:!1,refreshing:!1,videolitem:[],videolist:[],number:0,page:0,videoSwiperOption:{slidesPerView:"auto",spaceBetween:0,slidesPerGroup:1}}},methods:{getVideoClass(){this.$http({method:"get",url:"video_class"}).then(t=>{this.videolitem=t.data})},toPlayVideo(t){localStorage.getItem("token")?this.$router.push({path:"/PlayVideo?id="+t}):this.$router.push({path:"/Login"})},itemChange(){this.active=this.$refs.swiper.swiper.activeIndex,this.OnChange()},getVideoList(){this.$http({method:"get",data:{id:this.active,page:this.page},url:"video_list"}).then(t=>{this.videolist=this.videolist.concat(t.data.data),this.count=t.data.count,this.page++})},onLoad(){this.getVideoList();let t=setTimeout(()=>{this.refreshing&&(this.videolist=[],this.refreshing=!1),this.loading=!1,this.videolist.length===this.count&&(this.finished=!0),this.finished&&clearTimeout(t)},500)},OnChange(){this.videolist=[],this.number=0,this.page=0,this.count=0,this.getVideoList()},onRefresh(){setTimeout(()=>{this.finished=!1,this.loading=!0,this.onLoad(),this.isLoading=!1,Object(Y["a"])(this.$t("reservation.refresh"))},500)}},created(){this.getVideoClass(),this.OnChange()}},Z=Q,tt=(a("64a1"),Object(d["a"])(Z,F,X,!1,null,"02c74c21",null)),et=tt.exports,at=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"convention-hall page"},[a("van-nav-bar",{staticClass:"nav-bar",attrs:{title:t.$t("reservation.hall")}}),a("div",{staticClass:"convention-item"},[a("div",{staticClass:"left"},[a("van-sidebar",{on:{change:t.onChange},model:{value:t.activeKey,callback:function(e){t.activeKey=e},expression:"activeKey"}},[a("van-sidebar-item",{attrs:{title:t.$t("index.all")}}),t._l(t.lotteryitem,(function(t,e){return a("van-sidebar-item",{key:e,attrs:{title:t.name}})}))],2)],1),a("div",{staticClass:"right"},[a("van-pull-refresh",{staticClass:"list-wrapper",attrs:{"pulling-text":"Sao chép nhanh quy trình thả xuống","loosing-text":"Sedang Merefresh","loading-text":"Đang tải bản sao nhắc quá trình","success-text":"Làm mới bản sao lời nhắc thành công",border:!1},on:{refresh:t.onRefresh},model:{value:t.isLoading,callback:function(e){t.isLoading=e},expression:"isLoading"}},[a("van-grid",{attrs:{"column-num":2}},t._l(t.gameitem,(function(e,n){return a("van-grid-item",{key:n,on:{click:function(a){return t.toLottery(e.key,e.id)}}},[a("van-image",{staticClass:"game_item_img",attrs:{src:e.ico},scopedSlots:t._u([{key:"loading",fn:function(){return[a("van-loading",{attrs:{type:"circular"}})]},proxy:!0}],null,!0)}),a("span",[t._v(t._s(e.name))]),a("span",[t._v(t._s(e.desc))])],1)})),1)],1)],1)])],1)},nt=[],it={data(){return{gameitem:[{},{},{},{}],lotteryitem:[{},{},{},{}],isLoading:!1,activeKey:0}},methods:{onRefresh(){setTimeout(()=>{Object(Y["a"])(this.$t("reservation.refresh")),this.isLoading=!1},500)},toLottery(t,e){localStorage.getItem("token")?this.$router.push({path:"/Lottery?key="+t+"&id="+e}):this.$router.push({path:"/Login"})},getGameItem(){this.$http({method:"get",url:"lottery_list"}).then(t=>{this.gameitem=t.data})},onChange(t){this.$http({method:"get",data:{class:t},url:"lottery_list"}).then(t=>{this.gameitem=t.data})},getLotteryItem(){this.$http({method:"get",url:"lottery_class"}).then(t=>{this.lotteryitem=t.data})}},created(){this.getGameItem(),this.getLotteryItem()}},st=it,ot=(a("7218"),Object(d["a"])(st,at,nt,!1,null,"e423027c",null)),rt=ot.exports,lt=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"bg-container page"},[a("img",{staticClass:"bg-img",attrs:{src:"img/login/login-bg.png"}}),a("div",{staticClass:"bg-wrapper"},[a("div",{staticClass:"login"},[a("van-nav-bar",{staticClass:"nav-bar",scopedSlots:t._u([{key:"left",fn:function(){return[a("van-icon",{attrs:{name:"arrow-left",color:"#fff"},on:{click:function(e){return t.back()}}})]},proxy:!0}])}),a("div",{staticClass:"wrapper"},[a("div",{staticClass:"logo-container"},[a("div",{staticClass:"logo-wrapper"},[a("img",{staticClass:"logo-img",attrs:{src:void 0!==this.$store.getters.getBaseInfo.ico?this.$store.getters.getBaseInfo.ico:"/img/null.png"}})])]),a("div",{staticClass:"title"},[t._v(t._s(t.$t("auth.login")))]),a("div",{staticClass:"loginForm"},[a("van-field",{staticClass:"input",attrs:{clearable:"","input-align":"center",placeholder:t.$t("auth.username_place")},model:{value:t.username,callback:function(e){t.username=e},expression:"username"}}),a("van-field",{staticClass:"input",attrs:{type:t.passwordType,"input-align":"center",placeholder:t.$t("auth.pwd_place")},model:{value:t.password,callback:function(e){t.password=e},expression:"password"}},[a("template",{slot:"right-icon"},[a("van-icon",{attrs:{name:"password"===t.passwordType?"closed-eye":"eye-o"},on:{click:t.switchPasswordType}})],1)],2),a("div",{staticClass:"reset-text"},[a("span",[t._v(t._s(t.$t("auth.forgetpwd"))+"?")])]),a("div",{staticClass:"register-text",on:{click:function(e){return t.toRegister()}}},[a("span",[t._v(t._s(t.$t("auth.no_account")))])]),a("van-button",{staticClass:"login-btn",attrs:{type:"primary",size:"normal"},on:{click:function(e){return t.doLogin()}}},[t._v(t._s(t.$t("auth.login")))])],1)])],1)])])},ct=[],dt={model:{prop:"inputValue",event:"input"},props:{inputValue:{type:String,default:""}},data(){return{username:"",lang:"en_us",password:this.inputValue,passwordType:"password"}},mounted(){this.lang=localStorage.getItem("lang")||"yn_yu"},methods:{switchPasswordType(){this.passwordType="password"===this.passwordType?"text":"password"},back(){return window.history.back()},toRegister(){this.$router.push("Register")},doLogin(){return""===this.username||null===this.username||void 0===this.username?(this.$toast(this.$t("auth.username_place")),!1):""===this.password||null===this.password||void 0===this.password?(this.$toast(this.$t("auth.pwd_place")),!1):void this.$http({url:"member_login",method:"post",data:{username:this.username,password:this.password,lang:this.lang}}).then(t=>{200===t.code?(console.info(t.msg),this.$toast.success(t.msg),localStorage.setItem("token",t.data.id),this.$router.push("Mine")):this.$toast(t.msg)})}},created(){if(localStorage.getItem("token"))return window.history.back()}},ut=dt,ht=(a("45f5"),Object(d["a"])(ut,lt,ct,!1,null,"03ca737a",null)),mt=ht.exports,pt=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"bg-container page"},[a("img",{staticClass:"bg-img",attrs:{src:"img/login/login-bg.png"}}),a("div",{staticClass:"bg-wrapper"},[a("div",{staticClass:"register"},[a("van-nav-bar",{staticClass:"nav-bar",scopedSlots:t._u([{key:"left",fn:function(){return[a("van-icon",{attrs:{name:"arrow-left",color:"#fff"},on:{click:function(e){return t.back()}}})]},proxy:!0}])}),a("div",{staticClass:"wrapper"},[a("div",{staticClass:"logo-container"},[a("div",{staticClass:"logo-wrapper"},[a("img",{staticClass:"logo-img",attrs:{src:void 0!==this.$store.getters.getBaseInfo.ico?this.$store.getters.getBaseInfo.ico:""}})])]),a("div",{staticClass:"title"},[t._v(t._s(t.$t("auth.register")))]),a("div",{staticClass:"loginForm"},[a("van-field",{staticClass:"input",attrs:{clearable:"","input-align":"center",placeholder:t.$t("auth.username_place")},model:{value:t.username,callback:function(e){t.username=e},expression:"username"}}),a("van-field",{staticClass:"input",attrs:{type:t.passwordType,"input-align":"center",placeholder:t.$t("auth.pwd_place")},model:{value:t.password,callback:function(e){t.password=e},expression:"password"}},[a("template",{slot:"right-icon"},[a("van-icon",{attrs:{name:"password"===t.passwordType?"closed-eye":"eye-o"},on:{click:t.switchPasswordType}})],1)],2),a("van-field",{staticClass:"input",attrs:{clearable:"","input-align":"center",placeholder:t.$t("auth.invite_code_place")},model:{value:t.code,callback:function(e){t.code=e},expression:"code"}}),a("div",{staticClass:"agreement"},[a("van-checkbox",{model:{value:t.checked,callback:function(e){t.checked=e},expression:"checked"}}),a("span",{staticClass:"agreement-text"},[t._v(t._s(t.$t("auth.agreement_place")))])],1),a("van-button",{staticClass:"login-btn",attrs:{type:"primary",size:"normal"},on:{click:function(e){return t.doRegister()}}},[t._v(t._s(t.$t("auth.register")))])],1)])],1)])])},gt=[],_t={model:{prop:"inputValue",event:"input"},props:{inputValue:{type:String,default:""}},data(){return{checked:!0,username:"",code:"",lang:"en_us",password:this.inputValue,passwordType:"password"}},mounted(){this.lang=localStorage.getItem("lang")||"es_spa"},methods:{switchPasswordType(){this.passwordType="password"===this.passwordType?"text":"password"},back(){return window.history.back()},doRegister(){return""===this.username||null===this.username||void 0===this.username?(this.$toast.fail(this.$t("auth.username_place")),!1):new RegExp("/^[0-9]{9}([0-9]{1})?$/").test(this.username)?(this.$toast.fail(this.$t("setting.mobile_place")),!1):""===this.password||null===this.password||void 0===this.password?(this.$toast.fail(this.$t("auth.pwd_place")),!1):""===this.code||null===this.code||void 0===this.code?(this.$toast.fail(this.$t("auth.invite_code_place")),!1):this.checked?void this.$http({method:"post",data:{username:this.username,password:this.password,code:this.code,lang:this.lang},url:"member_register"}).then(t=>{200===t.code?(this.$toast.success(t.msg),localStorage.setItem("token",t.data),this.$router.push("Mine")):this.$toast.fail(t.msg)}):(this.$toast.fail(this.$t("auth.agreement")),!1)}},created(){if(localStorage.getItem("token"))return window.history.back()}},vt=_t,ft=(a("aeb3"),Object(d["a"])(vt,pt,gt,!1,null,"80fe5460",null)),bt=ft.exports,kt=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"container page"},[a("div",{staticClass:"header"},[a("van-nav-bar",{staticClass:"nav-bar",attrs:{title:t.$t("my.online_service")},scopedSlots:t._u([{key:"left",fn:function(){return[a("van-icon",{attrs:{name:"arrow-left",color:"#fff"},on:{click:function(e){return t.back()}}})]},proxy:!0}])})],1),a("div",{staticClass:"servicelistItem"},[a("div",{staticClass:"servicelistItemTop"},[a("img",{staticClass:"servicelistItemImage",attrs:{src:"img/mine/kefu.png"}}),a("div",{staticClass:"servicelistItemText"},[t._v(" "+t._s(void 0!==this.$store.getters.getBaseInfo.name?this.$store.getters.getBaseInfo.name:this.$t("my.title"))+" ")]),a("div",{staticClass:"servicelistItemBtn",on:{click:function(e){return t.toServicePage()}}},[a("div",{staticClass:"servicelistItemBtnText"},[t._v(" "+t._s(t.$t("my.contact"))+" ")])])]),a("div",{staticClass:"servicelistItemBottom"},[a("div",{staticClass:"servicelistItemInfoText"},[t._v(" "+t._s(t.$t("my.service_time"))+" ")])])]),a("van-collapse",{model:{value:t.activeNames,callback:function(e){t.activeNames=e},expression:"activeNames"}},[a("van-collapse-item",{attrs:{title:t.config.pay_title,name:"1",icon:"/img/1.png"}},[t._v(" "+t._s(t.config.pay_desc)+" ")])],1),a("div",{staticClass:"payments"},t._l(t.items,(function(t,e){return a("img",{key:e,attrs:{src:t.thumb}})})),0)],1)},yt=[],wt={data(){return{activeNames:[1],items:[],config:{}}},created(){this.$http({method:"get",url:"banks_list"}).then(t=>{this.items=t.data}),this.$http({method:"get",url:"base_info"}).then(t=>{this.config=t.data})},methods:{back(){return window.history.back()},toServicePage(){const t=this.$store.getters.getBaseInfo;console.log(t),1==t.iskefu&&(console.log("ssss"),window.location.href=t.kefu)}}},$t=wt,Ct=(a("eae9"),Object(d["a"])($t,kt,yt,!1,null,"********",null)),xt=Ct.exports,St=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"container page"},[a("div",{staticClass:"header"},[a("van-nav-bar",{staticClass:"nav-bar",attrs:{title:t.$t("my.online_service")},scopedSlots:t._u([{key:"left",fn:function(){return[a("van-icon",{attrs:{name:"arrow-left",color:"#fff"},on:{click:function(e){return t.back()}}})]},proxy:!0}])})],1),a("div",{staticClass:"ifrmae_page"},[a("iframe",{attrs:{width:"100%",height:"100%",frameborder:"0",id:"iframe_web",src:void 0!==this.$store.getters.getBaseInfo.kefu?this.$store.getters.getBaseInfo.kefu:"https://hao.360.com/"}})])])},It=[],Lt={data(){return{}},methods:{back(){return window.history.back()}},created(){},mounted(){const t=document.getElementById("iframe_web"),e=document.documentElement.clientHeight;t.style.height=Number(e)-65+"px"}},Pt=Lt,jt=(a("3455"),Object(d["a"])(Pt,St,It,!1,null,"5e585e22",null)),Tt=jt.exports,Bt=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"container page"},[a("van-nav-bar",{staticClass:"nav-bar",attrs:{title:t.$t("setting.setting")},scopedSlots:t._u([{key:"left",fn:function(){return[a("van-icon",{attrs:{name:"arrow-left",color:"#fff"},on:{click:function(e){return t.back()}}})]},proxy:!0}])}),a("div",{staticClass:"items"},[a("div",{staticClass:"item van-hairline--bottom",on:{click:function(e){return t.toInfomation()}}},[a("div",{staticClass:"left"},[t._v(t._s(t.$t("setting.base_setting")))]),a("van-icon",{attrs:{name:"arrow"}})],1),a("div",{staticClass:"item van-hairline--bottom",on:{click:function(e){return t.toLoginPassword()}}},[a("div",{staticClass:"left"},[t._v(t._s(t.$t("setting.login_pwd")))]),a("van-icon",{attrs:{name:"arrow"}})],1),a("div",{staticClass:"item van-hairline--bottom",on:{click:function(e){return t.toPayPassword()}}},[a("div",{staticClass:"left"},[t._v(t._s(t.$t("setting.finance_pwd")))]),a("div",{staticClass:"right"},[a("span",{staticClass:"desc"},[t._v(t._s(this.userInfo.paypassword))]),a("van-icon",{attrs:{name:"arrow"}})],1)])]),a("van-button",{staticClass:"sign-out",attrs:{type:"primary",size:"normal"},on:{click:function(e){return t.loginout()}}},[t._v(t._s(t.$t("setting.logout")))])],1)},Vt=[],Nt={data(){return{userInfo:{}}},methods:{back(){return window.history.back()},toPayPassword(){this.userInfo.paypassword!==this.$t("setting.no_setting")?this.$toast(this.$t("withdraw.with_service")):this.$router.push({path:"/SetPayPassword"})},toLoginPassword(){this.$router.push({path:"/SetLoginPassword"})},toLanguage(){this.$router.push({name:"Language",params:{type:"setting"}})},toInfomation(){this.$router.push({path:"/Infomation"})},loginout(){localStorage.clear(),this.$router.push({path:"/Mine"})},toServicePage(){this.$router.push("ServicePage")},getUserInfo(){this.$http({method:"get",url:"user_info"}).then(t=>{200===t.code?(this.userInfo=t.data,t.data.paypassword?this.userInfo.paypassword=this.$t("setting.y_setting"):this.userInfo.paypassword=this.$t("setting.no_setting")):401===t.code&&this.$toast(t.msg)})}},created(){localStorage.getItem("token")?this.getUserInfo():this.$router.push({path:"/Login"})}},Rt=Nt,Mt=(a("3ef5"),Object(d["a"])(Rt,Bt,Vt,!1,null,"59bb2ab0",null)),Ot=Mt.exports,Ut=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"container page"},[a("van-nav-bar",{staticClass:"nav-bar",attrs:{title:t.$t("setting.base_setting")},scopedSlots:t._u([{key:"left",fn:function(){return[a("van-icon",{attrs:{name:"arrow-left",color:"#fff"},on:{click:function(e){return t.back()}}})]},proxy:!0}])}),a("div",{staticClass:"main-content"},[a("div",{staticClass:"item van-hairline--bottom",on:{click:function(e){return t.toSetName()}}},[a("div",{staticClass:"left"},[t._v(t._s(t.$t("setting.real_name")))]),a("div",{staticClass:"right"},[a("span",{staticClass:"desc"},[t._v(t._s(this.userInfo.name?this.userInfo.name:this.$t("setting.no_setting")))]),a("van-icon",{attrs:{name:"arrow"}})],1)]),a("div",{staticClass:"item van-hairline--bottom",on:{click:function(e){return t.toSetSex()}}},[a("div",{staticClass:"left"},[t._v(t._s(t.$t("setting.sex")))]),a("div",{staticClass:"right"},[a("span",{staticClass:"desc"},[t._v(t._s("0"!==this.userInfo.sex?"1"===this.userInfo.sex?this.$t("setting.man"):this.$t("setting.female"):this.$t("setting.unkown")))]),a("van-icon",{attrs:{name:"arrow"}})],1)]),a("div",{staticClass:"item van-hairline--bottom",on:{click:function(e){return t.toSetBank()}}},[a("div",{staticClass:"left"},[t._v(t._s(t.$t("setting.bind_bank_info")))]),a("div",{staticClass:"right"},[a("span",{staticClass:"desc"},[t._v(t._s(this.isBank?this.$t("setting.bindinged"):this.$t("setting.no")))]),a("van-icon",{attrs:{name:"arrow"}})],1)])])],1)},Dt=[],Et={data(){return{isActive:!1,show:!1,isBank:!1,userInfo:{}}},methods:{back(){return window.history.back()},toSetName(){this.$router.push({path:"/Setname"})},toSetBank(){this.$router.push({path:"/Setbank"})},toSetSex(){this.$router.push({path:"/Setsex"})},openHerderImg(){this.show=!0},select_header_img(t){this.isActive=t},check(){this.$http({method:"post",data:{header_img:this.isActive},url:"user_header_img"}).then(t=>{200===t.code?(this.getUserInfo(),this.$toast(t.msg),this.show=!1):401===t.code&&this.$toast(t.msg)})},getUserInfo(){this.$http({method:"get",url:"user_info"}).then(t=>{200===t.code?this.userInfo=t.data:401===t.code&&this.$toast(t.msg)})},getUserBankInfo(){this.$http({method:"get",url:"user_get_bank"}).then(t=>{200===t.code?t.data.is_bank?this.isBank=!0:this.isBank=!1:401===t.code&&this.$toast(t.msg)})}},created(){localStorage.getItem("token")?(this.getUserInfo(),this.getUserBankInfo()):this.$router.push({path:"/Login"})}},Kt=Et,At=(a("d1d7"),Object(d["a"])(Kt,Ut,Dt,!1,null,"********",null)),zt=At.exports,qt=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"container page"},[a("van-nav-bar",{staticClass:"nav-bar",attrs:{title:t.$t("setting.modify_real_name")},scopedSlots:t._u([{key:"left",fn:function(){return[a("van-icon",{attrs:{name:"arrow-left",color:"#fff"},on:{click:function(e){return t.back()}}})]},proxy:!0},{key:"right",fn:function(){return[a("span",{staticClass:"nav-right",on:{click:function(e){return t.save()}}},[t._v(t._s(t.$t("setting.save")))])]},proxy:!0}])}),a("van-cell-group",[a("van-field",{attrs:{label:t.$t("setting.name"),placeholder:t.$t("setting.name_place")},model:{value:t.name,callback:function(e){t.name=e},expression:"name"}})],1),a("p",[t._v(t._s(t.$t("setting.name_tip")))])],1)},Ht=[],Jt={data(){return{name:"",userInfo:{}}},methods:{back(){return window.history.back()},save(){return this.userInfo.name?(this.$toast(this.$t("setting.repect")),!0):""===this.name||null===this.name||void 0===this.name?(this.$toast.fail(this.$t("setting.name_place")),!1):void this.$http({method:"get",data:{name:this.name},url:"user_set_name"}).then(t=>{200===t.code?(this.getUserInfo(),this.name=this.userInfo.name,this.$toast(t.msg)):401===t.code&&this.$toast(t.msg)})},getUserInfo(){this.$http({method:"get",url:"user_info"}).then(t=>{200===t.code?(this.userInfo=t.data,this.name=t.data.name):401===t.code&&this.$toast(t.msg)})}},created(){localStorage.getItem("token")?this.getUserInfo():this.$router.push({path:"/Login"})}},Wt=Jt,Gt=(a("6151"),Object(d["a"])(Wt,qt,Ht,!1,null,"d1c0fb6a",null)),Ft=Gt.exports,Xt=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"container page"},[a("van-nav-bar",{staticClass:"nav-bar",attrs:{title:t.$t("setting.language")},scopedSlots:t._u([{key:"left",fn:function(){return[a("van-icon",{attrs:{name:"arrow-left",color:"#fff"},on:{click:function(e){return t.back()}}})]},proxy:!0}])}),a("div",{staticClass:"items"},[a("div",{staticClass:"item van-hairline--bottom",on:{click:function(e){return t.changeLang("zh_cn",e)}}},[t._m(0),"zh_cn"==t.lang?a("div",[t._v("✓")]):t._e()]),a("div",{staticClass:"item van-hairline--bottom",on:{click:function(e){return t.changeLang("en_us",e)}}},[t._m(1),"en_us"==t.lang?a("div",[t._v("✓")]):t._e()]),a("div",{staticClass:"item van-hairline--bottom",on:{click:function(e){return t.changeLang("es_spa",e)}}},[t._m(2),"es_spa"==t.lang?a("div",[t._v("✓")]):t._e()]),a("div",{staticClass:"item van-hairline--bottom",on:{click:function(e){return t.changeLang("ms_my",e)}}},[t._m(3),"ms_my"==t.lang?a("div",[t._v("✓")]):t._e()]),a("div",{staticClass:"item van-hairline--bottom",on:{click:function(e){return t.changeLang("yn_yu",e)}}},[t._m(4),"yn_yu"==t.lang?a("div",[t._v("✓")]):t._e()])])],1)},Yt=[function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"flex_center"},[n("div",[n("img",{attrs:{src:a("d435")}})]),n("div",{staticClass:"info"},[t._v("简体中文")])])},function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"flex_center"},[n("div",[n("img",{attrs:{src:a("f940")}})]),n("div",{staticClass:"info"},[t._v("English")])])},function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"flex_center"},[n("div",[n("img",{attrs:{src:a("24ab")}})]),n("div",{staticClass:"info"},[t._v("España")])])},function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"flex_center"},[n("div",[n("img",{attrs:{src:a("650f")}})]),n("div",{staticClass:"info"},[t._v("Melayu")])])},function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"flex_center"},[n("div",[n("img",{attrs:{src:a("dea8")}})]),n("div",{staticClass:"info"},[t._v("Tiếng Việt")])])}],Qt={name:"Language",data(){return{lang:this.$i18n.locale||"es_spa",source:""}},created(){},mounted(){this.source=this.$route.params.type},methods:{back(){return window.history.back()},changeLang(t){Y["a"].loading({duration:200}),this.lang=t,this.$i18n.locale=t,localStorage.setItem("lang",t),"setting"==this.source?this.$router.push({path:"/"}):this.$router.go(-1)}}},Zt=Qt,te=(a("87e6"),Object(d["a"])(Zt,Xt,Yt,!1,null,"701427e5",null)),ee=te.exports,ae=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"container page"},[a("van-nav-bar",{staticClass:"nav-bar",attrs:{title:t.$t("setting.sex_place")},scopedSlots:t._u([{key:"left",fn:function(){return[a("van-icon",{attrs:{name:"arrow-left",color:"#fff"},on:{click:function(e){return t.back()}}})]},proxy:!0}])}),a("div",{staticClass:"sex"},[a("van-radio-group",{model:{value:t.radio,callback:function(e){t.radio=e},expression:"radio"}},[a("div",{staticClass:"item van-hairline--bottom",on:{click:function(e){return t.chooesSex(1)}}},[a("van-radio",{attrs:{name:"1"}},[t._v(t._s(t.$t("setting.man")))])],1),a("div",{staticClass:"item van-hairline--bottom",on:{click:function(e){return t.chooesSex(2)}}},[a("van-radio",{attrs:{name:"2"}},[t._v(t._s(t.$t("setting.female")))])],1)])],1)],1)},ne=[],ie={data(){return{radio:"",userInfo:{}}},methods:{back(){return window.history.back()},chooesSex(t){this.$http({method:"post",data:{sex:t},url:"user_set_sex"}).then(e=>{200===e.code?(this.getUserInfo(),this.radio=t,this.$toast(e.msg)):401===e.code&&this.$toast(e.msg)})},getUserInfo(){console.log(window.localStorage.getItem("lang").toLowerCase()),this.$http({method:"get",url:"user_info",headers:{lang:window.localStorage.getItem("lang").toLowerCase()}}).then(t=>{200===t.code?(this.userInfo=t.data,this.radio=t.data.sex):401===t.code&&this.$toast(t.msg)})}},created(){localStorage.getItem("token")?this.getUserInfo():this.$router.push({path:"/Login"})}},se=ie,oe=(a("f044"),Object(d["a"])(se,ae,ne,!1,null,"4bac0c4c",null)),re=oe.exports,le=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"container page"},[a("div",{staticClass:"header"},[a("van-nav-bar",{staticClass:"nav-bar",attrs:{title:t.$t("recharge.recharge")},scopedSlots:t._u([{key:"left",fn:function(){return[a("van-icon",{attrs:{name:"arrow-left",color:"#fff"},on:{click:function(e){return t.back()}}})]},proxy:!0}])}),a("div",{staticClass:"info"},[a("p",{staticClass:"title"},[t._v(" "+t._s(t.$t("recharge.curr_balance"))+"("+t._s(t.$t("reservation.unit"))+") ")]),a("p",{staticClass:"value"},[t._v(t._s(this.balance))])]),a("div",{staticClass:"content recharge"},[a("van-form",{on:{submit:t.onSubmit}},[a("div",{staticClass:"form-item"},[a("div",{staticClass:"form-item-title"},[t._v(t._s(t.$t("recharge.input_money")))]),a("div",{staticStyle:{height:"65px"}},[a("van-field",{attrs:{name:"money",label:"MXN",placeholder:t.$t("recharge.input_money")},model:{value:t.money,callback:function(e){t.money=e},expression:"money"}})],1)]),a("div",{staticClass:"form-item"},[a("div",{staticClass:"form-item-title"},[t._v(t._s(t.$t("recharge.pay_way")))]),a("div",[a("van-radio-group",{model:{value:t.pay_way,callback:function(e){t.pay_way=e},expression:"pay_way"}},[a("van-radio",{attrs:{name:"Mexicopay"}},[t._v("MexicoPay")])],1)],1),a("div",{staticStyle:{margin:"16px"}},[a("van-button",{attrs:{round:"",block:"",type:"info","native-type":"submit"}},[t._v("下一步")])],1)])])],1)],1)])},ce=[],de=a("772a"),ue=a("565f"),he=a("e27c"),me=a("9f14");n["a"].use(de["a"]).use(ue["a"]).use(he["a"]).use(me["a"]);var pe,ge={data(){return{balance:0,pay_way:"Mexicopay",win_money:0,money:"",personalreport:{}}},mounted(){this.balance=this.$route.params.balance},methods:{back(){return window.history.back()},onSubmit(t){const e=t.money;e<=0?this.$toast(this.$t("reservation.money_err")):this.$http({method:"post",data:{pay_way:this.pay_way,money:e},url:"recharge"}).then(t=>{console.log(t),200===t.code?window.location.href=t.data.pay_url:401===t.code&&this.$toast(t.msg)})},getPersonalreport(){this.$http({method:"get",url:"user_get_personalreport"}).then(t=>{200===t.code?(this.personalreport=t.data,this.win_money=this.personalreport.win_money-this.personalreport.play_money):401===t.code&&this.$toast(t.msg)})}},created(){localStorage.getItem("token")?this.getPersonalreport():this.$router.push({path:"/Login"})}},_e=ge,ve=(a("035d"),Object(d["a"])(_e,le,ce,!1,null,"93059f62",null)),fe=ve.exports,be=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"container page"},[a("van-nav-bar",{staticClass:"nav-bar",attrs:{title:t.$t("setting.set_money_pwd")},scopedSlots:t._u([{key:"left",fn:function(){return[a("van-icon",{attrs:{name:"arrow-left",color:"#fff"},on:{click:function(e){return t.back()}}})]},proxy:!0}])}),a("div",{staticClass:"wrapper"},[a("div",{staticClass:"item"},[a("p",{staticClass:"title"},[t._v(t._s(t.$t("setting.money_place")))]),a("van-password-input",{attrs:{value:t.opw,length:4,gutter:10,focused:t.oshowKeyboard},on:{focus:function(e){t.oshowKeyboard=!0,t.tshowKeyboard=!1}}}),a("van-number-keyboard",{attrs:{show:t.oshowKeyboard},on:{input:function(e){3!==t.opw.length?t.oshowKeyboard=!0:t.tshowKeyboard=!0},blur:function(e){t.oshowKeyboard=!1}},model:{value:t.opw,callback:function(e){t.opw=e},expression:"opw"}})],1),a("div",{staticClass:"item"},[a("p",{staticClass:"title"},[t._v(t._s(t.$t("setting.money_again_place")))]),a("van-password-input",{attrs:{value:t.tpw,length:4,gutter:10,focused:t.tshowKeyboard},on:{focus:function(e){t.tshowKeyboard=!0,t.oshowKeyboard=!1}}}),a("van-number-keyboard",{attrs:{show:t.tshowKeyboard},on:{input:function(e){3!==t.tpw.length?t.tshowKeyboard=!0:t.tshowKeyboard=!1,t.oshowKeyboard=!1},blur:function(e){t.tshowKeyboard=!1}},model:{value:t.tpw,callback:function(e){t.tpw=e},expression:"tpw"}})],1),a("van-button",{staticClass:"sub-btn",attrs:{type:"default"},on:{click:function(e){return t.setPayPassword()}}},[t._v(t._s(t.$t("reservation.submit")))])],1)],1)},ke=[],ye={data(){return{opw:"",tpw:"",oshowKeyboard:!0,tshowKeyboard:!1,userInfo:{}}},methods:{back(){return window.history.back()},setPayPassword(){return this.oshowKeyboard=!1,this.tshowKeyboard=!1,4!==this.opw.length?(this.oshowKeyboard=!0,this.$toast(this.$t("setting.prefect")),!1):4!==this.tpw.length?(this.tshowKeyboard=!0,this.$toast(this.$t("setting.prefect")),!1):this.opw!==this.tpw?(this.$toast(this.$t("setting.prefect")),!1):void this.$http({method:"post",data:{paypassword:this.opw},url:"user_set_paypw"}).then(t=>{200===t.code?(setTimeout(()=>{this.$toast(t.msg)},500),this.$router.push("Mine")):401===t.code&&this.$toast(t.msg)})},getUserInfo(){this.$http({method:"get",url:"user_info"}).then(t=>{200===t.code?(this.userInfo=t.data,this.radio=t.data.sex):401===t.code&&this.$toast(t.msg)})}},created(){localStorage.getItem("token")?this.getUserInfo():this.$router.push({path:"/Login"})}},we=ye,$e=(a("dc71"),Object(d["a"])(we,be,ke,!1,null,"abb84908",null)),Ce=$e.exports,xe=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"container page"},[a("van-nav-bar",{staticClass:"nav-bar",attrs:{title:t.$t("setting.login_pwd_tip")},scopedSlots:t._u([{key:"left",fn:function(){return[a("van-icon",{attrs:{name:"arrow-left",color:"#fff"},on:{click:function(e){return t.back()}}})]},proxy:!0},{key:"right",fn:function(){return[a("span",{staticClass:"nav-right",on:{click:function(e){return t.save()}}},[t._v(t._s(t.$t("setting.save")))])]},proxy:!0}])}),a("van-cell-group",[a("van-field",{attrs:{label:t.$t("setting.old_pwd"),placeholder:t.$t("setting.old_pwd_tip")},model:{value:t.old_password,callback:function(e){t.old_password=e},expression:"old_password"}}),a("van-field",{attrs:{label:t.$t("setting.new_pwd"),placeholder:t.$t("setting.new_pwd_tip")},model:{value:t.o_new_password,callback:function(e){t.o_new_password=e},expression:"o_new_password"}}),a("van-field",{attrs:{label:t.$t("setting.new_pwd"),placeholder:t.$t("setting.new_again_tip")},model:{value:t.t_new_password,callback:function(e){t.t_new_password=e},expression:"t_new_password"}})],1)],1)},Se=[],Ie={data(){return{o_new_password:"",t_new_password:"",old_password:"",userInfo:{}}},methods:{back(){return window.history.back()},save(){return""===this.o_new_password||null===this.o_new_password||void 0===this.o_new_password||""===this.t_new_password||null===this.t_new_password||void 0===this.t_new_password||""===this.old_password||null===this.old_password||void 0===this.old_password?(this.$toast.fail(this.$t("setting.prefect")),!1):this.o_new_password!==this.t_new_password?(this.$toast(this.$t("setting.pwd_error")),!1):void this.$http({method:"get",data:{old_password:this.old_password,new_password:this.o_new_password},url:"user_set_loginpw"}).then(t=>{200===t.code?(this.$toast(t.msg),setTimeout(()=>{localStorage.clear(),this.$router.push("Login")},500)):401===t.code&&this.$toast(t.msg)})},getUserInfo(){this.$http({method:"get",url:"user_info"}).then(t=>{200===t.code?this.userInfo=t.data:401===t.code&&this.$toast(t.msg)})}},created(){localStorage.getItem("token")?this.getUserInfo():this.$router.push({path:"/Login"})}},Le=Ie,Pe=(a("6b7e"),Object(d["a"])(Le,xe,Se,!1,null,"007117b0",null)),je=Pe.exports,Te=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"container page"},[a("van-nav-bar",{staticClass:"nav-bar",attrs:{title:this.lottery.name},scopedSlots:t._u([{key:"left",fn:function(){return[a("van-icon",{attrs:{name:"arrow-left",color:"#fff"},on:{click:function(e){return t.back()}}})]},proxy:!0}])}),a("div",{staticClass:"record"},[a("div",{staticClass:"period"},[a("van-image",{staticClass:"cover",attrs:{src:this.lottery.ico},scopedSlots:t._u([{key:"loading",fn:function(){return[a("van-loading",{attrs:{type:"spinner"}})]},proxy:!0}])}),a("span",{staticClass:"period-number"},[t._v(t._s(this.lottery.now_expect))]),a("div",{staticClass:"next-number"},[a("span",[t._v(t._s(this.lottery.next_expect))]),a("van-count-down",{attrs:{time:t.time},on:{finish:function(e){return t.check()}}})],1)],1),a("div",{staticClass:"linear-gradient",staticStyle:{background:"linear-gradient(to right, rgba(126, 86, 120, 0), rgb(230, 195, 161), rgba(126, 86, 120, 0))"}}),a("div",{staticClass:"recent"},[a("div",{staticClass:"kuaisan-ball left"},[a("van-image",{staticClass:"res-img",attrs:{src:this.shanzi_1},scopedSlots:t._u([{key:"loading",fn:function(){return[a("van-loading",{attrs:{type:"spinner"}})]},proxy:!0}])}),a("van-image",{staticClass:"res-img",attrs:{src:this.shanzi_2},scopedSlots:t._u([{key:"loading",fn:function(){return[a("van-loading",{attrs:{type:"spinner"}})]},proxy:!0}])}),a("van-image",{staticClass:"res-img",attrs:{src:this.shanzi_3},scopedSlots:t._u([{key:"loading",fn:function(){return[a("van-loading",{attrs:{type:"spinner"}})]},proxy:!0}])}),a("span",{staticClass:"res-des middle"},[t._v(t._s(this.sum))]),a("span",{staticClass:"res-des middle"},[t._v(t._s(this.size))]),a("span",{staticClass:"res-des middle"},[t._v(t._s(this.double))])],1),a("van-icon",{class:{up:t.active,down:!t.active},attrs:{name:"arrow-down"},on:{click:function(e){t.active?t.active=!1:t.active=!0}}})],1)]),a("div",{staticClass:"history_popup"}),a("div",{staticClass:"wrapper"},[a("div",{staticClass:"options-bar"},[a("div",{staticClass:"game"},[a("div",{staticClass:"tips"},[a("p",{staticClass:"odds"},[t._v("【"+t._s(this.lottery.desc)+"】")]),a("div",{staticClass:"play-tip"},[a("van-icon",{attrs:{name:"more-o"}}),a("span",{staticClass:"span-text",on:{click:function(e){return t.$router.push({path:"/GameRecord"})}}},[t._v(t._s(t.$t("my.task_record")))]),a("van-popup",{staticClass:"mask",attrs:{"get-container":"body"},model:{value:t.playgame,callback:function(e){t.playgame=e},expression:"playgame"}},[a("div",{staticClass:"play-type-tip"},[a("div",{staticClass:"title"},[t._v("玩法规则")]),a("div",{staticClass:"wrapper"},[a("div",{staticClass:"item"},[a("van-icon",{attrs:{name:"info-o"}}),a("div",{staticClass:"content"},[a("p",{staticClass:"content-title"},[t._v("玩法提示")]),a("p",{staticClass:"content-detail"},[t._v("从可选和值形态里面选择号码进行下注")])])],1),a("div",{staticClass:"item"},[a("van-icon",{attrs:{name:"comment-o"}}),a("div",{staticClass:"content"},[a("p",{staticClass:"content-title"},[t._v("中奖说明")]),a("p",{staticClass:"content-detail"},[t._v("三个开奖号码总和值11~18 为大;总和值3~ 10为小;")])])],1),a("div",{staticClass:"item"},[a("van-icon",{attrs:{name:"description"}}),a("div",{staticClass:"content"},[a("p",{staticClass:"content-title"},[t._v("投注范例")]),a("p",{staticClass:"content-detail"},[t._v("投注方案：小 开奖号码：123,即中小")])])],1)])])])],1)]),a("div",{staticClass:"linear-gradient",staticStyle:{background:"linear-gradient(to right, rgba(126, 86, 120, 0), rgb(230, 195, 161), rgba(126, 86, 120, 0))"}}),a("div",{staticClass:"sumValueTwoSides"},t._l(t.lottery_peilv_list,(function(e,n){return a("div",{key:n,staticClass:"rectangle large",class:{active:t.choose[e.type]},on:{click:function(a){return t.choosePlay(e.type,e.name)}}},[a("div",{staticClass:"wrapper"},[a("div",{staticClass:"content"},[a("p",{staticClass:"name-text large"},[t._v(t._s(e.name))]),a("p",{staticClass:"odd-text large",staticStyle:{opacity:"0"}},[t._v(t._s(e.proportion))])])])])})),0)])]),a("div",{staticClass:"bottom-bar"},[a("div",{staticClass:"bar"},[a("div",{staticClass:"left"},[a("div",{staticClass:"item",on:{click:function(e){t.shopping?t.shopping=!1:t.shopping=!0}}},[a("van-icon",{staticClass:"jixuanico",attrs:{name:"cart-o"}}),a("span",{staticClass:"text"},[t._v(t._s(t.$t("reservation.task_list")))])],1),a("div",{staticClass:"line"})]),a("div",{staticClass:"mid"},[a("span",{staticClass:"text"},[t._v(t._s(t.$t("reservation.available_balance")))]),a("span",{staticClass:"text num"},[t._v(t._s(this.userInfo.money))]),a("span",{staticClass:"text"},[t._v(t._s(t.$t("reservation.unit")))])]),a("div",{staticClass:"right",on:{click:function(e){return t.jiesuan()}}},[t._v(" "+t._s(t.$t("reservation.submit"))+" ")])]),a("div",{staticClass:"wrapper",class:{active:t.shopping}},[a("div",{staticClass:"item"},[a("span",{staticClass:"label"},[t._v(t._s(t.$t("reservation.curr_choose"))+"：")]),a("div",{staticClass:"bet-number"},[t._v(t._s(this.shopchoose))]),a("van-icon",{class:{up:!t.shopping,down:t.shopping},attrs:{name:"arrow-down"},on:{click:function(e){t.shopping?t.shopping=!1:t.shopping=!0}}})],1),a("div",{staticClass:"item"},[a("span",{staticClass:"label"},[t._v(t._s(t.$t("reservation.per_price")))]),a("div",{staticClass:"amount-wrapper"},[a("van-field",{attrs:{type:"digit",placeholder:t.$t("reservation.price_place")},model:{value:t.money,callback:function(e){t.money=e},expression:"money"}}),a("span",{staticClass:"label"},[t._v(t._s(t.$t("reservation.unit")))])],1)]),a("div",{staticClass:"item"},[a("div",{staticClass:"part"},[a("span",[t._v(t._s(t.$t("reservation.total")))]),a("span",{staticClass:"number"},[t._v(t._s(this.formData.length))]),a("span",[t._v(t._s(t.$t("reservation.note")))])]),a("div",{staticClass:"part"},[a("span",[t._v(t._s(t.$t("reservation.total")))]),a("span",{staticClass:"number"},[t._v(t._s(this.formData.length*this.money))]),a("span",[t._v(t._s(t.$t("reservation.unit")))])])])])]),a("van-popup",{attrs:{"get-container":"body"},model:{value:t.jiesuanpage,callback:function(e){t.jiesuanpage=e},expression:"jiesuanpage"}},[a("div",{staticClass:"confirm-order-modal"},[a("div",{staticClass:"head van-hairline--bottom"},[a("p",{staticClass:"text"},[t._v(t._s(t.$t("reservation.task_list")))])]),a("ui",{staticClass:"list"},t._l(t.formData,(function(e,n){return a("li",{key:n,staticClass:"lise-item van-hairline--bottom"},[a("div",{staticClass:"main"},[a("p",{staticClass:"bet-name"},[t._v(t._s(e.name))]),a("p",{staticClass:"detail-text"},[t._v("1"+t._s(t.$t("reservation.note"))+"X"+t._s(t.money)+t._s(t.$t("reservation.unit"))+"="+t._s(t.money)+t._s(t.$t("reservation.unit")))])]),a("van-icon",{attrs:{name:"close"},on:{click:function(a){return t.clearChooes(e.type)}}})],1)})),0),a("div",{staticClass:"sub-bar"},[a("van-button",{staticClass:"item cancel-btn",attrs:{type:"default"},on:{click:function(e){return t.allClear()}}},[t._v(t._s(t.$t("reservation.clear_order")))]),a("van-button",{staticClass:"item sub-btn",attrs:{type:"default"},on:{click:t.doSub}},[t._v(t._s(t.$t("reservation.submit")))])],1)],1)]),a("van-popup",{style:{height:"70%"},attrs:{position:"top"},model:{value:t.active,callback:function(e){t.active=e},expression:"active"}},[a("van-pull-refresh",{attrs:{"pulling-text":"Sao chép nhanh quy trình thả xuống","loosing-text":"Menyegarkan halaman","loading-text":"Proses pemuatan cepat","success-text":"Proses pemuatan berhasil"},on:{refresh:t.onRefresh},model:{value:t.isLoading,callback:function(e){t.isLoading=e},expression:"isLoading"}},[a("div",{staticClass:"wrapper"},[a("div",{staticClass:"item"},[a("div",{staticClass:"left font-weight"},[t._v(t._s(t.$t("reservation.num")))]),a("div",{staticClass:"right font-weight"},[t._v(t._s(t.$t("reservation.win_num")))])]),t._l(t.lottery_list,(function(e,n){return a("div",{key:n,staticClass:"item"},[a("div",{staticClass:"left font-weight"},[t._v(t._s(e.expect))]),a("div",{staticClass:"right font-weight"},[a("div",{staticClass:"kuaisan-ball left"},[a("van-image",{staticClass:"res-img",attrs:{src:"img/lottery/shaizi/"+e.opencode[0]+".png"},scopedSlots:t._u([{key:"loading",fn:function(){return[a("van-loading",{attrs:{type:"spinner"}})]},proxy:!0}],null,!0)}),a("van-image",{staticClass:"res-img",attrs:{src:"img/lottery/shaizi/"+e.opencode[1]+".png"},scopedSlots:t._u([{key:"loading",fn:function(){return[a("van-loading",{attrs:{type:"spinner"}})]},proxy:!0}],null,!0)}),a("van-image",{staticClass:"res-img",attrs:{src:"img/lottery/shaizi/"+e.opencode[2]+".png"},scopedSlots:t._u([{key:"loading",fn:function(){return[a("van-loading",{attrs:{type:"spinner"}})]},proxy:!0}],null,!0)}),a("span",{staticClass:"res-des middle"},[t._v(t._s(e.opencode[0]+e.opencode[1]+e.opencode[2]))]),a("span",{staticClass:"res-des middle"},[t._v(t._s(e.opencode[0]+e.opencode[1]+e.opencode[2]>=11&&e.opencode[0]+e.opencode[1]+e.opencode[2]<=18?"漂亮":"性感"))]),a("span",{staticClass:"res-des middle"},[t._v(t._s((e.opencode[0]+e.opencode[1]+e.opencode[2])%2===0?"可愛-可愛":"迷人"))])],1)])])}))],2)])],1)],1)],1)},Be=[],Ve=0,Ne={data(){return{jiesuanpage:!1,choose:{漂亮:!1,性感:!1,迷人:!1,"可愛-可愛":!1,3:!1,4:!1,5:!1,6:!1,7:!1,8:!1,9:!1,10:!1,11:!1,12:!1,13:!1,14:!1,15:!1,16:!1,17:!1,18:!1},playgame:!1,shopping:!1,isLoading:!1,play:{},lottery_peilv_list:{},lottery_list:{},active:!1,userInfo:{},lottery:{},shanzi_1:"0",shanzi_2:"0",shanzi_3:"0",sum:0,size:"",double:"",time:0,shopchoose:this.$t("reservation.no_choose"),gameitem:"",formData:[],money:""}},methods:{back(){return window.history.back()},doSub(){return"0"===this.money?(this.$toast(this.$t("reservation.money_err")),!1):0===this.formData.length?(this.$toast(this.$t("reservation.choose_num")),!1):""===this.money?(this.$toast(this.$t("reservation.price_place")),!1):this.userInfo.money-this.money*this.formData.length<0?(this.$toast(this.$t("reservation.balance_enough")),!1):(this.$http({method:"post",data:{item:this.gameitem,money:this.money,lid:this.lottery.id,mid:this.userInfo.id,expect:this.lottery.now_expect},url:"game_place_order"}).then(t=>{200===t.code?(this.$toast(t.msg),this.allClear(),this.getUserInfo()):401===t.code&&this.$toast(t.msg)}),!0)},allClear(){for(var t=0;t<this.formData.length;t++)this.choose[this.formData[t]["type"]]=!1;this.formData.length=0,this.money="",this.shopchoose=this.$t("reservation.no_choose"),this.gameitem="",this.shopping=!1,this.jiesuanpage=!1},jiesuan(){return 0===this.formData.length?(this.$toast(this.$t("reservation.choose_num")),!1):""===this.money?(this.$toast(this.$t("reservation.price_place")),!1):void(this.jiesuanpage?this.jiesuanpage=!1:this.jiesuanpage=!0)},clearChooes(t){for(var e=0;e<this.formData.length;e++)t===this.formData[e]["type"]&&(this.formData.splice(e,1),this.choose[t]=!1);if(this.formData.length>=1)for(var a=0;a<this.formData.length;a++)0===a?(this.shopchoose=this.formData[a]["name"],this.gameitem=this.formData[a]["type"]):(this.shopchoose+=","+this.formData[a]["name"],this.gameitem+=","+this.formData[a]["type"]);else this.shopchoose=this.$t("reservation.no_choose"),this.gameitem="",this.shopping=!1;0===this.formData.length&&(this.jiesuanpage=!1)},choosePlay(t,e){if(!0===this.choose[t]){this.choose[t]=!1;for(var a=0;a<this.formData.length;a++)t===this.formData[a]["type"]&&this.formData.splice(a,1)}else!1===this.choose[t]&&(this.formData.push({name:e,type:t}),this.choose[t]=!0);if(1===this.formData.length&&(this.shopping=!0),this.formData.length>=1)for(var n=0;n<this.formData.length;n++)0===n?(this.shopchoose=this.formData[n]["name"],this.gameitem=this.formData[n]["type"]):(this.shopchoose+=","+this.formData[n]["name"],this.gameitem+=","+this.formData[n]["type"]);else this.shopchoose=this.$t("reservation.no_choose"),this.gameitem="",this.shopping=!1},check(){localStorage.getItem("token")?pe=window.setInterval(()=>{setTimeout(()=>{this.getUserInfo(),this.getLotteryInfo(),this.getLotteryList(),Ve++,Ve>5&&(clearInterval(pe),Ve=0)},0)},300):this.$router.push({path:"/Login"})},onRefresh(){setTimeout(()=>{this.$toast(this.$t("reservation.refresh")),this.getLotteryList(),this.isLoading=!1},200)},getUserInfo(){this.$http({method:"get",url:"user_info"}).then(t=>{200===t.code?this.userInfo=t.data:401===t.code&&this.$toast(t.msg)})},getLotteryPeilv(){this.$http({method:"get",data:{id:this.$route.query.id},url:"lottery_get_peilv"}).then(t=>{200===t.code?this.lottery_peilv_list=t.data:401===t.code&&this.$toast(t.msg)})},getLotteryList(){this.$http({method:"get",data:{key:this.$route.query.key},url:"lottery_get_one_list"}).then(t=>{200===t.code?(this.lottery_list=t.data,console.log("lottery_list:"),console.log(t.data),this.getLotteryPeilv()):401===t.code&&this.$toast(t.msg)})},getLotteryInfo(){this.$http({method:"get",data:{key:this.$route.query.key},url:"lottery_get_info"}).then(t=>{if(200===t.code){var e,a,n;if(parseFloat(this.userInfo.money)<parseFloat(t.data.condition))return this.$toast(this.$t("reservation.contact_admin")),this.$router.push({path:"/Home"}),!1;this.lottery=t.data,this.time=1e3*t.data.second,this.time/1e3===59&&this.$toast(this.$t("reservation.prize_succ")+this.lottery.now_expect),this.shanzi_1="img/lottery/shaizi/"+(null===(e=t.data)||void 0===e?void 0:e.opencode[0])+".png",this.shanzi_2="img/lottery/shaizi/"+(null===(a=t.data)||void 0===a?void 0:a.opencode[1])+".png",this.shanzi_3="img/lottery/shaizi/"+(null===(n=t.data)||void 0===n?void 0:n.opencode[2])+".png",this.sum=t.data.opencode[0]+t.data.opencode[1]+t.data.opencode[2],this.sum>=11&&this.sum<=18?this.size="漂亮":this.sum>=3&&this.sum<=10&&(this.size="性感"),this.sum%2===0?this.double="可愛-可愛":this.double="迷人"}else 401===t.code&&this.$toast(t.msg)})}},created(){localStorage.getItem("token")?(this.getUserInfo(),this.getLotteryInfo(),this.getLotteryList()):this.$router.push({path:"/Login"})},destroyed(){clearInterval(pe)}},Re=Ne,Me=(a("72fb"),Object(d["a"])(Re,Te,Be,!1,null,"04b39cc3",null)),Oe=Me.exports,Ue=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"container page"},[a("div",{staticClass:"header"},[a("van-nav-bar",{staticClass:"nav-bar",attrs:{title:t.$t("my.sys_notice")},scopedSlots:t._u([{key:"left",fn:function(){return[a("van-icon",{attrs:{name:"arrow-left",color:"#fff"},on:{click:function(e){return t.back()}}})]},proxy:!0}])})],1),a("div",{staticClass:"content"},[a("van-pull-refresh",{attrs:{"pulling-text":"Sao chép nhanh quy trình thả xuống","loosing-text":"Menyegarkan halaman","loading-text":"Proses pemuatan cepat","success-text":"Proses pemuatan berhasil"},on:{refresh:t.onRefresh},model:{value:t.isLoading,callback:function(e){t.isLoading=e},expression:"isLoading"}},t._l(t.notice,(function(e,n){return a("div",{key:n,staticClass:"listItem"},[a("div",{staticClass:"listTitle"},[t._v(t._s(e.name))]),a("div",{staticClass:"listContent html"},[a("p",[t._v(t._s(e.text)),a("br")])]),a("div",{staticClass:"listTime"},[a("div",{staticClass:"listTimeText"},[t._v(t._s(e.create_time))])])])})),0)],1)])},De=[],Ee={data(){return{isLoading:!1,notice:{},loading:!1,finished:!1}},methods:{back(){return window.history.back()},getNoticeList(){this.$http({method:"get",url:"sys_get_notice_list"}).then(t=>{console.log(t),this.notice=t.data})},onRefresh(){setTimeout(()=>{this.$toast(this.$t("reservation.refresh")),this.isLoading=!1,this.getNoticeList()},500)}},created(){this.getNoticeList()}},Ke=Ee,Ae=(a("bd49"),Object(d["a"])(Ke,Ue,De,!1,null,"1d59bd46",null)),ze=Ae.exports,qe=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"container page"},[a("van-nav-bar",{staticClass:"nav-bar",attrs:{title:this.videoInfo.vod_name},scopedSlots:t._u([{key:"left",fn:function(){return[a("van-icon",{attrs:{name:"arrow-left",color:"#fff"},on:{click:function(e){return t.back()}}})]},proxy:!0}])}),t._m(0),a("div",{staticClass:"movie-content"},[a("div",{staticClass:"movie-descript"},[a("p",[t._v(t._s(this.videoInfo.vod_name))]),a("span",[t._v(t._s(this.videoInfo.count)+t._s(t.$t("video.num_play")))])]),a("div",{staticClass:"movie-body"},[a("div",{staticClass:"movie-title"},[a("div",[a("span",[t._v(t._s(t.$t("index.recmonmand")))])])]),a("div",{staticClass:"movie-list"},t._l(t.moreVideoInfo,(function(e,n){return a("div",{key:n,staticClass:"movie-play-item",on:{click:function(a){return t.toPlayVideo(e.id)}}},[a("div",[a("img",{attrs:{src:e.vod_pic}}),a("div",[a("div",{staticClass:"van-count-down"},[t._v(t._s(e.time))])])]),a("div",[a("p",[t._v(t._s(e.vod_name))]),a("span",[t._v(t._s(e.count)+t._s(t.$t("video.num_play")))])])])})),0)])])],1)},He=[function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"movie-video"},[a("video",{staticClass:"video-js",attrs:{id:"my-video"}})])}],Je=a("f0e2"),We=(a("a151"),{data(){return{nowPlayVideoUrl:"",cover:"",userInfo:[],videoInfo:{},moreVideoInfo:{},player:null,is_play:!1,times:null,is_see:0}},methods:{back(){this.$router.push({path:"Home"})},getVideoInfo(){this.$http({method:"get",data:{id:this.$route.query.id},url:"video_get_info"}).then(t=>{this.videoInfo=t.data,this.nowPlayVideoUrl=this.videoInfo.vod_play_url,this.cover=this.videoInfo.vod_pic;let e=document.getElementById("my-video");e.poster=this.cover,this.getVideo()})},toPlayVideo(t){localStorage.getItem("token")?(this.$router.push({path:"?id="+t}),location.reload()):this.$router.push({path:"/Login"})},getMoreVideoItem(){this.$http({method:"get",url:"video_get_more_item"}).then(t=>{this.moreVideoInfo=t.data})},getVideo(){this.player.src([{src:this.nowPlayVideoUrl,type:"application/x-mpegURL"}])},getUserInfo(){this.$http({method:"get",url:"user_info"}).then(t=>{200===t.code?(this.userInfo=t.data,this.is_see=this.userInfo.is_see,1!==this.userInfo.status?(this.$toast(this.$t("video.account_out")),localStorage.clear(),this.$router.push({path:"/Login"})):(this.$store.getters.getBaseInfo.isplay,this.getVideoInfo(),this.getMoreVideoItem())):401===t.code&&this.$toast(t.msg)})}},mounted(){const t=this;localStorage.getItem("token")?(this.player=Object(Je["a"])("my-video",{height:"200px",preload:"auto",controls:!0,multipleArray:[.75,1,1.5,2]},(function(){this.on("play",()=>{t.is_play=!0})})),this.getUserInfo(),this.times=setInterval(()=>{if(this.is_play&&0==this.is_see){const t=Math.round(this.player.currentTime());if(t>=180)return this.player.pause(),void this.$toast(this.$t("video.buy"))}},2e3)):this.$router.push({path:"/Login"})},destroyed(){this.is_play&&(this.is_play=!1),clearInterval(this.times)}}),Ge=We,Fe=(a("6420"),Object(d["a"])(Ge,qe,He,!1,null,"2b1dbf89",null)),Xe=Fe.exports,Ye=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"container page"},[a("van-nav-bar",{staticClass:"nav-bar",attrs:{title:t.$t("setting.bank_info")},scopedSlots:t._u([{key:"left",fn:function(){return[a("van-icon",{attrs:{name:"arrow-left",color:"#fff"},on:{click:function(e){return t.back()}}})]},proxy:!0}])}),a("div",{staticClass:"wrapper"},[t.is_bind?a("div",{staticClass:"bank"},[a("div",{staticClass:"info"},[a("div",{staticClass:"row-content"},[t._v(t._s(this.bankInfo.bankinfo))]),a("div",{staticClass:"row-content"},[t._v(t._s(this.userInfo.name))]),a("div",{staticClass:"row-content"},[t._v(t._s(this.bankInfo.bankid))])])]):a("div",{staticClass:"add-card",on:{click:function(e){return t.toBindCard()}}},[a("van-icon",{attrs:{name:"plus"}}),a("span",[t._v(t._s(t.$t("setting.add_bank")))])],1),a("div",{staticClass:"tips"},[t._v(t._s(t.$t("setting.bind_bank_tip")))])])],1)},Qe=[],Ze={data(){return{is_bind:!1,bankInfo:{},userInfo:{}}},methods:{back(){return window.history.back()},getUserBankInfo(){this.$http({method:"get",url:"user_get_bank"}).then(t=>{200===t.code?t.data.is_bank?(this.is_bind=!0,this.bankInfo=t.data.info):this.is_bind=!1:401===t.code&&this.$toast(t.msg)})},getUserInfo(){this.$http({method:"get",url:"user_info"}).then(t=>{200===t.code?(this.userInfo=t.data,this.name=t.data.name):401===t.code&&this.$toast(t.msg)})},toBindCard(){return this.userInfo.name?this.userInfo.paypassword?void this.$router.push({path:"/BindCard"}):(this.$router.push("SetPayPassword"),this.$toast(this.$t("setting.set_pwd_bank")),!0):(this.$router.push("Setname"),this.$toast(this.$t("setting.set_name_bank")),!0)}},created(){localStorage.getItem("token")?(this.getUserInfo(),this.getUserBankInfo()):this.$router.push({path:"/Login"})}},ta=Ze,ea=(a("3919"),Object(d["a"])(ta,Ye,Qe,!1,null,"e888331c",null)),aa=ea.exports,na=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"container page"},[a("van-nav-bar",{staticClass:"nav-bar",attrs:{title:t.$t("setting.fill_bank")},scopedSlots:t._u([{key:"left",fn:function(){return[a("van-icon",{attrs:{name:"arrow-left",color:"#fff"},on:{click:function(e){return t.back()}}})]},proxy:!0}])}),a("div",{staticClass:"main-box"},[a("div",{staticClass:"label"},[t._v(t._s(t.$t("setting.fill_bank_tip")))]),a("van-cell-group",[a("van-field",{attrs:{label:t.$t("setting.band_name"),readonly:"",placeholder:t.$t("setting.band_name_tip")},on:{click:function(e){return t.showSelectBanks()}},model:{value:t.bank,callback:function(e){t.bank=e},expression:"bank"}}),a("van-field",{attrs:{label:t.$t("setting.band_account"),type:"digit",placeholder:t.$t("setting.band_account_tip")},model:{value:t.bankid,callback:function(e){t.bankid=e},expression:"bankid"}}),a("van-field",{attrs:{label:t.$t("setting.username"),placeholder:t.$t("setting.username_place")},model:{value:t.username,callback:function(e){t.username=e},expression:"username"}}),a("van-field",{attrs:{label:t.$t("setting.mobile"),type:"digit",placeholder:t.$t("setting.mobile_place")},model:{value:t.mobile,callback:function(e){t.mobile=e},expression:"mobile"}})],1),a("p",[t._v(t._s(t.$t("setting.bank_warn")))])],1),a("van-button",{staticClass:"bindCard",attrs:{type:"default"},on:{click:function(e){return t.bindCard()}}},[t._v(t._s(t.$t("setting.bank_ok")))]),a("van-popup",{style:{height:"35%"},attrs:{round:"",position:"bottom"},model:{value:t.showBank,callback:function(e){t.showBank=e},expression:"showBank"}},[a("van-picker",{attrs:{"show-toolbar":"",columns:t.banks,"confirm-button-text":t.$t("setting.ok"),"cancel-button-text":t.$t("setting.cancel")},on:{confirm:t.onConfirm,cancel:t.onCancel}})],1)],1)},ia=[],sa={data(){return{banks:[],showBank:!1,userInfo:{},bankid:"",username:"",mobile:"",bank:"",bank_code:""}},methods:{back(){return window.history.back()},bindCard(){return this.userInfo.bankid?(this.$toast(this.$t("setting.band_account_tip")),!0):""===this.bank||null===this.bank||void 0===this.bank?(this.$toast.fail(this.$t("setting.band_name_tip")),!1):this.userInfo.bankid?(this.$toast(this.$t("setting.band_account_tip")),!0):""===this.username?(this.$toast(this.$t("setting.username_place")),!1):""===this.mobile?(this.$toast(this.$t("setting.mobile_place")),!1):void this.$http({method:"post",data:{bankid:this.bankid,bank:this.bank,bank_code:this.bank_code,username:this.username,mobile:this.mobile},url:"user_set_bank"}).then(t=>{200===t.code?(this.$router.push({path:"/Mine"}),this.$toast(t.msg)):401===t.code&&this.$toast(t.msg)})},showSelectBanks(){this.showBank=!0},getUserInfo(){this.$http({method:"get",url:"user_info"}).then(t=>{200===t.code?(this.userInfo=t.data,this.name=t.data.name):401===t.code&&this.$toast(t.msg)})},getBankList(){this.$http({method:"get",url:"sys_get_banks"}).then(t=>{200===t.code?this.banks=t.data:401===t.code&&this.$toast(t.msg)})},onConfirm(t){this.bank=t.text,this.bank_code=t.value,this.showBank=!1},onCancel(){this.showBank=!1},getUserBankInfo(){this.$http({method:"get",url:"user_get_bank"}).then(t=>{200===t.code?t.data.is_bank?this.is_bind=!0:this.is_bind=!1:401===t.code&&this.$toast(t.msg)})}},created(){localStorage.getItem("token")?(this.getUserInfo(),this.getBankList(),this.getUserBankInfo()):this.$router.push({path:"/Login"})}},oa=sa,ra=(a("4462"),Object(d["a"])(oa,na,ia,!1,null,"9e55b064",null)),la=ra.exports,ca=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"container page"},[a("van-nav-bar",{staticClass:"nav-bar",attrs:{title:t.$t("withdraw.with_center")},scopedSlots:t._u([{key:"left",fn:function(){return[a("van-icon",{attrs:{name:"arrow-left",color:"#fff"},on:{click:function(e){return t.back()}}})]},proxy:!0},{key:"right",fn:function(){return[a("span",{staticClass:"nav-right",on:{click:function(e){return t.$router.push({path:"/WithdrawRecord"})}}},[t._v(t._s(t.$t("withdraw.with_record")))])]},proxy:!0}])}),a("div",{staticClass:"main"},[a("div",{staticClass:"withdrawMoney"},[a("span",[t._v(t._s(t.$t("withdraw.with_money"))+" ("+t._s(t.$t("reservation.unit"))+")")]),a("div",{staticClass:"money"},[a("div",{staticClass:"moneyNumber"},[a("span",{staticClass:"moneyType"},[t._v("VND")]),a("van-field",{attrs:{type:"number"},model:{value:t.withdraw_money,callback:function(e){t.withdraw_money=e},expression:"withdraw_money"}})],1),a("span",{staticClass:"all",on:{click:function(e){return t.allMoeny()}}},[t._v(t._s(t.$t("index.all")))])]),a("div",{staticClass:"information"},[a("div",{staticClass:"description"},[a("van-popover",{attrs:{trigger:"click"},scopedSlots:t._u([{key:"reference",fn:function(){return[a("van-icon",{attrs:{name:"info-o"}}),t._v(" "+t._s(t.$t("withdraw.limit_desc"))+" ")]},proxy:!0}]),model:{value:t.showPopover,callback:function(e){t.showPopover=e},expression:"showPopover"}},[a("div",{staticClass:"popover-body",staticStyle:{padding:"10px"}},[a("p",[t._v("1."+t._s(t.$t("withdraw.single_limit"))+t._s(t.$t("withdraw.low"))+t._s(this.withdrawRole.min)+t._s(t.$t("withdraw.heigh"))+t._s(this.withdrawRole.max)+t._s(t.$t("reservation.unit")))]),a("p",[t._v("2."+t._s(t.$t("withdraw.with_num"))+t._s(this.withdrawRole.num)+t._s(t.$t("withdraw.number")))]),a("p",[t._v("3."+t._s(t.$t("withdraw.with_tip")))])])])],1),a("div",{staticClass:"balance"},[a("span",[t._v(t._s(t.$t("my.balance"))+"：")]),a("span",{staticClass:"number"},[t._v(t._s(this.userInfo.money)+t._s(t.$t("reservation.unit")))])])])]),a("van-button",{staticClass:"withdraw_btn",attrs:{type:"default"},on:{click:function(e){return t.doWithdraw()}}},[t._v(" "+t._s(t.$t("withdraw.immediately_withdraw")))])],1)],1)},da=[],ua={data(){return{showPopover:!1,withdraw_money:"",userInfo:{},withdrawRole:{}}},methods:{back(){return window.history.back()},getUserInfo(){this.$http({method:"get",url:"user_info"}).then(t=>{200===t.code?(this.userInfo=t.data,this.name=t.data.name):401===t.code&&this.$toast(t.msg)})},getUserWithdrawRole(){this.$http({method:"get",url:"user_get_withdraw_role"}).then(t=>{200===t.code?this.withdrawRole=t.data:401===t.code&&this.$toast(t.msg)})},allMoeny(){this.withdraw_money=this.userInfo.money},doWithdraw(){if(this.withdraw_money<=0)return this.$toast(this.$t("setting.correct_money")),!1;this.$http({method:"post",data:{money:this.withdraw_money},url:"user_set_withdraw"}).then(t=>{200===t.code?(location.reload(),this.$toast(t.msg),this.getUserInfo(),this.getUserWithdrawRole()):401===t.code&&this.$toast(t.msg)})},withdrawInfo(){this.showPopover=!0}},created(){localStorage.getItem("token")?(this.getUserInfo(),this.getUserWithdrawRole()):this.$router.push({path:"/Login"})}},ha=ua,ma=(a("bc24"),Object(d["a"])(ha,ca,da,!1,null,"3e44e2ad",null)),pa=ma.exports,ga=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"container page"},[a("div",{staticClass:"header"},[a("van-nav-bar",{staticClass:"nav-bar",attrs:{title:t.$t("my.my_statement")},scopedSlots:t._u([{key:"left",fn:function(){return[a("van-icon",{attrs:{name:"arrow-left",color:"#fff"},on:{click:function(e){return t.back()}}})]},proxy:!0}])}),a("div",{staticClass:"info"},[a("p",{staticClass:"title"},[t._v(t._s(t.$t("withdraw.task_money"))+"("+t._s(t.$t("reservation.unit"))+")")]),a("p",{staticClass:"value"},[t._v(t._s(this.win_money.toFixed(2)))]),a("p",{staticClass:"tip"},[t._v(t._s(t.$t("reservation.win_formula")))])]),a("div",{staticClass:"content"},[a("div",{staticClass:"datalist"},[a("div",{staticClass:"datalistitem"},[a("div",{staticClass:"datalistitemValue"},[t._v(t._s(this.personalreport.play_money))]),a("div",{staticClass:"datalistitemKey"},[t._v(t._s(t.$t("withdraw.task_money")))]),a("div",{staticClass:"datalistitemRightLine"})]),a("div",{staticClass:"datalistitem"},[a("div",{staticClass:"datalistitemValue"},[t._v(t._s(this.personalreport.recharge))]),a("div",{staticClass:"datalistitemKey"},[t._v(t._s(t.$t("withdraw.recharge_money")))]),a("div",{staticClass:"datalistitemRightLine"})]),a("div",{staticClass:"datalistitem"},[a("div",{staticClass:"datalistitemValue"},[t._v(t._s(this.personalreport.withdrawal))]),a("div",{staticClass:"datalistitemKey"},[t._v(t._s(t.$t("withdraw.money")))]),a("div",{staticClass:"datalistitemRightLine"})]),a("div",{staticClass:"datalistitem"},[a("div",{staticClass:"datalistitemValue"},[t._v(t._s(this.personalreport.win_money))]),a("div",{staticClass:"datalistitemKey"},[t._v(t._s(t.$t("withdraw.win_money")))]),a("div",{staticClass:"datalistitemRightLine"})])])])],1)])},_a=[],va={data(){return{win_money:0,personalreport:{}}},methods:{back(){return window.history.back()},getPersonalreport(){this.$http({method:"get",url:"user_get_personalreport"}).then(t=>{200===t.code?(this.personalreport=t.data,this.win_money=this.personalreport.win_money-this.personalreport.play_money):401===t.code&&this.$toast(t.msg)})}},created(){localStorage.getItem("token")?this.getPersonalreport():this.$router.push({path:"/Login"})}},fa=va,ba=(a("29ad"),Object(d["a"])(fa,ga,_a,!1,null,"3b11a32a",null)),ka=ba.exports,ya=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"container page"},[a("van-nav-bar",{staticClass:"nav-bar",attrs:{title:t.$t("my.task_record")},scopedSlots:t._u([{key:"left",fn:function(){return[a("van-icon",{attrs:{name:"arrow-left",color:"#fff"},on:{click:function(e){return t.back()}}})]},proxy:!0}])}),a("div",{staticClass:"main"},[a("van-pull-refresh",{attrs:{"pulling-text":"Sao chép nhanh quy trình thả xuống","loosing-text":"Menyegarkan halaman","loading-text":"Proses pemuatan cepat","success-text":"Proses pemuatan berhasil"},on:{refresh:t.onRefresh},model:{value:t.isLoading,callback:function(e){t.isLoading=e},expression:"isLoading"}},[0===t.list.length?a("van-empty",{attrs:{description:t.$t("withdraw.empty_data")}}):t._l(t.list,(function(e,n){return a("div",{key:n,staticClass:"item_list"},[a("div",{staticClass:"lottery_info"},[a("van-image",{staticClass:"cover",attrs:{src:e.ico},scopedSlots:t._u([{key:"loading",fn:function(){return[a("van-loading",{attrs:{type:"spinner"}})]},proxy:!0}],null,!0)}),a("span",{staticClass:"period-number"},[t._v(t._s(e.expect))]),a("span",{staticClass:"period-number"},[t._v(t._s(e.name))])],1),a("div",{staticClass:"recent"},[a("div",{staticClass:"kuaisan-ball left"},[a("van-image",{staticClass:"res-img",attrs:{src:0===e.status?"img/lottery/open_num.gif":"img/lottery/shaizi/"+e.opencode[0]+".png"},scopedSlots:t._u([{key:"loading",fn:function(){return[a("van-loading",{attrs:{type:"spinner"}})]},proxy:!0}],null,!0)}),a("van-image",{staticClass:"res-img",attrs:{src:0===e.status?"img/lottery/open_num.gif":"img/lottery/shaizi/"+e.opencode[1]+".png"},scopedSlots:t._u([{key:"loading",fn:function(){return[a("van-loading",{attrs:{type:"spinner"}})]},proxy:!0}],null,!0)}),a("van-image",{staticClass:"res-img",attrs:{src:0===e.status?"img/lottery/open_num.gif":"img/lottery/shaizi/"+e.opencode[2]+".png"},scopedSlots:t._u([{key:"loading",fn:function(){return[a("van-loading",{attrs:{type:"spinner"}})]},proxy:!0}],null,!0)}),a("span",{staticClass:"res-des middle"},[t._v(t._s(0===e.status?0:e.opencode[0]+e.opencode[1]+e.opencode[2]))]),a("span",{staticClass:"res-des middle"},[t._v(t._s(0===e.status?0:e.opencode[0]+e.opencode[1]+e.opencode[2]>=11&&e.opencode[0]+e.opencode[1]+e.opencode[2]<=18?t.$t("reservation.big"):t.$t("reservation.samll")))]),a("span",{staticClass:"res-des middle"},[t._v(t._s(0===e.status?0:(e.opencode[0]+e.opencode[1]+e.opencode[2])%2===0?t.$t("reservation.double"):this.$t("reservation.single")))])],1)]),a("div",{staticClass:"topInfo"},[1===e.status?a("span",{staticStyle:{color:"#07c160"}},[t._v(t._s(e.status_text))]):a("span",[t._v(t._s(e.status_text))]),a("span",[t._v(t._s(t.$t("reservation.money"))+"："+t._s(e.money))])]),a("div",{staticClass:"time"},[a("span",[t._v(t._s(t.$t("reservation.order_time"))+"："+t._s(e.create_time))])]),a("div",{staticClass:"time"},[a("span",[t._v(t._s(t.$t("reservation.settle_time"))+"："+t._s(e.update_time))])])])}))],2)],1)],1)},wa=[],$a={data(){return{isLoading:!1,list:[]}},methods:{back(){return window.history.back()},onRefresh(){setTimeout(()=>{this.$toast(this.$t("my.finish_task")),this.isLoading=!1},500)},getUserGameList(){this.$http({method:"get",url:"user_get_game_list"}).then(t=>{200===t.code?(console.log(t.data),this.list=t.data):401===t.code&&this.$toast(t.msg)})}},created(){localStorage.getItem("token")?this.getUserGameList():this.$router.push({path:"/Login"})}},Ca=$a,xa=(a("ea52"),Object(d["a"])(Ca,ya,wa,!1,null,"79252dba",null)),Sa=xa.exports,Ia=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"container page"},[a("div",{staticClass:"header"},[a("van-nav-bar",{staticClass:"nav-bar",attrs:{title:t.$t("withdraw.with_record")},scopedSlots:t._u([{key:"left",fn:function(){return[a("van-icon",{attrs:{name:"arrow-left",color:"#fff"},on:{click:function(e){return t.back()}}})]},proxy:!0}])})],1),a("div",{staticClass:"main"},[a("van-pull-refresh",{attrs:{"pulling-text":"Sao chép nhanh quy trình thả xuống","loosing-text":"Menyegarkan halaman","loading-text":"Proses pemuatan cepat","success-text":"Proses pemuatan berhasil"},on:{refresh:t.onRefresh},model:{value:t.isLoading,callback:function(e){t.isLoading=e},expression:"isLoading"}},[0===t.list.length?a("van-empty",{attrs:{description:t.$t("withdraw.empty_data")}}):t._l(t.list,(function(e,n){return a("div",{key:n,staticClass:"item_list"},[a("div",{staticClass:"topInfo"},[2===e.status||4===e.status?a("span",{staticStyle:{color:"#07c160"}},[t._v(t._s(e.status_text))]):a("span",[t._v(t._s(e.status_text))]),a("span",[t._v(t._s(t.$t("withdraw.money"))+"：-"+t._s(e.money))])]),a("div",{staticClass:"desc"},[a("span",[t._v(t._s(t.$t("withdraw.desc"))+"："+t._s(e.desc))])]),a("div",{staticClass:"time"},[a("span",[t._v(t._s(t.$t("withdraw.submit_time"))+"："+t._s(e.create_time))])]),a("div",{staticClass:"time"},[a("span",[t._v(t._s(t.$t("withdraw.check_time"))+"："+t._s(e.update_time))])])])}))],2)],1)])},La=[],Pa={data(){return{isLoading:!1,list:[]}},methods:{back(){return window.history.back()},onRefresh(){setTimeout(()=>{this.$toast(this.$t("reservation.refresh")),this.isLoading=!1},500)},getUserWithdrawList(){this.$http({method:"get",url:"user_get_withdraw_list"}).then(t=>{200===t.code?this.list=t.data:401===t.code&&this.$toast(t.msg)})}},created(){localStorage.getItem("token")?this.getUserWithdrawList():this.$router.push({path:"/Login"})}},ja=Pa,Ta=(a("390f"),Object(d["a"])(ja,Ia,La,!1,null,"924ccec4",null)),Ba=Ta.exports;n["a"].use(f["a"]);const Va=[{path:"/",redirect:"/Home",component:C,meta:{title:"halaman Depan"}},{path:"/Home",name:"home",component:C,meta:{title:"halaman Depan"}},{path:"/Choose",name:"choose",component:M,meta:{title:"Pilih selir"}},{path:"/List",name:"list",component:A,meta:{title:"Daftar selir terpilih"}},{path:"/Profile",name:"profile",component:G,meta:{title:"Detail pemilihan selir"}},{path:"/Mine",name:"mine",component:j,meta:{title:"milikku"}},{path:"/Video",name:"video",component:et,meta:{title:"video"}},{path:"/Game",name:"game",component:rt,meta:{title:"permainan"}},{path:"/Login",name:"login",component:mt,meta:{title:"Gabung"}},{path:"/Register",name:"register",component:bt,meta:{title:"daftar"}},{path:"/ServiceOnline",name:"ServiceOnline",component:xt,meta:{title:"layanan daring"}},{path:"/ServicePage",name:"ServicePage",component:Tt,meta:{title:"layanan daring"}},{path:"/Setting",name:"Setting",component:Ot,meta:{title:"mempersiapkan"}},{path:"/Infomation",name:"Infomation",component:zt,meta:{title:"Informasi dasar"}},{path:"/Setname",name:"Setname",component:Ft,meta:{title:"Ubah nama"}},{path:"/Setsex",name:"Setsex",component:re,meta:{title:"perubahan gender"}},{path:"/Language",name:"Language",component:ee,meta:{title:"pilihan bahasa"}},{path:"/Recharge",name:"Recharge",component:fe,meta:{title:"isi ulang"}},{path:"/SetPayPassword",name:"SetPayPassword",component:Ce,meta:{title:"Ubah kata sandi penarikan"}},{path:"/SetLoginPassword",name:"SetLoginPassword",component:je,meta:{title:"Kelola kata sandi masuk"}},{path:"/Lottery",name:"Lottery",component:Oe,meta:{title:"Detail lotere"}},{path:"/Notice",name:"Notice",component:ze,meta:{title:"pengumuman"}},{path:"/PlayVideo",name:"PlayVideo",component:Xe,meta:{title:"Halaman pemutaran video"}},{path:"/Setbank",name:"Setbank",component:aa,meta:{title:"Ikat kartu bank"}},{path:"/BindCard",name:"BindCard",component:la,meta:{title:"Ikat kartu bank"}},{path:"/Withdraw",name:"Withdraw",component:pa,meta:{title:"menarik"}},{path:"/Personalreport",name:"Personalreport",component:ka,meta:{title:"laporan pribadi"}},{path:"/WithdrawRecord",name:"WithdrawRecord",component:Ba,meta:{title:"Catatan permainan"}},{path:"/GameRecord",name:"GameRecord",component:Sa,meta:{title:"Pengenalan misi"}}],Na=new f["a"]({routes:Va});Na.beforeEach((t,e,a)=>{document.title=t.matched[0].meta.title,a()});var Ra=Na,Ma=a("bc3a"),Oa=a.n(Ma),Ua={sys_config:"/system/config",sys_get_notice_list:"/notice/getNoticeList",sys_get_banks:"/system/getBankList",video_class:"/video/itemlist",video_get_info:"/video/getVideoInfo",video_get_more_item:"/video/getVideoInfoItem",lottery_class:"/Lottery/itemlist",lottery_hot:"/Lottery/hotLottery",lottery_list:"/Lottery/lotteryList",lottery_get_info:"/Lottery/getLotteryInfo",lottery_get_one_list:"/Lottery/getLotteryOneList",lottery_get_peilv:"/Lottery/getLotteryPeilv",video_list:"/video/getVideoList",member_login:"/member/doLogin",member_register:"/member/doRegister",base_info:"/system/base",user_info:"/member/getUserInfo",user_header_img:"/member/uploadHeaderImg",user_get_bank:"/member/getUserBankInfo",user_get_withdraw_list:"/member/getUserWithdrawList",user_get_game_list:"/member/getUserGameList",user_set_bank:"/member/setBank",user_set_name:"/member/setName",user_set_sex:"/member/setSex",user_set_paypw:"/member/setPayPassword",user_set_loginpw:"/member/setLoginPassword",user_get_withdraw_role:"/member/getUserWithdrawRole",user_get_personalreport:"/member/getPersonalreport",user_set_withdraw:"/member/setUserWirhdraw",game_place_order:"/game/placeOrder",address_list:"/xuanfei/address",xuanfeilist:"/xuanfei/xuanfeilist",xuanfeidata:"/xuanfei/xuanfeidata",recharge:"/member/doRecharge",banks_list:"/bank/index"},Da=a("4328"),Ea=a.n(Da);let Ka=a("e762").Base64;n["a"].use(Y["a"]);const Aa=Oa.a.create({baseURL:"https://wanitapanggilan.life/api",timeout:5e3});async function za(t={}){let e=null;return"get"===t.method||"delete"===t.method?await Aa[t.method](Ua[t.url],{params:t.data}).then(t=>{e=t.data,e.data=JSON.parse(Ka.decode(e.data))}).catch(t=>{e=t}):"post"!==t.method&&"put"!==t.method||await Aa[t.method](Ua[t.url],Ea.a.stringify(t.data)).then(t=>{e=t.data,e.data=JSON.parse(Ka.decode(e.data))}).catch(t=>{e=t}),e}Aa.interceptors.request.use(t=>("post"===t.method&&(t.headers={"content-type":"application/x-www-form-urlencoded;charset=UTF-8"}),localStorage.getItem("token")&&(t.headers={token:Ka.encode(localStorage.getItem("token"))}),t.headers={...t.headers,lang:"idn_yu"},t),t=>{console.error("请求失败",t)}),Aa.interceptors.response.use(t=>("鉴权错误"===t.data.msg&&(localStorage.clear(),(void 0).$router.push({path:"/Login"})),t),t=>Promise.reject(t));var qa=za,Ha=a("7212"),Ja=a.n(Ha),Wa={isLogin:function(t){if(!t.$store.getters.getLoginValue)return t.$router.push("Login")}},Ga=(a("bbe3"),a("2f62"));n["a"].use(Ga["a"]);var Fa=new Ga["a"].Store({state:{userInfo:{},baseInfo:{}},mutations:{setUserInfoValue(t,e){t.userInfo=e},setBaseInfoValue(t,e){t.baseInfo=e}},getters:{getUserInfo:t=>t.userInfo,getBaseInfo:t=>t.baseInfo},actions:{},modules:{}}),Xa=a("a925");a("fda2");n["a"].prototype.$http=qa,n["a"].prototype.common=Wa,n["a"].config.productionTip=!1,n["a"].use(Ja.a),n["a"].use(v["a"]),n["a"].use(Xa["a"]);const Ya=new Xa["a"]({globalInjection:!0,locale:"idn_yu",messages:{yn_yu:a("9ce0"),zh_cn:a("d950"),en_us:a("a635"),es_spa:a("cc60"),ms_my:a("ad6f"),idn_yu:a("4ddd")}});new n["a"]({store:Fa,router:Ra,i18n:Ya,render:t=>t(_)}).$mount("#app")},"577f":function(t,e,a){},"5a98":function(t,e,a){},"5e45":function(t,e,a){"use strict";a("5a98")},6151:function(t,e,a){"use strict";a("1357")},"61c9":function(t,e,a){},6420:function(t,e,a){"use strict";a("8ddf")},"64a1":function(t,e,a){"use strict";a("bf7b")},"650f":function(t,e,a){t.exports=a.p+"img/ms_my.952f085c.png"},"6b7e":function(t,e,a){"use strict";a("f9cb")},7165:function(t,e,a){},7218:function(t,e,a){"use strict";a("5021")},7257:function(t,e,a){},"72fb":function(t,e,a){"use strict";a("48a6")},"74f4":function(t,e,a){},"87e6":function(t,e,a){"use strict";a("e9e0")},"88ca":function(t,e,a){},"89f6":function(t,e,a){"use strict";a("61c9")},"8c36":function(t,e,a){},"8d50":function(t,e,a){},"8ddf":function(t,e,a){},"91e5":function(t,e,a){},9254:function(t,e,a){},"9ce0":function(t){t.exports=JSON.parse('{"auth":{"login":"Đăng nhập","username_place":"Vui lòng nhập tên người dùng","pwd_place":"xin vui lòng nhập mật khẩu","forgetpwd":"Quên mật khẩu","no_account":"Không có tài khoản, đăng ký ngay","register":"đăng ký","invite_code_place":"Vui lòng nhập mã thư mời","agreement_place":"Tôi đã biết và đồng ý với các điều khoản của thỏa thuận mở tài khoản","agreement":"Vui lòng đánh dấu vào thỏa thuận mở tài khoản bên dưới!"},"recharge":{"recharge":"nạp điện","curr_balance":"số dư hiện tại","input_money":"Vui lòng nhập số tiền nạp","pay_way":"Vui lòng chọn phương thức thanh toán"},"foorter":{"index":"trang đầu","subscribe":"dự trữ","video":"băng hình","my":"của tôi"},"my":{"title":"Giải trí Honey Badger","recharge":"NẠP TIỀN","withdraw":"RÚT TIỀN","my_balance":"Túi của tôi","detail":"chi tiết","balance":"THĂNG BẰNG","my_statement":"tuyên bố cá nhân","account_detail":"Chi tiết tài khoản","task_record":"hồ sơ nhiệm vụ","personal_center":"trung tâm cá nhân","information_announcement":"thông báo","online_service":"dịch vụ trực tuyến","finish_task":"Vui lòng hoàn thành danh sách nhiệm vụ và nhập","contact":"kết nối","service_time":"7 * 24 giờ một ngày để phục vụ bạn tận tình","sys_notice":"thông báo hệ thống"},"index":{"task":"Nhiệm vụ được đề xuất","more":"xem thêm","hot":"xếp hạng phổ biến","recmonmand":"khuyến nghị phổ biến","all":"tất cả","loading":"Nhận..."},"reservation":{"hall":"sảnh đặt phòng","refresh":"Làm mới thành công","money_err":"số tiền sai","choose_num":"Hãy chọn một con số!","balance_enough":"Số dư không đủ, vui lòng liên hệ bộ phận chăm sóc khách hàng để nạp tiền!","contact_admin":"Vui lòng liên hệ với quản trị viên để nhận nhiệm vụ này!","prize_succ":"Xổ số thành công, số báo danh:","task_list":"danh sách nhiệm vụ","available_balance":"Số dư khả dụng","counselor":"Vui lòng liên hệ nhân viên tư vấn hoặc lễ tân","clear_order":"thứ tự rõ ràng","submit":"nộp","num":"Vấn đề","win_num":"trúng số","curr_choose":"lựa chọn hiện tại","per_price":"số tiền mỗi lần đặt tặng","price_place":"Vui lòng nhập số tiền","unit":"vnd","total":"tổng cộng","note":"Ghi chú","money":"số tiền đặt cược","order_time":"thời gian phục vụ","settle_time":"thời gian giải quyết","no_choose":"Không được chọn","big":"to lớn","small":"Bé nhỏ","double":"đôi","single":"một","win_formula":"Công thức tính lợi nhuận: số tiền thắng - số tiền nhiệm vụ"},"concubine":{"concubine":"chọn vợ lẽ","city":"bách khoa toàn thư thành phố","city_tip":"Nền tảng này là thiết bị ngoại vi + hộ tống kinh doanh + niềm đam mê thành phố chân thực nhất trong toàn mạng, để đảm bảo quyền riêng tư cá nhân của mỗi người dùng, khách hàng chỉ có thể tham gia bằng cách liên hệ với lễ tân hoặc tên thật của các thành viên cấp cao của nền tảng.","price":"giá xử lý","pri_resource":"Có những tài nguyên nào?","pri_obj":"Người nổi tiếng trên mạng, người mẫu, tiếp viên hàng không, người mẫu trẻ, sinh viên đại học, chỉ có điều bạn không nghĩ ra được và bạn không thể làm được nếu không có nền tảng này","pri_service":"Khu vực phục vụ?","pric_service_one":"Các cuộc hẹn quan hệ tình dục trong cùng một thành phố,  tại bất kỳ địa điểm nào trên toàn quốc, các thành phố cấp một và cấp hai tại địa phương ở Việt Nam và các thành phố cấp ba cũng có thể được sắp xếp bằng cách liên hệ với trực tiếp với nhân viên bên chúng tôi .","pric_service_two":"Nền tảng này là thiết bị ngoại vi + hộ tống kinh doanh + niềm đam mê thành phố chân thực nhất trong toàn mạng, để đảm bảo quyền riêng tư cá nhân của mỗi người dùng, khách hàng chỉ có thể tham gia bằng cách liên hệ với lễ tân hoặc tên thật của các thành viên cấp cao của nền tảng."},"withdraw":{"with_center":"trung tâm rút tiền","with_record":"Hồ sơ rút tiền","with_money":"Số tiền rút","recharge_money":"số tiền nạp","task_money":"số lượng nhiệm vụ","win_money":"số tiền chiến thắng","single_limit":"giới hạn duy nhất","low":"thấp nhất","heigh":"cao nhất","with_num":"Số lần rút tiền: số lần rút cao nhất trong ngày","number":"hạng hai","with_tip":"Thời gian đến: Thời gian đến chung là khoảng 5 phút và thời gian đến nhanh nhất là 2 phút","limit_desc":"mô tả hạn ngạch","immediately_withdraw":"Rút tiền ngay lập tức","empty_data":"dữ liệu trống","money":"số tiền","desc":"Minh họa","submit_time":"thời gian nộp","check_time":"Thời gian xem xét","with_service":"Mật khẩu rút tiền đã được đặt, vui lòng liên hệ bộ phận chăm sóc khách hàng nếu bạn cần sửa đổi mật khẩu"},"video":{"video":"rạp chiếu phim","play":"chơi","no_more":"Không còn nữa.","num_play":"vở kịch","account_out":"tài khoản ngoại tuyến","buy":"Vui lòng nạp tiền và xem video"},"setting":{"setting":"cài đặt","base_setting":"Thông tin cơ bản","login_pwd":"mật khẩu đăng nhập","finance_pwd":"mật khẩu quỹ","language":"sự lựa chọn ngôn ngữ","logout":"đăng xuất","avatar":"hình đại diện","choose_avatar":"Chọn hình đại diện","ok":"Chắc chắn","cancel":"Hủy bỏ","real_name":"tên thật","modify_real_name":"Sửa tên thật","name_place":"vui lòng nhập tên thật của bạn","save":"thay mới","name":"Tên","name_tip":"Để đảm bảo an toàn cho tài khoản của bạn, tên thật cần phải nhất quán với tên trên thẻ ngân hàng bị ràng buộc","repect":"Không lặp lại cài đặt!","sex":"giới tính","sex_place":"sửa đổi giới tính","man":"nam giới","female":"nữ giới","unkown":"không xác định","no_setting":"không được thiết lập","y_setting":"Đã được chuẩn bị","bindinged":"ràng buộc","no":"không có","login_pwd_tip":"Thay đổi mật khẩu đăng nhập","old_pwd":"Mật khẩu cũ","new_pwd":"mật khẩu mới","old_pwd_tip":"Vui lòng nhập mật khẩu cũ của bạn","new_pwd_tip":"Vui lòng nhập mật khẩu mới của bạn","new_again_tip":"Vui lòng nhập lại mật khẩu mới của bạn","prefect":"Vui lòng điền đầy đủ!","pwd_error":"Hai mật khẩu đã nhập không nhất quán","set_money_pwd":"Đặt mật khẩu quỹ","money_place":"Vui lòng nhập mật khẩu quỹ","money_again_place":"Vui lòng nhập lại mật khẩu quỹ","correct_money":"Vui lòng điền số tiền chính xác","contact_recharge":"Vui lòng liên hệ bộ phận chăm sóc khách hàng để nạp tiền","set_bank":"Vui lòng thiết lập một thẻ thanh toán","forbid":"tính năng bị vô hiệu hóa","log_reg":"đăng nhập Đăng ký","more_service":"Đăng nhập để tận hưởng nhiều dịch vụ hơn!","bind_bank_info":"Liên kết thông tin thẻ ngân hàng","bank_info":"thông tin thẻ ngân hàng","add_bank":"thêm thẻ ngân hàng","bind_bank_tip":"Nhắc nhở: Vui lòng liên kết với ngân hàng thương mại lớn. Nếu cần sửa đổi, vui lòng liên hệ bộ phận chăm sóc khách hàng trực tuyến","fill_bank":"Điền vào thẻ thanh toán","fill_bank_tip":"Vui lòng nhập thông tin thẻ thanh toán của bạn","band_account":"số thẻ ngân hàng","set_name_bank":"Xin vui lòng đặt tên trước khi buộc thẻ ngân hàng!","set_pwd_bank":"Vui lòng đặt mật khẩu rút tiền trước khi liên kết thẻ ngân hàng!","band_name":"tên ngân hàng","band_account_tip":"Vui lòng nhập số thẻ ngân hàng thực","band_name_tip":"Vui lòng chọn một ngân hàng","bank_warn":"Kính gửi người dùng, để đảm bảo an toàn cho tiền của bạn, vui lòng liên kết tên thật của bạn và đặt mật khẩu rút tiền. Nếu tên của bạn không khớp với tên tài khoản, bạn sẽ không thể rút tiền","bank_ok":"OK để liên kết thẻ","username":"Tên","username_place":"vui lòng nhập tên thật của bạn","mobile":"Số điện thoại","mobile_place":"Vui lòng nhập số điện thoại của bạn"}}')},a635:function(t){t.exports=JSON.parse('{"auth":{"login":"Login","username_place":"please enter user name","pwd_place":"Please enter password","forgetpwd":"Forgot password","no_account":"No account, register now","register":"register","invite_code_place":"Please enter the invitation code","agreement_place":"I have known and agreed to the terms of the account opening agreement","agreement":"Please tick the account opening agreement below!"},"recharge":{"recharge":"Recharge","curr_balance":"current balance","input_money":"Please enter the recharge amount","pay_way":"Please select the payment method"},"foorter":{"index":"index","subscribe":"reserve","video":"video","my":"mine"},"my":{"title":"Honey Badger Entertainment","recharge":"recharge","withdraw":"withdraw","my_balance":"My purse","detail":"Details","balance":"balance","my_statement":"personal statement","account_detail":"Account details","task_record":"Mission record","personal_center":"personal center","information_announcement":"announcement","online_service":"online service","finish_task":"Please enter after completing the task list","contact":"connect","service_time":"7 * 24 hours a day to serve you wholeheartedly","sys_notice":"system notification"},"index":{"task":"Recommended tasks","more":"see more","hot":"Popularity Ranking","recmonmand":"Popular recommendation","all":"all","loading":"loading......"},"reservation":{"hall":"Reservation hall","refresh":"Refresh successfully","money_err":"wrong amount","choose_num":"Please choose a number!","balance_enough":"Insufficient balance, please contact customer service to recharge!","contact_admin":"Please contact the administrator for this task!","prize_succ":"The lottery draw is successful, the issue number:","task_list":"mission list","available_balance":"Available Balance","counselor":"Please contact an advisor or receptionist","clear_order":"Clear order","submit":"submit","num":"Issue","win_num":"Winning numbers","curr_choose":"current selection","per_price":"Amount per bet","price_place":"Please enter the amount","unit":"Yuan","total":"total","note":"Note","money":"bet amount","order_time":"order time","settle_time":"settlement time","no_choose":"Unselected","big":"Big","small":"Small","double":"pair","single":"one","win_formula":"Profit Calculation Formula: Winning Amount - Task Amount"},"concubine":{"concubine":"Concubine","city":"City Encyclopedia","city_tip":"This platform is the most authentic peripheral + business escorts + passion in the same city. In order to ensure the personal privacy of each user, customers can only join by contacting the receptionist or the real-name recommendation of senior members of the platform.","price":"Process price","pri_resource":"What resources are there?","pri_obj":"Internet celebrities, models, flight attendants, young models, college students, only you can\'t think of it, and you can\'t do it without this platform","pri_service":"Service area?","pric_service_one":"Free appointments in the same city, airborne at any location across the country, local first- and second-tier cities in China, and third-tier cities can also make appointments by contacting the receptionist.","pric_service_two":"This platform is the most authentic peripheral + business escorts + passion in the same city. In order to ensure the personal privacy of each user, customers can only join by contacting the receptionist or the real-name recommendation of senior members of the platform."},"withdraw":{"with_center":"Withdrawal","with_record":"Detail","with_money":"Withdrawal Amount","recharge_money":"Recharge amount","task_money":"task amount","win_money":"winning amount","single_limit":"single limit","low":"lowest","heigh":"Highest","with_num":"Number of withdrawals: maximum withdrawal in one day","number":"Second-rate","with_tip":"Arrival time: Generally, the arrival time is about 5 minutes, and the fastest arrival time is 2 minutes.","limit_desc":"Limit description","immediately_withdraw":"Withdraw immediately","empty_data":"data is empty","money":"Amount","desc":"illustrate","submit_time":"submission time","check_time":"Review time","with_service":"The withdrawal password has been set. If you need to modify it, please contact customer service."},"video":{"video":"video theater","play":"play","no_more":"no more","num_play":"plays","account_out":"Account offline","buy":"Please recharge and watch the video"},"setting":{"setting":"set up","base_setting":"Basic Information","login_pwd":"Login Password","finance_pwd":"Fund password","language":"language selection","logout":"sign out","avatar":"avatar","choose_avatar":"Choose Avatar","ok":"Sure","cancel":"Cancel","real_name":"actual name","modify_real_name":"Edit real name","name_place":"please enter your real name","save":"save","name":"name","name_tip":"For the security of your account, the real name needs to be the same as the name of the bound bank card","repect":"Do not repeat the settings!","sex":"gender","sex_place":"gender modification","man":"male","female":"Female","unkown":"Unknown","no_setting":"not set","y_setting":"Has been set","bindinged":"bound","no":"none","login_pwd_tip":"Modify login password","old_pwd":"Old Password","new_pwd":"new password","old_pwd_tip":"Please enter your old password","new_pwd_tip":"Please enter your new password","new_again_tip":"Please enter your new password again","prefect":"Please fill in completely!","pwd_error":"Inconsistent password entered twice","set_money_pwd":"Set up a fund password","money_place":"Please enter the fund password","money_again_place":"Please enter the fund password again","correct_money":"Please fill in the correct amount","contact_recharge":"Please contact customer service to recharge","set_bank":"Please set up a payment card","forbid":"feature disabled","log_reg":"log in Register","more_service":"Sign in to enjoy more services!","bind_bank_info":"Bind bank card information","bank_info":"bank card information","add_bank":"Add a bank card","bind_bank_tip":"Tip: Please bind a large commercial bank, if you need to modify, please contact online customer service","fill_bank":"Fill in the payment card","fill_bank_tip":"Please enter your payment card information","band_account":"Bank card number","set_name_bank":"Please set your name before binding your bank card!","set_pwd_bank":"Please set the withdrawal password before binding the bank card!","band_name":"Bank name","band_account_tip":"Please enter real bank card number","band_name_tip":"Please select a bank","bank_warn":"Dear users, in order to ensure the safety of your funds, please bind your real name and set a withdrawal password. If the name is inconsistent with the account name, you will not be able to withdraw money","bank_ok":"Make sure to bind the card","username":"Name","username_place":"please enter your real name","mobile":"Phone number","mobile_place":"Please type your phone number"}}')},ad6f:function(t){t.exports=JSON.parse('{"auth":{"login":"Log masuk","username_place":"sila masukkan nama pengguna","pwd_place":"Sila masukkan kata laluan","forgetpwd":"Lupa kata laluan","no_account":"Tiada akaun, daftar sekarang","register":"mendaftar","invite_code_place":"Sila masukkan kod jemputan","agreement_place":"Saya telah mengetahui dan bersetuju dengan syarat perjanjian pembukaan akaun","agreement":"Sila tandakan perjanjian pembukaan akaun di bawah!"},"recharge":{"recharge":"cas semula","curr_balance":"baki terkini","input_money":"Sila masukkan jumlah caj semula","pay_way":"Sila pilih kaedah pembayaran"},"foorter":{"index":"Rumah","subscribe":"simpanan","video":"video","my":"saya"},"my":{"title":"Hiburan Honey Badger","recharge":"cas semula","withdraw":"menarik diri","my_balance":"Dompet saya","detail":"Butiran","balance":"seimbang","my_statement":"kenyataan peribadi","account_detail":"Butiran akaun","task_record":"Rekod misi","personal_center":"pusat peribadi","information_announcement":"pengumuman","online_service":"perkhidmatan dalam talian","finish_task":"Sila masukkan selepas melengkapkan senarai tugas","contact":"menyambung","service_time":"7 * 24 jam sehari untuk melayani anda dengan sepenuh hati","sys_notice":"pemberitahuan sistem"},"index":{"task":"Tugasan yang disyorkan","more":"lihat lagi","hot":"Kedudukan Populariti","recmonmand":"Cadangan popular","all":"semua","loading":"Mendapat......"},"reservation":{"hall":"Dewan tempahan","refresh":"Segar semula berjaya","money_err":"jumlah yang salah","choose_num":"Sila pilih nombor!","balance_enough":"Baki tidak mencukupi, sila hubungi perkhidmatan pelanggan untuk mengecas semula!","contact_admin":"Sila hubungi pentadbir untuk tugasan ini!","prize_succ":"Loteri berjaya, nombor keluaran：","task_list":"senarai misi","available_balance":"Baki yang ada","counselor":"Sila hubungi penasihat atau penyambut tetamu","clear_order":"Perintah yang jelas","submit":"serahkan","num":"Isu","win_num":"Nombor menang","curr_choose":"pemilihan semasa","per_price":"Amaun setiap pertaruhan","price_place":"Sila masukkan jumlah","unit":"Yuan","total":"jumlah","note":"Catatan","money":"jumlah pertaruhan","order_time":"masa pesanan","settle_time":"masa penyelesaian","no_choose":"Tidak dipilih","big":"Besar","small":"Kecil","double":"sepasang","single":"satu","win_formula":"Formula Pengiraan Untung: Amaun Menang - Amaun Tugas"},"concubine":{"concubine":"gundik","city":"Ensiklopedia Bandar","city_tip":"Platform ini adalah persisian + pengiring perniagaan + semangat yang paling tulen di bandar yang sama. Bagi memastikan privasi peribadi setiap pengguna, pelanggan hanya boleh menyertainya dengan menghubungi penyambut tetamu atau cadangan nama sebenar ahli kanan platform.","price":"Harga proses","pri_resource":"Apakah sumber yang ada?","pri_obj":"Selebriti Internet, model, pramugari, model muda, pelajar kolej, hanya anda tidak boleh memikirkannya, dan anda tidak boleh melakukannya tanpa platform ini","pri_service":"Kawasan servis?","pric_service_one":"Janji temu percuma di bandar yang sama, melalui udara di mana-mana lokasi di seluruh negara, bandar peringkat pertama dan kedua tempatan di China, dan bandar peringkat ketiga juga boleh membuat janji temu dengan menghubungi penyambut tetamu。","pric_service_two":"Platform ini adalah persisian + pengiring perniagaan + semangat yang paling tulen di bandar yang sama. Bagi memastikan privasi peribadi setiap pengguna, pelanggan hanya boleh menyertainya dengan menghubungi penyambut tetamu atau cadangan nama sebenar ahli kanan platform.。"},"withdraw":{"with_center":"Pusat Pengeluaran","with_record":"Rekod pengeluaran","with_money":"Jumlah Pengeluaran","recharge_money":"Jumlah cas semula","task_money":"jumlah tugas","win_money":"jumlah kemenangan","single_limit":"had tunggal","low":"paling rendah","heigh":"Tertinggi","with_num":"Bilangan pengeluaran: pengeluaran maksimum dalam satu hari","number":"Kadar kedua","with_tip":"Masa ketibaan: Secara umumnya, masa ketibaan adalah kira-kira 5 minit, dan masa ketibaan terpantas ialah 2 minit.","limit_desc":"Hadkan perihalan","immediately_withdraw":"Tarik balik serta merta","empty_data":"data kosong","money":"jumlah","desc":"menggambarkan","submit_time":"masa penyerahan","check_time":"Masa semakan","with_service":"Kata laluan pengeluaran telah ditetapkan. Jika anda perlu mengubah suainya, sila hubungi perkhidmatan pelanggan."},"video":{"video":"teater video","play":"bermain","no_more":"tiada lagi","num_play":"bermain","account_out":"Akaun luar talian","buy":"Sila cas semula dan tonton video"},"setting":{"setting":"sediakan","base_setting":"Maklumat asas","login_pwd":"kata laluan log masuk","finance_pwd":"Kata laluan dana","language":"pemilihan bahasa","logout":"log keluar","avatar":"avatar","choose_avatar":"Pilih Avatar","ok":"pasti","cancel":"Batal","real_name":"nama sebenar","modify_real_name":"Edit nama sebenar","name_place":"sila masukkan nama sebenar anda","save":"jimat","name":"Nama","name_tip":"Untuk keselamatan akaun anda, nama sebenar perlu sama dengan nama kad bank yang terikat","repect":"Jangan ulangi tetapan!","sex":"jantina","sex_place":"pengubahsuaian jantina","man":"lelaki","female":"perempuan","unkown":"tidak diketahui","no_setting":"tidak ditetapkan","y_setting":"Telah ditetapkan","bindinged":"terikat","no":"tiada","login_pwd_tip":"Ubah suai kata laluan log masuk","old_pwd":"Kata laluan lama","new_pwd":"kata laluan baharu","old_pwd_tip":"Sila masukkan kata laluan lama anda","new_pwd_tip":"Sila masukkan kata laluan baharu anda","new_again_tip":"Sila masukkan kata laluan baharu anda sekali lagi","prefect":"Sila isi dengan lengkap!","pwd_error":"Kata laluan tidak konsisten dimasukkan dua kali","set_money_pwd":"Sediakan kata laluan dana","money_place":"Sila masukkan kata laluan dana","money_again_place":"Sila masukkan kata laluan dana sekali lagi","correct_money":"Sila isikan jumlah yang betul","contact_recharge":"Sila hubungi perkhidmatan pelanggan untuk mengecas semula","set_bank":"Sila sediakan kad pembayaran","forbid":"ciri dilumpuhkan","log_reg":"log masuk Daftar","more_service":"Log masuk untuk menikmati lebih banyak perkhidmatan!","bind_bank_info":"Ikat maklumat kad bank","bank_info":"maklumat kad bank","add_bank":"Tambah kad bank","bind_bank_tip":"Petua: Sila ikat bank perdagangan yang besar, jika anda perlu mengubah suai, sila hubungi perkhidmatan pelanggan dalam talian","fill_bank":"Isi kad pembayaran","fill_bank_tip":"Sila masukkan maklumat kad pembayaran anda","band_account":"Nombor kad bank","set_name_bank":"Sila tetapkan nama anda sebelum mengikat kad bank anda!","set_pwd_bank":"Sila tetapkan kata laluan pengeluaran sebelum mengikat kad bank!","band_name":"nama bank","band_account_tip":"Sila masukkan nombor kad bank sebenar","band_name_tip":"Sila pilih bank","bank_warn":"Pengguna yang dihormati, untuk memastikan keselamatan dana anda, sila ikat nama sebenar anda dan tetapkan kata laluan pengeluaran. Jika nama itu tidak konsisten dengan nama akaun, anda tidak akan dapat mengeluarkan wang","bank_ok":"Pastikan untuk mengikat kad","username":"Nama","username_place":"sila masukkan nama sebenar anda","mobile":"Nombor telefon","mobile_place":"Sila taip nombor telefon anda"}}')},aeb3:function(t,e,a){"use strict";a("bab5")},b4cb:function(t,e,a){"use strict";a("88ca")},bab5:function(t,e,a){},bc24:function(t,e,a){"use strict";a("3b7d")},bd49:function(t,e,a){"use strict";a("1476")},bf7b:function(t,e,a){},c835:function(t,e,a){"use strict";a("91e5")},c8ca:function(t,e,a){},cc60:function(t){t.exports=JSON.parse('{"auth":{"login":"Iniciar sesión","username_place":"por favor ingrese el nombre de usuario","pwd_place":"Por favor, ingrese contraseña","forgetpwd":"Se te olvidó tu contraseña","no_account":"Sin cuenta, regístrate ahora","register":"Registrarse","invite_code_place":"Por favor, introduzca el código de invitación","agreement_place":"He conocido y estoy de acuerdo con los términos del acuerdo de apertura de cuenta","agreement":"¡Marque el acuerdo de apertura de cuenta a continuación!"},"recharge":{"recharge":"recargar","curr_balance":"saldo actual","input_money":"Por favor ingrese el monto de la recarga","pay_way":"Por favor seleccione el método de pago"},"foorter":{"index":"Inicio","subscribe":"reserva","video":"video","my":"mía"},"my":{"title":"Entretenimiento de tejón de miel","recharge":"recargar","withdraw":"retirar","my_balance":"My bolso","detail":"Detalles","balance":"balance","my_statement":"declaración personal","account_detail":"Detalles de la cuenta","task_record":"Registro de la misión","personal_center":"centro personal","information_announcement":"anuncio","online_service":"Servicio en línea","finish_task":"Ingrese después de completar la lista de tareas","contact":"Contacto","service_time":"7 * 24 horas al día para servirle de todo corazón","sys_notice":"notificación del sistema"},"index":{"task":"Tarea recomendada","more":"ver más","hot":"Clasificación de popularidad","recmonmand":"Recomendación popular","all":"todos","loading":"Consiguiendo......"},"reservation":{"hall":"Sala de reservas","refresh":"Actualizar con éxito","money_err":"cantidad incorrecta","choose_num":"¡Elige un número!","balance_enough":"Saldo insuficiente, comuníquese con el servicio al cliente para recargar.","contact_admin":"Póngase en contacto con el administrador para esta tarea!","prize_succ":"La lotería tiene éxito, el número de emisión：","task_list":"lista de misiones","available_balance":"Saldo disponible","counselor":"Por favor, póngase en contacto con un asesor o recepcionista.","clear_order":"Orden claro","submit":"enviar","num":"Tema","win_num":"Numeros ganadores","curr_choose":"selección actual","per_price":"Cantidad por apuesta","price_place":"Por favor ingrese la cantidad","unit":"MEX","total":"total","note":"Nota","money":"monto de la apuesta","order_time":"tiempo de la orden","settle_time":"tiempo de liquidación","no_choose":"no seleccionado","big":"Grande","small":"Pequeña","double":"par","single":"una","win_formula":"Fórmula de cálculo de ganancias: Monto ganador - Monto de la tarea"},"concubine":{"concubine":"Concubina","city":"Enciclopedia de la ciudad","city_tip":"Esta plataforma es la más auténtica periferia + business escorts + pasión en una misma ciudad.Para garantizar la privacidad personal de cada usuario, los clientes solo pueden unirse contactando a la recepcionista o la recomendación de nombre real de los miembros senior de la plataforma.","price":"Precio del proceso","pri_resource":"que recursos hay?","pri_obj":"Celebridades de Internet, modelos, asistentes de vuelo, modelos jóvenes, estudiantes universitarios, solo que no puedes pensar en eso, y no puedes hacerlo sin esta plataforma.","pri_service":"Área de servicio?","pric_service_one":"Las citas gratuitas en la misma ciudad, en el aire en cualquier lugar del país, las ciudades locales de primer y segundo nivel en China y las ciudades de tercer nivel también pueden programar citas comunicándose con la recepcionista.","pric_service_two":"Esta plataforma es la más auténtica periferia + business escorts + pasión en una misma ciudad.Para garantizar la privacidad personal de cada usuario, los clientes solo pueden unirse contactando a la recepcionista o la recomendación de nombre real de los miembros senior de la plataforma."},"withdraw":{"with_center":"Centro de retiro","with_record":"registro de retiros","with_money":"Cantidad de retiro","recharge_money":"Cantidad de recarga","task_money":"cantidad de tarea","win_money":"cantidad ganadora","single_limit":"límite único","low":"más bajo","heigh":"más alto","with_num":"Número de retiros: retiro máximo en un día","number":"de segunda categoría","with_tip":"Hora de llegada: generalmente, la hora de llegada es de aproximadamente 5 minutos y la hora de llegada más rápida es de 2 minutos.","limit_desc":"Descripción del límite","immediately_withdraw":"retirar inmediatamente","empty_data":"los datos estan vacios","money":"Cantidad","desc":"ilustrar","submit_time":"tiempo de presentación","check_time":"Tiempo de revisión","with_service":"Se ha establecido la contraseña de retiro. Si necesita modificarla, comuníquese con el servicio al cliente."},"video":{"video":"video teatro","play":"desempeñar","no_more":"no más","num_play":"obras de teatro","account_out":"Cuenta sin conexión","buy":"Por favor recarga y mira el video"},"setting":{"setting":"configurar","base_setting":"Información básica","login_pwd":"contraseña de inicio de sesión","finance_pwd":"Contraseña del fondo","language":"Selección de idioma","logout":"desconectar","avatar":"avatar","choose_avatar":"elegir avatar","ok":"Por supuesto","cancel":"Cancelar","real_name":"nombre real","modify_real_name":"Editar nombre real","name_place":"por favor ingrese su nombre real","save":"ahorrar","name":"Nombre","name_tip":"Para la seguridad de su cuenta, el nombre real debe ser el mismo que el nombre de la tarjeta bancaria vinculada.","repect":"¡No repita los ajustes!","sex":"género","sex_place":"modificación de género","man":"masculino","female":"Femenino","unkown":"desconocido","no_setting":"no establecido","y_setting":"Ha sido establecido","bindinged":"vinculado","no":"ninguna","login_pwd_tip":"Modificar contraseña de inicio de sesión","old_pwd":"Contraseña anterior","new_pwd":"Nueva contraseña","old_pwd_tip":"Por favor ingrese su antigua contraseña","new_pwd_tip":"Por favor ingrese su nueva contraseña","new_again_tip":"Por favor ingrese su nueva contraseña nuevamente","prefect":"¡Por favor complete completamente!","pwd_error":"Contraseña inconsistente ingresada dos veces","set_money_pwd":"Configurar una contraseña de fondo","money_place":"Por favor ingrese la contraseña del fondo","money_again_place":"Por favor, introduzca la contraseña del fondo de nuevo","correct_money":"Por favor complete la cantidad correcta","contact_recharge":"Comuníquese con el servicio al cliente para recargar","set_bank":"Configure una tarjeta de pago","forbid":"característica deshabilitada","log_reg":"Iniciar sesión Registrarse","more_service":"¡Inicia sesión para disfrutar de más servicios!","bind_bank_info":"Enlazar la información de la tarjeta bancaria","bank_info":"información de la tarjeta bancaria","add_bank":"Añadir una tarjeta bancaria","bind_bank_tip":"Consejo: vincule un gran banco comercial, si necesita modificar, comuníquese con el servicio al cliente en línea","fill_bank":"Llena la tarjeta de pago","fill_bank_tip":"Por favor ingrese la información de su tarjeta de pago","band_account":"número de tarjeta bancaria","set_name_bank":"¡Establezca su nombre antes de vincular su tarjeta bancaria!","set_pwd_bank":"¡Establezca la contraseña de retiro antes de vincular la tarjeta bancaria!","band_name":"Nombre del banco","band_account_tip":"Ingrese el número de tarjeta bancaria real","band_name_tip":"Seleccione un banco","bank_warn":"Estimados usuarios, para garantizar la seguridad de sus fondos, vincule su nombre real y establezca una contraseña de retiro. Si el nombre no coincide con el nombre de la cuenta, no podrá retirar dinero.","bank_ok":"Asegúrate de encuadernar la tarjeta.","username":"Nombre","username_place":"por favor ingrese su nombre real","mobile":"Número de teléfono","mobile_place":"Por favor escriba su número de teléfono"}}')},d1d7:function(t,e,a){"use strict";a("0f8b")},d1fe:function(t,e,a){},d435:function(t,e,a){t.exports=a.p+"img/zh_cn.2e0fd9e9.png"},d950:function(t){t.exports=JSON.parse('{"auth":{"login":"登录","username_place":"请输入用户名","pwd_place":"请输入密码","forgetpwd":"忘记密码","no_account":"没有账号，马上注册","register":"注册","invite_code_place":"请输入邀请码","agreement_place":"我已经知晓并同意开户协议各项条约","agreement":"请勾选下方开户协议！"},"recharge":{"recharge":"充值","curr_balance":"当前余额","input_money":"请输入充值金额","pay_way":"请选择支付方式"},"foorter":{"index":"首页","subscribe":"预约","video":"视频","my":"我的"},"my":{"title":"韩国空降","recharge":"充值","withdraw":"提现","my_balance":"我的钱包","detail":"详情","balance":"余额","my_statement":"个人报表","account_detail":"账户明细","task_record":"任务记录","personal_center":"个人中心","information_announcement":"信息公告","online_service":"在线客服","finish_task":"请完成任务单后进入","contact":"联系","service_time":"全天7 * 24小时竭诚为您服务","sys_notice":"系统公告"},"index":{"task":"推荐任务","more":"查看更多","hot":"人气排行","recmonmand":"热门推荐","all":"全部","loading":"获取中......"},"reservation":{"hall":"预约大厅","refresh":"刷新成功","money_err":"金额错误","choose_num":"请选号！","balance_enough":"余额不足，请联系客服充值！","contact_admin":"请联系管理员领取该任务!","prize_succ":"开奖成功，期号：","task_list":"任务单","available_balance":"可用余额","counselor":"请联系顾问或接待员","clear_order":"清空订单","submit":"提交","num":"期号","win_num":"开奖号码","curr_choose":"当前选号","per_price":"每注金额","price_place":"请输入金额","unit":"元","total":"总共","note":"注","money":"下注金额","order_time":"下单时间","settle_time":"结算时间","no_choose":"未选中","big":"大","small":"小","double":"双","single":"单","win_formula":"盈利计算公式 : 中奖金额 - 任务金额"},"concubine":{"concubine":"选妃","city":"城市大全","city_tip":"本平台全网最真实外围+商务伴游+同城激情，为保证每个用户的个人隐私，客户仅限通过联系接待员或平台资深会员实名推荐才可加入","price":"流程价格","pri_resource":"有哪些资源?","pri_obj":"网红、模特、空姐、嫩模、大学生，只有您想不到，没有本平台办不到","pri_service":"服务范围?","pric_service_one":"同城免费约炮，任意地点全国空降，国内一二线城市当地均有，三线城市也可通过联系接待员预约安排。","pric_service_two":"本平台全网最真实外围+商务伴游+同城激情，为保证每个用户的个人隐私，客户仅限通过联系接待员或平台资深会员实名推荐才可加入。"},"withdraw":{"with_center":"提现中心","with_record":"提现记录","with_money":"提现金额","recharge_money":"充值金额","task_money":"任务金额","win_money":"中奖金额","single_limit":"单笔限额","low":"最低","heigh":"最高","with_num":"提现次数：一天最高提现","number":"次","with_tip":"到账时间：一般到账时间在5分钟左右，最快2分钟内到账","limit_desc":"限额说明","immediately_withdraw":"立马提现","empty_data":"数据为空","money":"金额","desc":"说明","submit_time":"提交时间","check_time":"审核时间","with_service":"提现密码已设置，需要修改请联系客服"},"video":{"video":"视频影院","play":"播放","no_more":"没有更多了","num_play":"次播放","account_out":"账号下线","buy":"请充值后观看视频"},"setting":{"setting":"设置","base_setting":"基本信息","login_pwd":"登录密码","finance_pwd":"资金密码","language":"语言选择","logout":"退出登录","avatar":"头像","choose_avatar":"选择头像","ok":"确定","cancel":"取消","real_name":"真实姓名","modify_real_name":"修改真实姓名","name_place":"请输入真实姓名","save":"保存","name":"姓名","name_tip":"为了您账户安全,真实姓名需要与绑定银行卡姓名一致","repect":"请勿重复设置！","sex":"性别","sex_place":"性别修改","man":"男","female":"女","unkown":"未知","no_setting":"未设置","y_setting":"已设置","bindinged":"已绑定","no":"无","login_pwd_tip":"修改登录密码","old_pwd":"旧密码","new_pwd":"新密码","old_pwd_tip":"请输入您的旧密码","new_pwd_tip":"请输入您的新密码","new_again_tip":"请再次输入您的新密码","prefect":"请填写完整！","pwd_error":"两次密码输入不一致","set_money_pwd":"设置资金密码","money_place":"请输入资金密码","money_again_place":"请再次输入资金密码","correct_money":"请填写正确的金额","contact_recharge":"请联系客服充值","set_bank":"请设置收款卡","forbid":"功能已禁用","log_reg":"登录/注册","more_service":"登录可享受更多服务！","bind_bank_info":"绑定银行卡信息","bank_info":"银行卡信息","add_bank":"添加银行卡","bind_bank_tip":"提示:请绑定大型商业银行,如需修改,请您联系在线客服","fill_bank":"填写收款卡","fill_bank_tip":"请输入您的收款卡信息","band_account":"银行卡号","set_name_bank":"请设置姓名后再绑定银行卡！","set_pwd_bank":"请设置提现密码后再绑定银行卡！","band_name":"银行名称","band_account_tip":"请输入真实银行卡号","band_name_tip":"请选择银行","bank_warn":"尊敬的用户,为了保障您的资金安全,请您绑定您的真实姓名和设置取款密码,如果姓名与开户名不一致,将无法取款","bank_ok":"确定绑卡","username":"姓名","username_place":"请输入真实姓名","mobile":"手机号","mobile_place":"请输入联系电话"}}')},dac5:function(t,e,a){},dc71:function(t,e,a){"use strict";a("4804")},dea8:function(t,e,a){t.exports=a.p+"img/yn_yu.68ecca54.png"},e9e0:function(t,e,a){},ea52:function(t,e,a){"use strict";a("377e")},eae9:function(t,e,a){"use strict";a("9254")},f044:function(t,e,a){"use strict";a("dac5")},f30b:function(t,e,a){},f873:function(t,e,a){},f940:function(t,e,a){t.exports=a.p+"img/en_us.fc5456b0.png"},f9cb:function(t,e,a){}});
//# sourceMappingURL=app.c87eb52a.js.map