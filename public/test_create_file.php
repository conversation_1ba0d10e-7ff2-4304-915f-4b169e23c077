<?php
// 测试在lottery/banner目录下创建文件
header('Content-Type: text/html; charset=utf-8');

echo "<h2>测试文件创建</h2>";

$testDir = __DIR__ . '/lottery/banner/20250629';
$testFile = $testDir . '/test_file.txt';
$testImage = $testDir . '/test_image.jpg';

echo "<p><strong>测试目录:</strong> {$testDir}</p>";
echo "<p><strong>测试文件:</strong> {$testFile}</p>";

// 确保目录存在
if (!is_dir($testDir)) {
    if (mkdir($testDir, 0755, true)) {
        echo "<p style='color: green;'>目录创建成功</p>";
    } else {
        echo "<p style='color: red;'>目录创建失败</p>";
    }
} else {
    echo "<p>目录已存在</p>";
}

// 创建测试文件
$content = "这是一个测试文件，创建时间：" . date('Y-m-d H:i:s');
if (file_put_contents($testFile, $content)) {
    echo "<p style='color: green;'>测试文件创建成功</p>";
    echo "<p>文件大小: " . filesize($testFile) . " 字节</p>";
    echo "<p>文件权限: " . substr(sprintf('%o', fileperms($testFile)), -4) . "</p>";
    
    // 测试访问
    $webPath = '/lottery/banner/20250629/test_file.txt';
    echo "<p><a href='{$webPath}' target='_blank'>访问测试文件</a></p>";
} else {
    echo "<p style='color: red;'>测试文件创建失败</p>";
}

// 创建一个简单的测试图片（1x1像素的PNG）
$pngData = base64_decode('iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==');
if (file_put_contents($testImage, $pngData)) {
    echo "<p style='color: green;'>测试图片创建成功</p>";
    $webImagePath = '/lottery/banner/20250629/test_image.jpg';
    echo "<p><a href='{$webImagePath}' target='_blank'>访问测试图片</a></p>";
    echo "<img src='{$webImagePath}' style='border: 1px solid #ccc;' alt='测试图片' />";
} else {
    echo "<p style='color: red;'>测试图片创建失败</p>";
}

// 检查目录权限
echo "<h3>目录权限检查</h3>";
$dirs = [
    __DIR__,
    __DIR__ . '/lottery',
    __DIR__ . '/lottery/banner',
    $testDir
];

foreach ($dirs as $dir) {
    if (is_dir($dir)) {
        echo "<p>{$dir}: " . substr(sprintf('%o', fileperms($dir)), -4) . " (可写: " . (is_writable($dir) ? '是' : '否') . ")</p>";
    } else {
        echo "<p>{$dir}: 不存在</p>";
    }
}
?>
