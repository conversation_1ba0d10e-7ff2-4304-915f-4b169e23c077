<?php
// 调试上传问题
header('Content-Type: text/html; charset=utf-8');

echo "<h2>上传调试工具</h2>";

// 检查今天的banner目录
$today = date('Ymd');
$bannerPath = __DIR__ . '/lottery/banner/' . $today;
$webPath = '/lottery/banner/' . $today;

echo "<h3>目录检查</h3>";
echo "<p>今天日期: {$today}</p>";
echo "<p>Banner目录: {$bannerPath}</p>";
echo "<p>Web路径: {$webPath}</p>";
echo "<p>目录是否存在: " . (is_dir($bannerPath) ? '是' : '否') . "</p>";

if (is_dir($bannerPath)) {
    echo "<p>目录权限: " . substr(sprintf('%o', fileperms($bannerPath)), -4) . "</p>";
    
    $files = scandir($bannerPath);
    echo "<h4>目录中的文件:</h4>";
    foreach ($files as $file) {
        if ($file != '.' && $file != '..') {
            $filePath = $bannerPath . '/' . $file;
            $webFilePath = $webPath . '/' . $file;
            echo "<div style='border: 1px solid #ccc; margin: 10px; padding: 10px;'>";
            echo "<p><strong>文件名:</strong> {$file}</p>";
            echo "<p><strong>完整路径:</strong> {$filePath}</p>";
            echo "<p><strong>Web路径:</strong> {$webFilePath}</p>";
            echo "<p><strong>文件大小:</strong> " . filesize($filePath) . " 字节</p>";
            echo "<p><strong>文件权限:</strong> " . substr(sprintf('%o', fileperms($filePath)), -4) . "</p>";
            echo "<p><strong>MIME类型:</strong> " . mime_content_type($filePath) . "</p>";
            
            // 尝试显示图片
            echo "<p><strong>图片预览:</strong></p>";
            echo "<img src='{$webFilePath}' style='max-width: 200px; border: 1px solid #ddd;' onerror=\"this.style.display='none'; this.nextSibling.style.display='block';\" />";
            echo "<p style='display:none; color:red;'>图片加载失败</p>";
            
            // 提供直接访问链接
            echo "<p><a href='{$webFilePath}' target='_blank'>直接访问图片</a></p>";
            echo "</div>";
        }
    }
} else {
    echo "<p style='color: red;'>目录不存在，可能上传失败或路径错误</p>";
}

// 检查最近几天的目录
echo "<h3>最近的上传目录</h3>";
$bannerBaseDir = __DIR__ . '/lottery/banner';
if (is_dir($bannerBaseDir)) {
    $dirs = scandir($bannerBaseDir);
    rsort($dirs); // 按日期倒序
    $count = 0;
    foreach ($dirs as $dir) {
        if ($dir != '.' && $dir != '..' && is_dir($bannerBaseDir . '/' . $dir) && $count < 5) {
            echo "<p>{$dir} - 文件数量: " . (count(scandir($bannerBaseDir . '/' . $dir)) - 2) . "</p>";
            $count++;
        }
    }
}

// 显示PHP配置信息
echo "<h3>PHP配置信息</h3>";
echo "<p>上传最大文件大小: " . ini_get('upload_max_filesize') . "</p>";
echo "<p>POST最大大小: " . ini_get('post_max_size') . "</p>";
echo "<p>内存限制: " . ini_get('memory_limit') . "</p>";
echo "<p>文件上传是否启用: " . (ini_get('file_uploads') ? '是' : '否') . "</p>";

// 显示服务器信息
echo "<h3>服务器信息</h3>";
echo "<p>服务器软件: " . $_SERVER['SERVER_SOFTWARE'] . "</p>";
echo "<p>文档根目录: " . $_SERVER['DOCUMENT_ROOT'] . "</p>";
echo "<p>当前脚本目录: " . __DIR__ . "</p>";
?>
