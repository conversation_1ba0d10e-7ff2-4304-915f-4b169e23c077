<?php
// 简单的上传测试
header('Content-Type: text/html; charset=utf-8');

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['test_file'])) {
    echo "<h2>上传测试结果</h2>";
    
    $file = $_FILES['test_file'];
    echo "<h3>文件信息</h3>";
    echo "<pre>" . print_r($file, true) . "</pre>";
    
    if ($file['error'] === UPLOAD_ERR_OK) {
        $uploadDir = __DIR__ . '/lottery/banner/test/';
        $webDir = '/lottery/banner/test/';
        
        // 确保目录存在
        if (!is_dir($uploadDir)) {
            mkdir($uploadDir, 0755, true);
        }
        
        $fileName = date('YmdHis') . '_' . $file['name'];
        $targetPath = $uploadDir . $fileName;
        $webPath = $webDir . $fileName;
        
        echo "<h3>路径信息</h3>";
        echo "<p><strong>上传目录:</strong> {$uploadDir}</p>";
        echo "<p><strong>目标文件:</strong> {$targetPath}</p>";
        echo "<p><strong>Web路径:</strong> {$webPath}</p>";
        echo "<p><strong>目录是否存在:</strong> " . (is_dir($uploadDir) ? '是' : '否') . "</p>";
        echo "<p><strong>目录是否可写:</strong> " . (is_writable($uploadDir) ? '是' : '否') . "</p>";
        
        if (move_uploaded_file($file['tmp_name'], $targetPath)) {
            echo "<p style='color: green;'><strong>上传成功！</strong></p>";
            echo "<p><strong>文件大小:</strong> " . filesize($targetPath) . " 字节</p>";
            echo "<p><strong>文件权限:</strong> " . substr(sprintf('%o', fileperms($targetPath)), -4) . "</p>";
            echo "<p><a href='{$webPath}' target='_blank'>访问上传的文件</a></p>";
            
            if (strpos($file['type'], 'image/') === 0) {
                echo "<h3>图片预览</h3>";
                echo "<img src='{$webPath}' style='max-width: 300px; border: 1px solid #ccc;' />";
            }
        } else {
            echo "<p style='color: red;'><strong>上传失败！</strong></p>";
            echo "<p>可能的原因：</p>";
            echo "<ul>";
            echo "<li>目录权限不足</li>";
            echo "<li>磁盘空间不足</li>";
            echo "<li>临时文件已被删除</li>";
            echo "</ul>";
        }
    } else {
        echo "<p style='color: red;'>上传错误代码: {$file['error']}</p>";
    }
    
    echo "<hr><a href='test_upload.php'>返回上传页面</a>";
} else {
?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>上传测试</title>
</head>
<body>
    <h2>文件上传测试</h2>
    <form method="post" enctype="multipart/form-data">
        <p>
            <label>选择文件:</label><br>
            <input type="file" name="test_file" accept="image/*" required>
        </p>
        <p>
            <button type="submit">上传测试</button>
        </p>
    </form>
    
    <h3>系统信息</h3>
    <p><strong>文档根目录:</strong> <?php echo $_SERVER['DOCUMENT_ROOT']; ?></p>
    <p><strong>当前脚本目录:</strong> <?php echo __DIR__; ?></p>
    <p><strong>上传最大文件大小:</strong> <?php echo ini_get('upload_max_filesize'); ?></p>
    <p><strong>POST最大大小:</strong> <?php echo ini_get('post_max_size'); ?></p>
    <p><strong>临时目录:</strong> <?php echo sys_get_temp_dir(); ?></p>
    <p><strong>临时目录可写:</strong> <?php echo is_writable(sys_get_temp_dir()) ? '是' : '否'; ?></p>
</body>
</html>
<?php
}
?>
