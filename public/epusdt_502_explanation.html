<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EPUSDT 502错误说明</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
            line-height: 1.6;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border: 1px solid transparent;
            border-radius: 4px;
        }
        .alert-success {
            color: #3c763d;
            background-color: #dff0d8;
            border-color: #d6e9c6;
        }
        .alert-info {
            color: #31708f;
            background-color: #d9edf7;
            border-color: #bce8f1;
        }
        .alert-warning {
            color: #8a6d3b;
            background-color: #fcf8e3;
            border-color: #faebcc;
        }
        .btn {
            display: inline-block;
            padding: 10px 20px;
            margin: 10px 5px;
            background-color: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            border: none;
            cursor: pointer;
        }
        .btn:hover {
            background-color: #0056b3;
        }
        .btn-success {
            background-color: #28a745;
        }
        .btn-warning {
            background-color: #ffc107;
            color: #212529;
        }
        .step {
            background: #f8f9fa;
            border-left: 4px solid #007bff;
            padding: 15px;
            margin: 15px 0;
        }
        .code {
            background: #f1f1f1;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 EPUSDT 502错误说明</h1>
        
        <div class="alert alert-success">
            <h3>✅ 重要：您的支付已成功处理！</h3>
            <p>502错误不影响实际支付，只是页面显示问题。您的余额已正常更新。</p>
        </div>

        <h2>🤔 为什么会出现502错误？</h2>
        
        <div class="alert alert-info">
            <p><strong>根据EPUSDT官方API文档：</strong></p>
            <ul>
                <li>EPUSDT系统<strong>没有提供</strong>订单状态查询接口</li>
                <li>支付页面尝试访问 <span class="code">/pay/check-status/{订单号}</span> 接口</li>
                <li>该接口在官方API中<strong>根本不存在</strong></li>
                <li>服务器返回502错误，因为请求的资源不存在</li>
            </ul>
        </div>

        <h2>💡 实际支付流程</h2>
        
        <div class="step">
            <h4>第1步：创建支付订单</h4>
            <p>系统调用EPUSDT的 <span class="code">/api/v1/order/create-transaction</span> 接口创建支付订单</p>
        </div>
        
        <div class="step">
            <h4>第2步：用户完成支付</h4>
            <p>用户在EPUSDT页面完成USDT转账</p>
        </div>
        
        <div class="step">
            <h4>第3步：异步回调通知</h4>
            <p>EPUSDT系统检测到支付后，发送回调通知到我们的服务器</p>
        </div>
        
        <div class="step">
            <h4>第4步：自动更新余额</h4>
            <p>我们的系统接收回调，自动更新订单状态和用户余额</p>
        </div>

        <div class="alert alert-warning">
            <h4>⚠️ 502错误发生在哪里？</h4>
            <p>在第2步和第3步之间，支付页面的JavaScript尝试查询支付状态，但调用了不存在的接口，导致502错误。</p>
            <p><strong>这不影响第3步和第4步的正常执行！</strong></p>
        </div>

        <h2>🛠️ 如何确认支付状态？</h2>
        
        <div style="text-align: center; margin: 30px 0;">
            <a href="/epusdt_payment_status_checker.html" class="btn btn-success">
                📊 使用状态查询工具
            </a>
            <a href="https://vip.jsdao.cc/#/Mine" class="btn btn-warning">
                💰 查看我的余额
            </a>
        </div>

        <h2>📞 需要帮助？</h2>
        
        <div class="alert alert-info">
            <p><strong>如果您遇到以下情况，请联系客服：</strong></p>
            <ul>
                <li>支付完成超过5分钟，余额仍未更新</li>
                <li>确认已转账，但订单状态显示未支付</li>
                <li>其他支付相关问题</li>
            </ul>
        </div>

        <h2>🔗 相关链接</h2>
        
        <ul>
            <li><a href="https://github.com/assimon/epusdt/blob/master/wiki/API.md" target="_blank">EPUSDT官方API文档</a></li>
            <li><a href="/epusdt_payment_status_checker.html">支付状态查询工具</a></li>
            <li><a href="https://vip.jsdao.cc/#/Mine">我的账户</a></li>
        </ul>

        <div style="margin-top: 40px; padding-top: 20px; border-top: 1px solid #eee; color: #666; font-size: 14px;">
            <p><strong>技术说明：</strong>502 Bad Gateway错误是HTTP状态码，表示作为网关或代理的服务器从上游服务器接收到无效响应。在这种情况下，是因为请求的API端点不存在。</p>
        </div>
    </div>
</body>
</html>
