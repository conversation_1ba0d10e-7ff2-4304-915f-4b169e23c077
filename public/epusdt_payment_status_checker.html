<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EPUSDT支付状态检查器</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
            background: #f9f9f9;
        }
        .status-success {
            border-color: #4CAF50;
            background-color: #e8f5e8;
        }
        .status-pending {
            border-color: #ff9800;
            background-color: #fff3e0;
        }
        .status-failed {
            border-color: #f44336;
            background-color: #ffebee;
        }
        .btn {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background-color: #0056b3;
        }
        .btn-success {
            background-color: #28a745;
        }
        .btn-warning {
            background-color: #ffc107;
            color: #212529;
        }
        .btn-danger {
            background-color: #dc3545;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .form-control {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border: 1px solid transparent;
            border-radius: 4px;
        }
        .alert-info {
            color: #31708f;
            background-color: #d9edf7;
            border-color: #bce8f1;
        }
        .alert-warning {
            color: #8a6d3b;
            background-color: #fcf8e3;
            border-color: #faebcc;
        }
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 10px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>EPUSDT支付状态检查器</h1>
        
        <div class="alert alert-info">
            <strong>关于502错误：</strong>根据EPUSDT官方文档，系统没有提供状态检查接口。502错误是因为支付页面尝试访问不存在的接口导致的，这不影响实际支付处理。您可以使用此工具检查真实的支付状态。
        </div>

        <div class="form-group">
            <label for="orderNo">订单号：</label>
            <input type="text" id="orderNo" class="form-control" placeholder="请输入订单号，例如：R2025062917511508">
        </div>

        <div class="form-group">
            <label for="userToken">用户Token：</label>
            <input type="text" id="userToken" class="form-control" placeholder="请输入用户Token（base64编码的用户ID）">
        </div>

        <div>
            <button class="btn" onclick="checkOrderStatus()">
                <span id="checkLoading" style="display: none;" class="loading"></span>
                检查订单状态
            </button>
            <button class="btn btn-warning" onclick="startAutoCheck()">开始自动检查</button>
            <button class="btn btn-danger" onclick="stopAutoCheck()">停止自动检查</button>
        </div>

        <div id="statusResult" class="status-card" style="display: none;">
            <h3>订单状态</h3>
            <div id="statusContent"></div>
        </div>

        <div class="alert alert-warning">
            <strong>502错误的真实原因：</strong><br>
            根据EPUSDT官方API文档，系统并没有提供 <code>/pay/check-status/</code> 接口。
            502错误是因为支付页面的JavaScript代码尝试调用这个不存在的接口导致的。<br>
            <strong>重要：</strong>这不影响实际支付处理！支付成功后，EPUSDT会发送异步回调通知，我们的系统会自动更新订单状态和用户余额。
        </div>

        <h3>操作日志</h3>
        <div id="logContainer" class="log"></div>
    </div>

    <script>
        let autoCheckInterval = null;
        let checkCount = 0;

        function log(message) {
            const logContainer = document.getElementById('logContainer');
            const timestamp = new Date().toLocaleTimeString();
            logContainer.innerHTML += `[${timestamp}] ${message}\n`;
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        function showLoading(show) {
            document.getElementById('checkLoading').style.display = show ? 'inline-block' : 'none';
        }

        async function checkOrderStatus() {
            const orderNo = document.getElementById('orderNo').value.trim();
            const userToken = document.getElementById('userToken').value.trim();

            if (!orderNo) {
                alert('请输入订单号');
                return;
            }

            if (!userToken) {
                alert('请输入用户Token');
                return;
            }

            showLoading(true);
            log(`开始检查订单状态: ${orderNo}`);

            try {
                const response = await fetch('/api/recharge/getOrderStatus', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'token': userToken
                    },
                    body: JSON.stringify({
                        order_no: orderNo
                    })
                });

                const result = await response.json();
                log(`API响应: ${JSON.stringify(result)}`);

                if (result.code === 200) {
                    displayOrderStatus(result.data);
                } else {
                    log(`错误: ${result.message}`);
                    alert(`查询失败: ${result.message}`);
                }
            } catch (error) {
                log(`请求失败: ${error.message}`);
                alert(`网络错误: ${error.message}`);
            } finally {
                showLoading(false);
            }
        }

        function displayOrderStatus(order) {
            const statusResult = document.getElementById('statusResult');
            const statusContent = document.getElementById('statusContent');
            
            let statusClass = 'status-pending';
            let statusText = order.status_text;
            let statusIcon = '⏳';

            if (order.status === 2) {
                statusClass = 'status-success';
                statusIcon = '✅';
            } else if (order.status === 3) {
                statusClass = 'status-failed';
                statusIcon = '❌';
            }

            statusResult.className = `status-card ${statusClass}`;
            statusResult.style.display = 'block';

            statusContent.innerHTML = `
                <div style="font-size: 18px; margin-bottom: 10px;">
                    ${statusIcon} ${statusText}
                </div>
                <div><strong>订单号：</strong>${order.order_no}</div>
                <div><strong>金额：</strong>${order.amount} USDT</div>
                <div><strong>创建时间：</strong>${order.create_time}</div>
                <div><strong>支付时间：</strong>${order.pay_time || '未支付'}</div>
            `;

            log(`订单状态: ${statusText}`);

            if (order.status === 2) {
                log('✅ 支付成功！');
                stopAutoCheck();
                alert('支付成功！您的余额已更新。');
            } else if (order.status === 3) {
                log('❌ 支付失败');
                stopAutoCheck();
            }
        }

        function startAutoCheck() {
            if (autoCheckInterval) {
                stopAutoCheck();
            }

            const orderNo = document.getElementById('orderNo').value.trim();
            if (!orderNo) {
                alert('请先输入订单号');
                return;
            }

            checkCount = 0;
            log('开始自动检查订单状态（每10秒检查一次）');
            
            autoCheckInterval = setInterval(() => {
                checkCount++;
                log(`自动检查第${checkCount}次`);
                checkOrderStatus();

                // 最多检查30次（5分钟）
                if (checkCount >= 30) {
                    stopAutoCheck();
                    log('自动检查已达到最大次数，请手动检查');
                }
            }, 10000);
        }

        function stopAutoCheck() {
            if (autoCheckInterval) {
                clearInterval(autoCheckInterval);
                autoCheckInterval = null;
                log('已停止自动检查');
            }
        }

        // 页面加载时的初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('支付状态检查器已加载');
            
            // 从URL参数获取订单号
            const urlParams = new URLSearchParams(window.location.search);
            const orderNo = urlParams.get('order_no');
            if (orderNo) {
                document.getElementById('orderNo').value = orderNo;
                log(`从URL获取订单号: ${orderNo}`);
            }
        });

        // 页面关闭时清理定时器
        window.addEventListener('beforeunload', function() {
            stopAutoCheck();
        });
    </script>
</body>
</html>
