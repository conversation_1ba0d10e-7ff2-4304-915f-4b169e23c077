<?php
// 临时文件访问脚本
$path = $_GET["path"] ?? "";
if (empty($path)) {
    http_response_code(400);
    echo "Missing path parameter";
    exit;
}

// 安全检查：防止目录遍历攻击
$path = str_replace(["../", "..\"], "", $path);
$fullPath = __DIR__ . "/" . $path;

if (!file_exists($fullPath)) {
    http_response_code(404);
    echo "File not found";
    exit;
}

// 检查文件类型
$mimeTypes = [
    "jpg" => "image/jpeg",
    "jpeg" => "image/jpeg", 
    "png" => "image/png",
    "gif" => "image/gif",
    "ico" => "image/x-icon"
];

$extension = strtolower(pathinfo($fullPath, PATHINFO_EXTENSION));
$mimeType = $mimeTypes[$extension] ?? "application/octet-stream";

// 输出文件
header("Content-Type: " . $mimeType);
header("Content-Length: " . filesize($fullPath));
header("Cache-Control: public, max-age=31536000");
readfile($fullPath);
?>