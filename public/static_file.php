<?php
/**
 * 静态文件访问脚本
 * 临时解决nginx配置问题
 */

// 获取请求的文件路径
$requestUri = $_SERVER["REQUEST_URI"] ?? "";
$path = parse_url($requestUri, PHP_URL_PATH);

// 移除开头的斜杠
$path = ltrim($path, "/");

// 安全检查：防止目录遍历攻击
if (strpos($path, "..") !== false || strpos($path, "\") !== false) {
    http_response_code(403);
    echo "Access denied";
    exit;
}

// 构建文件路径
$filePath = __DIR__ . "/" . $path;

// 检查文件是否存在
if (!file_exists($filePath) || !is_file($filePath)) {
    http_response_code(404);
    echo "File not found";
    exit;
}

// 检查文件类型
$allowedExtensions = ["jpg", "jpeg", "png", "gif", "ico", "css", "js", "pdf", "txt"];
$extension = strtolower(pathinfo($filePath, PATHINFO_EXTENSION));

if (!in_array($extension, $allowedExtensions)) {
    http_response_code(403);
    echo "File type not allowed";
    exit;
}

// 设置MIME类型
$mimeTypes = [
    "jpg" => "image/jpeg",
    "jpeg" => "image/jpeg",
    "png" => "image/png", 
    "gif" => "image/gif",
    "ico" => "image/x-icon",
    "css" => "text/css",
    "js" => "application/javascript",
    "pdf" => "application/pdf",
    "txt" => "text/plain"
];

$mimeType = $mimeTypes[$extension] ?? "application/octet-stream";

// 输出文件
header("Content-Type: " . $mimeType);
header("Content-Length: " . filesize($filePath));
header("Cache-Control: public, max-age=31536000"); // 缓存1年
header("Expires: " . gmdate("D, d M Y H:i:s", time() + 31536000) . " GMT");

readfile($filePath);
?>