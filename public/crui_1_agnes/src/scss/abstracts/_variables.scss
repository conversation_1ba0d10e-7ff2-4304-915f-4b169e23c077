// --------------------------------------------
// Colors -------------------------------------
// Usage example: color(primary, main)
// --------------------------------------------
$color: (
	typography: (
		1: #1F2B35,
		2: #6F8394,
		1i: #FFF,
		2i: #6F8394
	),
	bg: (
		1: #FFFFFF,
		2: #F6F8FA,
		3: #E2E8ED
	),
	primary: (
		1: #0081F6,
		2: #44A6FF,
		3: #0066C3,
 	),
 	secondary: (
		1: #FF4D79,
		2: #FF809F,
		3: #FF1A53
 	),
 	additional: (
		1: #5FFAD0,
		2: #A9FCE6,
		3: #2EF8C1
 	)
);

// --------------------------------------------
// Typography ---------------------------------
// --------------------------------------------
$font__family: (
	base: '"Hind Vadodara", sans-serif', // font-family(base)
	heading: '"Mukta", sans-serif', // font-family(heading)
	code: 'Monaco, Consolas, "Andale Mono", "DejaVu Sans Mono", monospace', // font-family(code)
	pre: '"Courier 10 Pitch", Courier, monospace' // font-family(pre)
);

$font__sizes: (
	alpha:   ( 56px, 66px, -0.1px ), // font-size, line-height, kerning (use '0' if don't want to output any kerning)
	beta:    ( 42px, 52px, -0.1px ),
	gamma:   ( 36px, 46px, -0.1px ),
	delta:   ( 24px, 34px, -0.1px ),
	epsilon: ( 20px, 30px, -0.1px ),
	zeta:    ( 18px, 27px, -0.1px ),
	eta:     ( 16px, 24px, -0.1px ),
	theta:   ( 14px, 20px, 0px )
);

$font__scale: (
	desktop: (                             // i.e. $breakpoint__m + $breakpoint__l (600 - 1024)
		1: map-get($font__sizes, alpha),   // H1
		2: map-get($font__sizes, beta),    // H2
		3: map-get($font__sizes, gamma),   // H3
		4: map-get($font__sizes, delta),   // H4, H5, H6
		5: map-get($font__sizes, epsilon), // Body
		6: map-get($font__sizes, zeta),    // Text small (e.g. features description)
		7: map-get($font__sizes, eta),     // Text smaller (e.g. pricing table's lists), buttons
		8: map-get($font__sizes, theta)    // Footer area
	),
	mobile: (                              // i.e. $breakpoint__xs + $breakpoint__s (up to 600)
		1: map-get($font__sizes, beta),    // H1
		2: map-get($font__sizes, gamma),   // H2
		3: map-get($font__sizes, delta),   // H3
		4: map-get($font__sizes, epsilon), // H4, H5, H6
		5: map-get($font__sizes, zeta),    // Body
		6: map-get($font__sizes, zeta),    // Text small (e.g. features description)
		7: map-get($font__sizes, eta),     // Text smaller (e.g. pricing table's lists), buttons
		8: map-get($font__sizes, theta)    // Footer area
	)
);

$font__weight: (
	regular: 400, 	// font__weight(regular)
	medium: 500,	// font__weight(medium)
	semibold: 600,	// font__weight(semibold)
	bold: 700		// font__weight(bold)
);

// --------------------------------------------
// Structure ----------------------------------
// --------------------------------------------
$content__padding: (
	mobile: 16px,
	desktop:  24px
);
$container__width: 1080px;
$container__width-sm: 800px;
