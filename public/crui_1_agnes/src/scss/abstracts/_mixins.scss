// Font-size + Line-height + Kerning
// Usage: @include font-size(1, mobile)
// Add more true/false args to control what to output: font-size, line-height, kerning
@mixin font-size($size, $elem, $font-size: true, $line-height: false, $kerning: false, $adjust-font-size: 0) {
	@if not map-has-key(map-get($font__scale, $elem), $size) {
		@warn "'#{$size}' key does not exist in array!";
	}
	@if ( $font-size != false ) {
		font-size: get-font-size($size, $elem) + $adjust-font-size;
	}
	@if ( $line-height == true ) {
		line-height: get-line-height($size, $elem);
	}
	@if ( $kerning == true ) {
		letter-spacing: get-kerning($size, $elem);
	}
}

// Font Family
@mixin font-family($elem) {
	font-family: unquote(get-font-family($elem));
}

// Font Weight
@mixin font-weight($elem) {
	font-weight: get-font-weight($elem);
}

// Anchor aspect
@mixin anchor-aspect($type: 'main') {
	@if ($type == 'main') { // Base
		color: inherit;
		text-decoration: underline;

		&:hover,
		&:active {
			outline: 0;
			text-decoration: none;
		}
	} @else if ($type == 'header') {
		color: color(typography, 2i);
		text-transform: uppercase;
		text-decoration: none;

		&:hover,
		&:active {
			color: color(typography, 1i);
		}
	} @else if ($type == 'footer') {
		color: color(typography, 3);
		text-decoration: none;

		&:hover,
		&:active {
			color: color(typography, 2);
			text-decoration: underline;
		}
	}
}

@mixin shadow {
	box-shadow: 0 16px 48px color(bg, 3);

	/* Edge fallback */
	@supports(-ms-ime-align:auto) {
		box-shadow: 0 16px 48px rgba(color(typography, 1), .12);
	}
}

@mixin divider-mix {
	display: block;
	height: 1px;
	background: color(bg, 3);
}

@mixin divider($type: false) {
	@if ( $type == 'before' ) {
		position: relative;

		&::before {
			content: '';
			position: absolute;
			top: 0;
			left: 0;
			width: 100%;
			@include divider-mix;
		}
	} @else if ($type == 'after') {
		position: relative;

		&::after {
			content: '';
			position: absolute;
			bottom: 0;
			left: 0;
			width: 100%;
			@include divider-mix;
		}
	} @else {
		@include divider-mix;
	}
}
