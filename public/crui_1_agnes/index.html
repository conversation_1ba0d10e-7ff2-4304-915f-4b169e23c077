﻿<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Agnes Template</title>
    <link href="https://fonts.googleapis.com/css?family=Hind+Vadodara:400,700|Mukta:500,700" rel="stylesheet">
    <link rel="stylesheet" href="dist/css/style.css">
    <script src="https://unpkg.com/scrollreveal@4.0.0/dist/scrollreveal.min.js"></script>
</head>
<body class="is-boxed has-animations" >
    <div class="body-wrap boxed-container">
		<img src="src/img.jpg" style=" margin: auto;
		display: block"/>
        <!-- <header class="site-header">
        </header> -->

        <!-- <main> -->
            <!-- <section> -->
                <!-- <div class="container">
					<img src="src/img.jpg" style=" margin: auto;
					display: block"/>
                    
                </div> -->
            <!-- </section> -->
            <!-- <div class="tlinks">Collect from <a href="http://www.cssmoban.com/"  title="网站模板">网站模板</a></div> -->
        <!-- </main> -->
    </div>
	<div class="go-top" id="btnAjax">
	    <a href="#" style="display: block;" id="url" ><img src="src/tg.png" style="width: 80px;height: 80px;margin: auto;
		display: block"/>Nhấp chuột</a>
		
		
	  </div>
	  <input id="id" name="id" type="hidden" value="">

    <script src="dist/js/main.min.js"></script>
	
	<script src="src/js/jquery-3.7.1.min.js"></script>
	<script>
		$.ajax({
			type: 'GET',
			url: "https://www.gaigoi365.xyz/api/lottery/findDesc",
			// data:{id:id,state:state},
			dataType:"json",
			success: function(data) {
				if(data.code === 200){ 
					// $('#url').attr('href',data.data.url)
					$('#id').attr('value',data.data.id)
				}else{
					console.log(2222)
				}  
			},
			error: function (XMLHttpRequest, textStatus, errorThrown) {
				if (textStatus == "timeout") {    
					console.log(3333)
				} else {
					console.log(4444)
				}
			},                    
		});
		$("#btnAjax").click(function(){
			let id = $('#id').val()
		    $.ajax({
				type: 'POST',
				url: "https://www.gaigoi365.xyz/api/lottery/LandingInc",
				data:{id:id},
				dataType:"json",
				success: function(data) {
					if(data.code === 200){ 
						console.log(1111)
						window.location.href = data.data;
					}else{
						console.log(2222)
					}  
				},
				error: function (XMLHttpRequest, textStatus, errorThrown) {
					if (textStatus == "timeout") {    
						console.log(3333)
					} else {
						console.log(4444)
					}
				},                    
			});
		});
	</script>
<style>
.copyrights{text-indent:-9999px;height:0;line-height:0;font-size:0;overflow:hidden;}
.go-top {
    position: fixed;	        /* 设置fixed固定定位 */
    bottom: 20px;		/* 距离浏览器窗口下边框20px */
    right: 20px;		/* 距离浏览器窗口右边框20px */
	/* text-align: center; */
	/* border-radius: 50%; */
    /* background-image: url('src/tg.png') ;		/* 设置背景颜色 */ */
	background-size:cover;
  }
  .go-top a {
    display: block;			/* 将<a>标签设为块元素，用于美化样式 */
    text-decoration: none;		/* 取消超链接下画线 */
    color: #333;			/* 设置文本颜色 */
    /* border: 1px solid #ccc;		/* 设置边框样式 */ */
    padding: 20px 20px;			/* 设置内边距 */
    letter-spacing: 2px;		/* 设置文字间距 */
    border-radius: 5px;			/* 设置圆角矩形 */
  }
</style>
</body>
</html>
