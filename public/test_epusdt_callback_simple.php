<?php
/**
 * 简单的EPUSDT回调测试页面
 * 直接访问: https://jsdao.cc/test_epusdt_callback_simple.php
 */

// 记录所有请求信息
$log_data = [
    'timestamp' => date('Y-m-d H:i:s'),
    'method' => $_SERVER['REQUEST_METHOD'] ?? 'UNKNOWN',
    'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'UNKNOWN',
    'ip' => $_SERVER['REMOTE_ADDR'] ?? 'UNKNOWN',
    'input_data' => file_get_contents('php://input'),
    'get_params' => $_GET,
    'post_params' => $_POST,
    'headers' => getallheaders()
];

// 写入日志文件
$log_file = __DIR__ . '/epusdt_callback_test.log';
file_put_contents($log_file, json_encode($log_data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n\n", FILE_APPEND);

// 输出响应
header('Content-Type: text/plain');
echo "EPUSDT回调测试页面\n";
echo "时间: " . date('Y-m-d H:i:s') . "\n";
echo "请求方法: " . ($_SERVER['REQUEST_METHOD'] ?? 'UNKNOWN') . "\n";
echo "IP地址: " . ($_SERVER['REMOTE_ADDR'] ?? 'UNKNOWN') . "\n";
echo "User-Agent: " . ($_SERVER['HTTP_USER_AGENT'] ?? 'UNKNOWN') . "\n";
echo "输入数据: " . file_get_contents('php://input') . "\n";
echo "GET参数: " . json_encode($_GET) . "\n";
echo "POST参数: " . json_encode($_POST) . "\n";
echo "\n已记录到日志文件: " . $log_file . "\n";
echo "\n如果这是EPUSDT回调，应该返回: ok\n";

// 如果是POST请求且包含回调数据，返回ok
if ($_SERVER['REQUEST_METHOD'] === 'POST' && !empty(file_get_contents('php://input'))) {
    echo "ok";
} else {
    echo "test_page_accessible";
}
?>
