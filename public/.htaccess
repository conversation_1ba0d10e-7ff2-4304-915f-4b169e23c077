<IfModule mod_rewrite.c>
 RewriteEngine on
 RewriteBase /

 # 强制HTTPS重定向
 RewriteCond %{HTTPS} off
 RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

 RewriteCond %{REQUEST_FILENAME} !-d
 RewriteCond %{REQUEST_FILENAME} !-f
 RewriteRule ^(.*)$ index.php?s=/$1 [QSA,PT,L]
</IfModule>

# 文件上传配置
<IfModule mod_php.c>
    php_value upload_max_filesize 2048M
    php_value post_max_size 2048M
    php_value max_execution_time 300
    php_value max_input_time 300
    php_value memory_limit 512M
</IfModule>

# 设置请求体大小限制（Apache 2.4+）
<IfModule mod_reqtimeout.c>
    RequestReadTimeout header=300,MinRate=500 body=300,MinRate=500
</IfModule>