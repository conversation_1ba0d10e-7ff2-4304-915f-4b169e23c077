<!DOCTYPE html>
<html>
<head>
    <title>选妃素材验证逻辑测试</title>
    <meta charset="utf-8">
    <script src="https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js"></script>
</head>
<body>
    <h1>选妃素材验证逻辑测试</h1>
    
    <h2>测试场景</h2>
    <div id="test-results"></div>
    
    <script>
    // 模拟验证逻辑
    function validateImages() {
        var hasFemaleImages = false;
        var hasMaleImages = false;
        
        // 检查女生素材
        $("input[name='pc_src[]']").each(function(){
            var pc_src = $(this).val();
            if(pc_src != "" && pc_src != null && pc_src != undefined){
                hasFemaleImages = true;
            }
        });
        
        // 检查男生素材
        $("input[name='male_images[]']").each(function(){
            var male_img = $(this).val();
            if(male_img != "" && male_img != null && male_img != undefined){
                hasMaleImages = true;
            }
        });
        
        // 至少需要上传一种素材
        if(!hasFemaleImages && !hasMaleImages){
            return {success: false, message: "请至少上传女生素材或男生素材中的一种"};
        }
        
        return {success: true, message: "验证通过"};
    }
    
    // 测试用例
    function runTests() {
        var results = [];
        
        // 清空测试区域
        $('body').find('input[type="hidden"]').remove();
        
        // 测试1: 没有任何素材
        results.push({
            name: "测试1: 没有任何素材",
            result: validateImages()
        });
        
        // 测试2: 只有女生素材
        $('body').append('<input type="hidden" name="pc_src[]" value="/uploads/female1.jpg">');
        results.push({
            name: "测试2: 只有女生素材",
            result: validateImages()
        });
        
        // 清空并测试3: 只有男生素材
        $('body').find('input[type="hidden"]').remove();
        $('body').append('<input type="hidden" name="male_images[]" value="/uploads/male1.jpg">');
        results.push({
            name: "测试3: 只有男生素材",
            result: validateImages()
        });
        
        // 测试4: 同时有女生和男生素材
        $('body').append('<input type="hidden" name="pc_src[]" value="/uploads/female1.jpg">');
        results.push({
            name: "测试4: 同时有女生和男生素材",
            result: validateImages()
        });
        
        // 测试5: 有空值的情况
        $('body').find('input[type="hidden"]').remove();
        $('body').append('<input type="hidden" name="pc_src[]" value="">');
        $('body').append('<input type="hidden" name="male_images[]" value="/uploads/male1.jpg">');
        results.push({
            name: "测试5: 女生素材为空，男生素材有值",
            result: validateImages()
        });
        
        // 显示结果
        var html = '<table border="1" style="border-collapse: collapse; width: 100%;">';
        html += '<tr><th>测试用例</th><th>验证结果</th><th>消息</th><th>状态</th></tr>';
        
        results.forEach(function(test) {
            var status = test.result.success ? '✅ 通过' : '❌ 失败';
            var color = test.result.success ? 'green' : 'red';
            html += '<tr>';
            html += '<td>' + test.name + '</td>';
            html += '<td>' + test.result.success + '</td>';
            html += '<td>' + test.result.message + '</td>';
            html += '<td style="color: ' + color + ';">' + status + '</td>';
            html += '</tr>';
        });
        
        html += '</table>';
        
        html += '<h3>预期结果说明</h3>';
        html += '<ul>';
        html += '<li>测试1: 应该失败 - 没有上传任何素材</li>';
        html += '<li>测试2: 应该通过 - 有女生素材</li>';
        html += '<li>测试3: 应该通过 - 有男生素材</li>';
        html += '<li>测试4: 应该通过 - 同时有两种素材</li>';
        html += '<li>测试5: 应该通过 - 男生素材有效</li>';
        html += '</ul>';
        
        $('#test-results').html(html);
    }
    
    // 页面加载完成后运行测试
    $(document).ready(function() {
        runTests();
    });
    </script>
</body>
</html>
