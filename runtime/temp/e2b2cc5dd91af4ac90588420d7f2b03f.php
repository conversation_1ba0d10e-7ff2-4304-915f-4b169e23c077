<?php /*a:6:{s:59:"/www/wwwroot/tcyp/application/admin/view/company/index.html";i:1750773135;s:57:"/www/wwwroot/tcyp/application/admin/view/public/meta.html";i:1722181302;s:58:"/www/wwwroot/tcyp/application/admin/view/public/style.html";i:1638604914;s:57:"/www/wwwroot/tcyp/application/admin/view/public/menu.html";i:1751142456;s:57:"/www/wwwroot/tcyp/application/admin/view/public/head.html";i:1681316918;s:59:"/www/wwwroot/tcyp/application/admin/view/public/footer.html";i:1717312262;}*/ ?>
<!DOCTYPE html>
<html lang="zh">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" />
<title>火狐狸娱乐</title>
<link rel="icon" href="favicon.ico" type="image/ico">
<meta name="author" content="yinqi">

<link href="/static/admin/index/css/bootstrap.min.css" rel="stylesheet">
<link href="/static/admin/index/css/materialdesignicons.min.css" rel="stylesheet">
<link href="/static/admin/index/css/style.css" rel="stylesheet">
<link rel="stylesheet" type="text/css" href="/static/admin/index/css/layui.css" />
<style type="text/css" media="all">
.layui-form-label {
    width: 100px;
}
.layui-layout{
    margin-top: 10px;
}
.sidebar-header {
    height: 70px;
}
.sidebar-header a {
    font-size: 30px;
    color:#fff;
    line-height: 68px;
}

</style>
</head>
  
<body data-logobg="color_8" data-sidebarbg="color_8">
<div class="layout-web">
  <div class="layout-container">
    <!--左侧导航-->
    <aside class="layout-sidebar">
      <!-- logo -->
      <div id="logo" class="sidebar-header">
        <a href="<?php echo url('index/index'); ?>">火狐狸娱乐</a>
      </div>
      <div class="layout-sidebar-scroll"> 
        
        <nav class="sidebar-main">
          <ul class="nav nav-drawer">
            <li class="nav-item <?php if(Request::controller() == 'Index'): ?> active <?php endif; ?>"> <a href="<?php echo url('index/index'); ?>"><i class="mdi mdi-home"></i>首页</a></li>
            <li class="nav-item nav-item-has-subnav <?php if(Request::controller() == 'Data'): ?> active open <?php endif; ?>">
              <a href="javascript:void(0)"><i class="mdi mdi-poll"></i>数据统计</a>
              <ul class="nav nav-subnav">
                <li><a href="<?php echo url('data/register'); ?>">注册统计</a></li>
                <li><a href="<?php echo url('data/recharge'); ?>">充值统计</a></li>
                <li><a href="<?php echo url('data/withdrawal'); ?>">提现统计</a></li>
              </ul>
            </li>   
            <li class="nav-item nav-item-has-subnav <?php if(Request::controller() == 'Withdrawal' 
            || Request::controller() == 'Banks'): ?> active open <?php endif; ?>">
              <a href="javascript:void(0)"><i class="mdi mdi-bank"></i>财务管理</a>
              <ul class="nav nav-subnav">
                <li><a href="<?php echo url('recharge/index'); ?>">充值管理</a></li>
                <li><a href="<?php echo url('withdrawal/index'); ?>">提现管理</a></li>
                <li><a href="<?php echo url('Banks/index'); ?>">银行管理</a></li>
                <li><a href="<?php echo url('Bank/index'); ?>">银行界面</a></li>
              </ul>
            </li>     
            <li class="nav-item nav-item-has-subnav <?php if(Request::controller() == 'Log' || Request::controller() == 'User' || Request::controller() == 'Role'): ?> active open <?php endif; ?>">
              <a href="javascript:void(0)"><i class="mdi mdi-account"></i>超管管理</a>
              <ul class="nav nav-subnav">
                <li> <a href="<?php echo url('user/index'); ?>">管理员管理</a></li>
                <li> <a href="<?php echo url('role/index'); ?>">管理组管理</a> </li>
                <li> <a href="<?php echo url('log/index'); ?>">日志管理</a> </li>
              </ul>
            </li>
            <li class="nav-item-has-subnav 
            <?php if(Request::controller()=='Member'
            || Request::controller() == 'Agent'): ?> active open <?php endif; ?>">
                <a href="javascript:void(0)"><i class="mdi mdi-account-multiple"></i>会员管理</a>
                <ul class="nav nav-subnav">
                     <li> <a href="<?php echo url('agent/index'); ?>">代理列表</a></li>
                     <li> <a href="<?php echo url('member/index'); ?>">会员列表</a></li>
                </ul>
            </li>            
            <li class="nav-item nav-item-has-subnav 
            <?php if(Request::controller() == 'Lotteryclass' 
            || Request::controller() == 'Lottery'
            || Request::controller() == 'Openlottery'
            || Request::controller() == 'Yulottery'
            || Request::controller() == 'Buylottery'): ?> active open <?php endif; ?>">
              <a href="javascript:void(0)"><i class="mdi mdi-gamepad-variant"></i>彩票管理</a>
              <ul class="nav nav-subnav">
                <li> <a href="<?php echo url('lotteryclass/index'); ?>">彩票分类</a></li>
                <li> <a href="<?php echo url('lottery/index'); ?>">彩票列表</a></li>
                <li> <a href="<?php echo url('openlottery/index'); ?>">开奖记录</a></li>
                <li> <a href="<?php echo url('buylottery/index'); ?>">投注记录</a></li>
                <li> <a href="<?php echo url('Yulottery/index'); ?>">预设开奖</a></li>
              </ul>
            </li>            
            <li class="nav-item nav-item-has-subnav <?php if(Request::controller() == 'Video' || Request::controller() == 'Videoclass'): ?> active open <?php endif; ?>">
              <a href="javascript:void(0)"><i class="mdi mdi-video"></i>视频管理</a>
              <ul class="nav nav-subnav">
                <li> <a href="<?php echo url('videoclass/index'); ?>">视频分类</a> </li>
                <li> <a href="<?php echo url('video/index'); ?>">视频列表</a> </li>
              </ul>
            </li>
            
            <li class="nav-item nav-item-has-subnav <?php if(Request::controller() == 'Xuanfei' || Request::controller() == 'Xuanfei'): ?> active open <?php endif; ?>">
              <a href="javascript:void(0)"><i class="mdi mdi-video"></i>选妃管理</a>
              <ul class="nav nav-subnav">
                <li> <a href="<?php echo url('xuanfei/xuanfeiaddress'); ?>">选妃地区</a> </li>
                <li> <a href="<?php echo url('xuanfeilist/xuanfeilist'); ?>">选妃列表</a> </li>
              </ul>
            </li>
            
            <li class="nav-item nav-item-has-subnav <?php if(Request::controller() == 'System'
            || Request::controller() == 'Notice'
            || Request::controller() == 'Banner'
            || Request::controller() == 'Vip'
            || Request::controller() == 'Company'
            || Request::controller() == 'OssConfig'
            || Request::controller() == 'Upload'
            || Request::controller() == 'EpusdtConfig'): ?> active open <?php endif; ?>">
              <a href="javascript:void(0)"><i class="mdi mdi-settings"></i>系统管理</a>
              <ul class="nav nav-subnav">
                <li> <a href="<?php echo url('system/index'); ?>">基本配置</a> </li>
                <li> <a href="<?php echo url('vip/index'); ?>">VIP配置</a> </li>
                <li> <a href="<?php echo url('company/index'); ?>">公司简历配置</a> </li>
                <li> <a href="<?php echo url('oss_config/index'); ?>">OSS存储配置</a> </li>
                <li> <a href="<?php echo url('epusdt_config/index'); ?>">EPUSDT支付配置</a> </li>
                <li> <a href="<?php echo url('upload/demo'); ?>">文件上传演示</a> </li>
                <li> <a href="<?php echo url('notice/index'); ?>">公告配置</a> </li>
                <li> <a href="<?php echo url('banner/index'); ?>">广告配置</a> </li>
                <!--<li> <a href="<?php echo url('landing/index'); ?>">落地页链接配置</a> </li>-->
              </ul>
            </li>
          </ul>
        </nav>
        
        <div class="sidebar-footer">
          <p class="copyright">Copyright &copy; 2019.  All rights <a href="http://www.bootstrapmb.com/">reserved</a>. </p>
        </div>
      </div>
      
    </aside>
    <!--End 左侧导航-->
    <!--头部信息-->
    <header class="layout-header">
      
      <nav class="navbar navbar-default">
        <div class="topbar">
          
          <div class="topbar-left">
            <div class="aside-toggler">
              <span class="toggler-bar"></span>
              <span class="toggler-bar"></span>
              <span class="toggler-bar"></span>
            </div>
            <!--<span class="navbar-page-title"> </span>-->
          </div>
          
          <ul class="topbar-right">
            <li class="dropdown dropdown-profile">
              <a href="javascript:void(0)" data-toggle="dropdown">
                <img class="img-avatar img-avatar-48 m-r-10" src="/static/admin/index/images/users/avatar.jpg" alt="管理员" />
                <span><?php echo htmlentities($userinfo['username']); ?>【<?php echo htmlentities($role['name']); ?>】<span class="caret"></span></span>
              </a>
              <ul class="dropdown-menu dropdown-menu-right">
                <!--<li> <a href="pages_profile.html"><i class="mdi mdi-account"></i> 个人信息</a> </li>-->
                <!--<li> <a href="pages_edit_pwd.html"><i class="mdi mdi-lock-outline"></i> 修改密码</a> </li>-->
                <li> <a href="javascript:void(0)"><i class="mdi mdi-delete"></i> 清空缓存</a></li>
                <li class="divider"></li>
                <li> <a href="<?php echo url('base/Logout'); ?>"><i class="mdi mdi-logout-variant"></i> 退出登录</a> </li>
              </ul>
            </li>
            <!--切换主题配色-->
		    <li class="dropdown dropdown-skin">
			  <span data-toggle="dropdown" class="icon-palette"><i class="mdi mdi-palette"></i></span>
			  <ul class="dropdown-menu dropdown-menu-right" data-stopPropagation="true">
                <li class="drop-title"><p>主题</p></li>
                <li class="drop-skin-li clearfix">
                  <span class="inverse">
                    <input type="radio" name="site_theme" value="default" id="site_theme_1" checked>
                    <label for="site_theme_1"></label>
                  </span>
                  <span>
                    <input type="radio" name="site_theme" value="dark" id="site_theme_2">
                    <label for="site_theme_2"></label>
                  </span>
                  <span>
                    <input type="radio" name="site_theme" value="translucent" id="site_theme_3">
                    <label for="site_theme_3"></label>
                  </span>
                </li>
			    <li class="drop-title"><p>LOGO</p></li>
				<li class="drop-skin-li clearfix">
                  <span class="inverse">
                    <input type="radio" name="logo_bg" value="default" id="logo_bg_1" checked>
                    <label for="logo_bg_1"></label>
                  </span>
                  <span>
                    <input type="radio" name="logo_bg" value="color_2" id="logo_bg_2">
                    <label for="logo_bg_2"></label>
                  </span>
                  <span>
                    <input type="radio" name="logo_bg" value="color_3" id="logo_bg_3">
                    <label for="logo_bg_3"></label>
                  </span>
                  <span>
                    <input type="radio" name="logo_bg" value="color_4" id="logo_bg_4">
                    <label for="logo_bg_4"></label>
                  </span>
                  <span>
                    <input type="radio" name="logo_bg" value="color_5" id="logo_bg_5">
                    <label for="logo_bg_5"></label>
                  </span>
                  <span>
                    <input type="radio" name="logo_bg" value="color_6" id="logo_bg_6">
                    <label for="logo_bg_6"></label>
                  </span>
                  <span>
                    <input type="radio" name="logo_bg" value="color_7" id="logo_bg_7">
                    <label for="logo_bg_7"></label>
                  </span>
                  <span>
                    <input type="radio" name="logo_bg" value="color_8" id="logo_bg_8">
                    <label for="logo_bg_8"></label>
                  </span>
				</li>
				<li class="drop-title"><p>头部</p></li>
				<li class="drop-skin-li clearfix">
                  <span class="inverse">
                    <input type="radio" name="header_bg" value="default" id="header_bg_1" checked>
                    <label for="header_bg_1"></label>                      
                  </span>                                                    
                  <span>                                                     
                    <input type="radio" name="header_bg" value="color_2" id="header_bg_2">
                    <label for="header_bg_2"></label>                      
                  </span>                                                    
                  <span>                                                     
                    <input type="radio" name="header_bg" value="color_3" id="header_bg_3">
                    <label for="header_bg_3"></label>
                  </span>
                  <span>
                    <input type="radio" name="header_bg" value="color_4" id="header_bg_4">
                    <label for="header_bg_4"></label>                      
                  </span>                                                    
                  <span>                                                     
                    <input type="radio" name="header_bg" value="color_5" id="header_bg_5">
                    <label for="header_bg_5"></label>                      
                  </span>                                                    
                  <span>                                                     
                    <input type="radio" name="header_bg" value="color_6" id="header_bg_6">
                    <label for="header_bg_6"></label>                      
                  </span>                                                    
                  <span>                                                     
                    <input type="radio" name="header_bg" value="color_7" id="header_bg_7">
                    <label for="header_bg_7"></label>
                  </span>
                  <span>
                    <input type="radio" name="header_bg" value="color_8" id="header_bg_8">
                    <label for="header_bg_8"></label>
                  </span>
				</li>
				<li class="drop-title"><p>侧边栏</p></li>
				<li class="drop-skin-li clearfix">
                  <span class="inverse">
                    <input type="radio" name="sidebar_bg" value="default" id="sidebar_bg_1" checked>
                    <label for="sidebar_bg_1"></label>
                  </span>
                  <span>
                    <input type="radio" name="sidebar_bg" value="color_2" id="sidebar_bg_2">
                    <label for="sidebar_bg_2"></label>
                  </span>
                  <span>
                    <input type="radio" name="sidebar_bg" value="color_3" id="sidebar_bg_3">
                    <label for="sidebar_bg_3"></label>
                  </span>
                  <span>
                    <input type="radio" name="sidebar_bg" value="color_4" id="sidebar_bg_4">
                    <label for="sidebar_bg_4"></label>
                  </span>
                  <span>
                    <input type="radio" name="sidebar_bg" value="color_5" id="sidebar_bg_5">
                    <label for="sidebar_bg_5"></label>
                  </span>
                  <span>
                    <input type="radio" name="sidebar_bg" value="color_6" id="sidebar_bg_6">
                    <label for="sidebar_bg_6"></label>
                  </span>
                  <span>
                    <input type="radio" name="sidebar_bg" value="color_7" id="sidebar_bg_7">
                    <label for="sidebar_bg_7"></label>
                  </span>
                  <span>
                    <input type="radio" name="sidebar_bg" value="color_8" id="sidebar_bg_8">
                    <label for="sidebar_bg_8"></label>
                  </span>
				</li>
			  </ul>
			</li>
            <!--切换主题配色-->
          </ul>
          
        </div>
      </nav>
      
    </header>
    <!--End 头部信息-->
    <main class="layout-content">
      <div class="container-fluid">
        <div class="row">
          <div class="col-lg-12">
            <div class="card">
				<div class="card-header">
					<h4>
						公司简历配置
					</h4>
					<small class="help-block">配置公司基本信息，用于对外展示公司简历</small>
				</div>
				<div class="card-body">
					<form class="layui-form" id="companyForm">
						<!-- 基本信息 -->
						<div class="row">
							<div class="col-md-12">
								<h5 class="text-primary">基本信息</h5>
								<hr>
							</div>
						</div>
						
						<div class="row">
							<div class="col-md-6">
								<div class="form-group">
									<label>公司名称 <span class="text-danger">*</span></label>
									<input class="form-control" type="text" name="company_name" 
										placeholder="请输入公司名称" 
										value="<?php echo isset($companyConfig['company_name'])?$companyConfig['company_name']:''; ?>">
									<small class="help-block">公司的正式名称</small>
								</div>
							</div>
							<div class="col-md-6">
								<div class="form-group">
									<label>所属行业</label>
									<input class="form-control" type="text" name="company_industry" 
										placeholder="请输入所属行业" 
										value="<?php echo isset($companyConfig['company_industry'])?$companyConfig['company_industry']:''; ?>">
									<small class="help-block">如：互联网、金融、制造业等</small>
								</div>
							</div>
						</div>
						
						<div class="row">
							<div class="col-md-6">
								<div class="form-group">
									<label>成立时间</label>
									<input class="form-control" type="text" name="company_founded" 
										placeholder="请输入成立时间" 
										value="<?php echo isset($companyConfig['company_founded'])?$companyConfig['company_founded']:''; ?>">
									<small class="help-block">如：2020年、2020年1月等</small>
								</div>
							</div>
							<div class="col-md-6">
								<div class="form-group">
									<label>员工规模</label>
									<input class="form-control" type="text" name="company_employees" 
										placeholder="请输入员工规模" 
										value="<?php echo isset($companyConfig['company_employees'])?$companyConfig['company_employees']:''; ?>">
									<small class="help-block">如：100-500人、1000+人等</small>
								</div>
							</div>
						</div>
						
						<!-- 公司LOGO -->
						<div class="form-group">
							<label>公司LOGO</label>
							<div class="form-group">
								<div class="layui-upload-drag" id="company_logo">
									<i class="layui-icon"></i>
									<p>点击上传，或将文件拖拽到此处</p>
									<div <?php if(!isset($companyConfig['company_logo']) || empty($companyConfig['company_logo'])): ?>class="layui-hide"<?php endif; ?> id="logoView">
										<hr>
										<input type="hidden" id="logoInput" name="company_logo" value="<?php echo isset($companyConfig['company_logo'])?$companyConfig['company_logo']:''; ?>">
										<img src="<?php echo isset($companyConfig['company_logo'])?$companyConfig['company_logo']:''; ?>" alt="公司LOGO" style="max-width: 200px;height: auto;">
									</div>
								</div>  
							</div>	
							<small class="help-block">建议尺寸：200x200像素，支持PNG、JPG格式</small>
						</div>
						
						<!-- 公司描述 -->
						<div class="form-group">
							<label>公司简介</label>
							<textarea class="form-control" name="company_description" rows="4" 
								placeholder="请输入公司简介"><?php echo isset($companyConfig['company_description'])?$companyConfig['company_description']:''; ?></textarea>
							<small class="help-block">简要介绍公司的业务和特色</small>
						</div>
						
						<!-- 联系信息 -->
						<div class="row">
							<div class="col-md-12">
								<h5 class="text-primary">联系信息</h5>
								<hr>
							</div>
						</div>
						
						<div class="row">
							<div class="col-md-6">
								<div class="form-group">
									<label>公司地址</label>
									<input class="form-control" type="text" name="company_address" 
										placeholder="请输入公司地址" 
										value="<?php echo isset($companyConfig['company_address'])?$companyConfig['company_address']:''; ?>">
								</div>
							</div>
							<div class="col-md-6">
								<div class="form-group">
									<label>联系电话</label>
									<input class="form-control" type="text" name="company_phone" 
										placeholder="请输入联系电话" 
										value="<?php echo isset($companyConfig['company_phone'])?$companyConfig['company_phone']:''; ?>">
								</div>
							</div>
						</div>
						
						<div class="row">
							<div class="col-md-6">
								<div class="form-group">
									<label>邮箱地址</label>
									<input class="form-control" type="email" name="company_email" 
										placeholder="请输入邮箱地址" 
										value="<?php echo isset($companyConfig['company_email'])?$companyConfig['company_email']:''; ?>">
								</div>
							</div>
							<div class="col-md-6">
								<div class="form-group">
									<label>官方网站</label>
									<input class="form-control" type="url" name="company_website" 
										placeholder="请输入官方网站" 
										value="<?php echo isset($companyConfig['company_website'])?$companyConfig['company_website']:''; ?>">
								</div>
							</div>
						</div>
						
						<div class="form-group">
							<button class="layui-btn" type="button" lay-submit lay-filter="save">
							    保存配置
							</button>
							<button class="layui-btn layui-btn-normal" type="button" onclick="resetForm()">
							    重置
							</button>
						</div>
					</form>
				</div>
            </div>
          </div>
        </div>
      </div>
    </main>
  </div>
</div>

<script type="text/javascript" src="/static/admin/index/js/jquery.min.js"></script>
<script type="text/javascript" src="/static/admin/index/js/bootstrap.min.js"></script>
<script type="text/javascript" src="/static/admin/index/js/perfect-scrollbar.min.js"></script>
<script type="text/javascript" src="/static/admin/index/js/main.js"></script>
<script src="/static/admin/index/js/layui.js"></script>
<!--图表插件-->
<script type="text/javascript" src="/static/admin/index/js/Chart.js"></script>
</body>
</html>
<script>
layui.use(['form', 'upload', 'layer'], function(){
  var layer = layui.layer
  ,form = layui.form
  ,upload = layui.upload;

  // 上传LOGO
  upload.render({
    elem: '#company_logo'
    ,url: "<?php echo url('doupload'); ?>"
    ,before: function(obj){
      layer.msg('上传中', {icon: 16, time: 0});
    }
    ,done: function(res){
      $("#logoInput").val(res.data);
      layui.$('#logoView').removeClass('layui-hide').find('img').attr('src', res.data);
      layer.msg('上传成功');
    }
    ,error: function(){
      layer.msg('上传失败');
    }
  });

  // 监听提交
  form.on('submit(save)', function(data){
        var loading = layer.load(0, {shade: false});
        $.ajax({
            type: 'post',
            url: "<?php echo url('doSave'); ?>",
            data: $("#companyForm").serialize(),
            dataType: "json",
            success: function(data) {
                layer.close(loading);
                if(data.code === 200){
                    layer.msg(data.msg, {
                      icon: 1,
                      time: 1500
                    }, function(){
                        window.location.reload();
                    });
                }else{
                     layer.msg(data.msg, {icon: 2});
                     return false;
                }
            },
            error: function (XMLHttpRequest, textStatus, errorThrown) {
                layer.close(loading);
                if (textStatus == "timeout") {
                    layer.msg('请求超时！', {icon: 2, time: 1500});
                } else {
                    layer.msg('服务器错误！', {icon: 2, time: 1500});
                }
            }
        });
        return false;
    });
});

// 重置表单
function resetForm() {
    layui.layer.confirm('确定要重置表单吗？', {
        btn: ['确定', '取消']
    }, function(index) {
        document.getElementById('companyForm').reset();
        layui.layer.close(index);
        layui.layer.msg('表单已重置');
    });
}
</script>
