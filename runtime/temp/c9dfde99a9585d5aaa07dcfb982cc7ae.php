<?php /*a:6:{s:62:"/www/wwwroot/tcyp/application/admin/view/oss_config/index.html";i:1751128729;s:57:"/www/wwwroot/tcyp/application/admin/view/public/meta.html";i:1722181302;s:58:"/www/wwwroot/tcyp/application/admin/view/public/style.html";i:1638604914;s:57:"/www/wwwroot/tcyp/application/admin/view/public/menu.html";i:1751142456;s:57:"/www/wwwroot/tcyp/application/admin/view/public/head.html";i:1681316918;s:59:"/www/wwwroot/tcyp/application/admin/view/public/footer.html";i:1717312262;}*/ ?>
<!DOCTYPE html>
<html lang="zh">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" />
<title>火狐狸娱乐</title>
<link rel="icon" href="favicon.ico" type="image/ico">
<meta name="author" content="yinqi">

<link href="/static/admin/index/css/bootstrap.min.css" rel="stylesheet">
<link href="/static/admin/index/css/materialdesignicons.min.css" rel="stylesheet">
<link href="/static/admin/index/css/style.css" rel="stylesheet">
<link rel="stylesheet" type="text/css" href="/static/admin/index/css/layui.css" />
<style type="text/css" media="all">
.layui-form-label {
    width: 100px;
}
.layui-layout{
    margin-top: 10px;
}
.sidebar-header {
    height: 70px;
}
.sidebar-header a {
    font-size: 30px;
    color:#fff;
    line-height: 68px;
}

</style>
</head>
  
<body data-logobg="color_8" data-sidebarbg="color_8">
<div class="layout-web">
  <div class="layout-container">
    <!--左侧导航-->
    <aside class="layout-sidebar">
      <!-- logo -->
      <div id="logo" class="sidebar-header">
        <a href="<?php echo url('index/index'); ?>">火狐狸娱乐</a>
      </div>
      <div class="layout-sidebar-scroll"> 
        
        <nav class="sidebar-main">
          <ul class="nav nav-drawer">
            <li class="nav-item <?php if(Request::controller() == 'Index'): ?> active <?php endif; ?>"> <a href="<?php echo url('index/index'); ?>"><i class="mdi mdi-home"></i>首页</a></li>
            <li class="nav-item nav-item-has-subnav <?php if(Request::controller() == 'Data'): ?> active open <?php endif; ?>">
              <a href="javascript:void(0)"><i class="mdi mdi-poll"></i>数据统计</a>
              <ul class="nav nav-subnav">
                <li><a href="<?php echo url('data/register'); ?>">注册统计</a></li>
                <li><a href="<?php echo url('data/recharge'); ?>">充值统计</a></li>
                <li><a href="<?php echo url('data/withdrawal'); ?>">提现统计</a></li>
              </ul>
            </li>   
            <li class="nav-item nav-item-has-subnav <?php if(Request::controller() == 'Withdrawal' 
            || Request::controller() == 'Banks'): ?> active open <?php endif; ?>">
              <a href="javascript:void(0)"><i class="mdi mdi-bank"></i>财务管理</a>
              <ul class="nav nav-subnav">
                <li><a href="<?php echo url('recharge/index'); ?>">充值管理</a></li>
                <li><a href="<?php echo url('withdrawal/index'); ?>">提现管理</a></li>
                <li><a href="<?php echo url('Banks/index'); ?>">银行管理</a></li>
                <li><a href="<?php echo url('Bank/index'); ?>">银行界面</a></li>
              </ul>
            </li>     
            <li class="nav-item nav-item-has-subnav <?php if(Request::controller() == 'Log' || Request::controller() == 'User' || Request::controller() == 'Role'): ?> active open <?php endif; ?>">
              <a href="javascript:void(0)"><i class="mdi mdi-account"></i>超管管理</a>
              <ul class="nav nav-subnav">
                <li> <a href="<?php echo url('user/index'); ?>">管理员管理</a></li>
                <li> <a href="<?php echo url('role/index'); ?>">管理组管理</a> </li>
                <li> <a href="<?php echo url('log/index'); ?>">日志管理</a> </li>
              </ul>
            </li>
            <li class="nav-item-has-subnav 
            <?php if(Request::controller()=='Member'
            || Request::controller() == 'Agent'): ?> active open <?php endif; ?>">
                <a href="javascript:void(0)"><i class="mdi mdi-account-multiple"></i>会员管理</a>
                <ul class="nav nav-subnav">
                     <li> <a href="<?php echo url('agent/index'); ?>">代理列表</a></li>
                     <li> <a href="<?php echo url('member/index'); ?>">会员列表</a></li>
                </ul>
            </li>            
            <li class="nav-item nav-item-has-subnav 
            <?php if(Request::controller() == 'Lotteryclass' 
            || Request::controller() == 'Lottery'
            || Request::controller() == 'Openlottery'
            || Request::controller() == 'Yulottery'
            || Request::controller() == 'Buylottery'): ?> active open <?php endif; ?>">
              <a href="javascript:void(0)"><i class="mdi mdi-gamepad-variant"></i>彩票管理</a>
              <ul class="nav nav-subnav">
                <li> <a href="<?php echo url('lotteryclass/index'); ?>">彩票分类</a></li>
                <li> <a href="<?php echo url('lottery/index'); ?>">彩票列表</a></li>
                <li> <a href="<?php echo url('openlottery/index'); ?>">开奖记录</a></li>
                <li> <a href="<?php echo url('buylottery/index'); ?>">投注记录</a></li>
                <li> <a href="<?php echo url('Yulottery/index'); ?>">预设开奖</a></li>
              </ul>
            </li>            
            <li class="nav-item nav-item-has-subnav <?php if(Request::controller() == 'Video' || Request::controller() == 'Videoclass'): ?> active open <?php endif; ?>">
              <a href="javascript:void(0)"><i class="mdi mdi-video"></i>视频管理</a>
              <ul class="nav nav-subnav">
                <li> <a href="<?php echo url('videoclass/index'); ?>">视频分类</a> </li>
                <li> <a href="<?php echo url('video/index'); ?>">视频列表</a> </li>
              </ul>
            </li>
            
            <li class="nav-item nav-item-has-subnav <?php if(Request::controller() == 'Xuanfei' || Request::controller() == 'Xuanfei'): ?> active open <?php endif; ?>">
              <a href="javascript:void(0)"><i class="mdi mdi-video"></i>选妃管理</a>
              <ul class="nav nav-subnav">
                <li> <a href="<?php echo url('xuanfei/xuanfeiaddress'); ?>">选妃地区</a> </li>
                <li> <a href="<?php echo url('xuanfeilist/xuanfeilist'); ?>">选妃列表</a> </li>
              </ul>
            </li>
            
            <li class="nav-item nav-item-has-subnav <?php if(Request::controller() == 'System'
            || Request::controller() == 'Notice'
            || Request::controller() == 'Banner'
            || Request::controller() == 'Vip'
            || Request::controller() == 'Company'
            || Request::controller() == 'OssConfig'
            || Request::controller() == 'Upload'
            || Request::controller() == 'EpusdtConfig'): ?> active open <?php endif; ?>">
              <a href="javascript:void(0)"><i class="mdi mdi-settings"></i>系统管理</a>
              <ul class="nav nav-subnav">
                <li> <a href="<?php echo url('system/index'); ?>">基本配置</a> </li>
                <li> <a href="<?php echo url('vip/index'); ?>">VIP配置</a> </li>
                <li> <a href="<?php echo url('company/index'); ?>">公司简历配置</a> </li>
                <li> <a href="<?php echo url('oss_config/index'); ?>">OSS存储配置</a> </li>
                <li> <a href="<?php echo url('epusdt_config/index'); ?>">EPUSDT支付配置</a> </li>
                <li> <a href="<?php echo url('upload/demo'); ?>">文件上传演示</a> </li>
                <li> <a href="<?php echo url('notice/index'); ?>">公告配置</a> </li>
                <li> <a href="<?php echo url('banner/index'); ?>">广告配置</a> </li>
                <!--<li> <a href="<?php echo url('landing/index'); ?>">落地页链接配置</a> </li>-->
              </ul>
            </li>
          </ul>
        </nav>
        
        <div class="sidebar-footer">
          <p class="copyright">Copyright &copy; 2019.  All rights <a href="http://www.bootstrapmb.com/">reserved</a>. </p>
        </div>
      </div>
      
    </aside>
    <!--End 左侧导航-->
    <!--头部信息-->
    <header class="layout-header">
      
      <nav class="navbar navbar-default">
        <div class="topbar">
          
          <div class="topbar-left">
            <div class="aside-toggler">
              <span class="toggler-bar"></span>
              <span class="toggler-bar"></span>
              <span class="toggler-bar"></span>
            </div>
            <!--<span class="navbar-page-title"> </span>-->
          </div>
          
          <ul class="topbar-right">
            <li class="dropdown dropdown-profile">
              <a href="javascript:void(0)" data-toggle="dropdown">
                <img class="img-avatar img-avatar-48 m-r-10" src="/static/admin/index/images/users/avatar.jpg" alt="管理员" />
                <span><?php echo htmlentities($userinfo['username']); ?>【<?php echo htmlentities($role['name']); ?>】<span class="caret"></span></span>
              </a>
              <ul class="dropdown-menu dropdown-menu-right">
                <!--<li> <a href="pages_profile.html"><i class="mdi mdi-account"></i> 个人信息</a> </li>-->
                <!--<li> <a href="pages_edit_pwd.html"><i class="mdi mdi-lock-outline"></i> 修改密码</a> </li>-->
                <li> <a href="javascript:void(0)"><i class="mdi mdi-delete"></i> 清空缓存</a></li>
                <li class="divider"></li>
                <li> <a href="<?php echo url('base/Logout'); ?>"><i class="mdi mdi-logout-variant"></i> 退出登录</a> </li>
              </ul>
            </li>
            <!--切换主题配色-->
		    <li class="dropdown dropdown-skin">
			  <span data-toggle="dropdown" class="icon-palette"><i class="mdi mdi-palette"></i></span>
			  <ul class="dropdown-menu dropdown-menu-right" data-stopPropagation="true">
                <li class="drop-title"><p>主题</p></li>
                <li class="drop-skin-li clearfix">
                  <span class="inverse">
                    <input type="radio" name="site_theme" value="default" id="site_theme_1" checked>
                    <label for="site_theme_1"></label>
                  </span>
                  <span>
                    <input type="radio" name="site_theme" value="dark" id="site_theme_2">
                    <label for="site_theme_2"></label>
                  </span>
                  <span>
                    <input type="radio" name="site_theme" value="translucent" id="site_theme_3">
                    <label for="site_theme_3"></label>
                  </span>
                </li>
			    <li class="drop-title"><p>LOGO</p></li>
				<li class="drop-skin-li clearfix">
                  <span class="inverse">
                    <input type="radio" name="logo_bg" value="default" id="logo_bg_1" checked>
                    <label for="logo_bg_1"></label>
                  </span>
                  <span>
                    <input type="radio" name="logo_bg" value="color_2" id="logo_bg_2">
                    <label for="logo_bg_2"></label>
                  </span>
                  <span>
                    <input type="radio" name="logo_bg" value="color_3" id="logo_bg_3">
                    <label for="logo_bg_3"></label>
                  </span>
                  <span>
                    <input type="radio" name="logo_bg" value="color_4" id="logo_bg_4">
                    <label for="logo_bg_4"></label>
                  </span>
                  <span>
                    <input type="radio" name="logo_bg" value="color_5" id="logo_bg_5">
                    <label for="logo_bg_5"></label>
                  </span>
                  <span>
                    <input type="radio" name="logo_bg" value="color_6" id="logo_bg_6">
                    <label for="logo_bg_6"></label>
                  </span>
                  <span>
                    <input type="radio" name="logo_bg" value="color_7" id="logo_bg_7">
                    <label for="logo_bg_7"></label>
                  </span>
                  <span>
                    <input type="radio" name="logo_bg" value="color_8" id="logo_bg_8">
                    <label for="logo_bg_8"></label>
                  </span>
				</li>
				<li class="drop-title"><p>头部</p></li>
				<li class="drop-skin-li clearfix">
                  <span class="inverse">
                    <input type="radio" name="header_bg" value="default" id="header_bg_1" checked>
                    <label for="header_bg_1"></label>                      
                  </span>                                                    
                  <span>                                                     
                    <input type="radio" name="header_bg" value="color_2" id="header_bg_2">
                    <label for="header_bg_2"></label>                      
                  </span>                                                    
                  <span>                                                     
                    <input type="radio" name="header_bg" value="color_3" id="header_bg_3">
                    <label for="header_bg_3"></label>
                  </span>
                  <span>
                    <input type="radio" name="header_bg" value="color_4" id="header_bg_4">
                    <label for="header_bg_4"></label>                      
                  </span>                                                    
                  <span>                                                     
                    <input type="radio" name="header_bg" value="color_5" id="header_bg_5">
                    <label for="header_bg_5"></label>                      
                  </span>                                                    
                  <span>                                                     
                    <input type="radio" name="header_bg" value="color_6" id="header_bg_6">
                    <label for="header_bg_6"></label>                      
                  </span>                                                    
                  <span>                                                     
                    <input type="radio" name="header_bg" value="color_7" id="header_bg_7">
                    <label for="header_bg_7"></label>
                  </span>
                  <span>
                    <input type="radio" name="header_bg" value="color_8" id="header_bg_8">
                    <label for="header_bg_8"></label>
                  </span>
				</li>
				<li class="drop-title"><p>侧边栏</p></li>
				<li class="drop-skin-li clearfix">
                  <span class="inverse">
                    <input type="radio" name="sidebar_bg" value="default" id="sidebar_bg_1" checked>
                    <label for="sidebar_bg_1"></label>
                  </span>
                  <span>
                    <input type="radio" name="sidebar_bg" value="color_2" id="sidebar_bg_2">
                    <label for="sidebar_bg_2"></label>
                  </span>
                  <span>
                    <input type="radio" name="sidebar_bg" value="color_3" id="sidebar_bg_3">
                    <label for="sidebar_bg_3"></label>
                  </span>
                  <span>
                    <input type="radio" name="sidebar_bg" value="color_4" id="sidebar_bg_4">
                    <label for="sidebar_bg_4"></label>
                  </span>
                  <span>
                    <input type="radio" name="sidebar_bg" value="color_5" id="sidebar_bg_5">
                    <label for="sidebar_bg_5"></label>
                  </span>
                  <span>
                    <input type="radio" name="sidebar_bg" value="color_6" id="sidebar_bg_6">
                    <label for="sidebar_bg_6"></label>
                  </span>
                  <span>
                    <input type="radio" name="sidebar_bg" value="color_7" id="sidebar_bg_7">
                    <label for="sidebar_bg_7"></label>
                  </span>
                  <span>
                    <input type="radio" name="sidebar_bg" value="color_8" id="sidebar_bg_8">
                    <label for="sidebar_bg_8"></label>
                  </span>
				</li>
			  </ul>
			</li>
            <!--切换主题配色-->
          </ul>
          
        </div>
      </nav>
      
    </header>
    <!--End 头部信息-->
    <main class="layout-content">
      <div class="container-fluid">
        <div class="row">
          <div class="col-lg-12">
            <div class="card">
                <div class="card-header">
                    <h4>OSS对象存储配置</h4>
                    <small class="help-block">配置文件上传到云存储服务，支持阿里云OSS、腾讯云COS、七牛云等</small>
                </div>
                <div class="card-body">
                    <form class="layui-form" action="<?php echo url('doSave'); ?>" method="post">
                
                <!-- 基础配置 -->
                <div class="layui-form-item">
                    <label class="layui-form-label">启用OSS</label>
                    <div class="layui-input-block">
                        <input type="checkbox" name="enable" value="1" lay-skin="switch" lay-text="开启|关闭" 
                               <?php if(isset($ossConfig['enable']) && $ossConfig['enable']): ?>checked<?php endif; ?>>
                        <div class="layui-form-mid layui-word-aux">开启后文件将上传到云存储，关闭则保存到本地服务器</div>
                    </div>
                </div>
                
                <div class="layui-form-item">
                    <label class="layui-form-label">服务商</label>
                    <div class="layui-input-block">
                        <select name="provider" lay-filter="provider">
                            <option value="aliyun" <?php if(isset($ossConfig['provider']) && $ossConfig['provider'] == 'aliyun'): ?>selected<?php endif; ?>>阿里云OSS</option>
                            <option value="tencent" <?php if(isset($ossConfig['provider']) && $ossConfig['provider'] == 'tencent'): ?>selected<?php endif; ?>>腾讯云COS</option>
                            <option value="qiniu" <?php if(isset($ossConfig['provider']) && $ossConfig['provider'] == 'qiniu'): ?>selected<?php endif; ?>>七牛云</option>
                        </select>
                    </div>
                </div>
                
                <!-- 阿里云OSS配置 -->
                <div class="provider-config" id="aliyun-config">
                    <div class="layui-card">
                        <div class="layui-card-header">阿里云OSS配置</div>
                        <div class="layui-card-body">
                            <div class="layui-form-item">
                                <label class="layui-form-label">AccessKey ID</label>
                                <div class="layui-input-block">
                                    <input type="text" name="aliyun_access_key_id" placeholder="请输入AccessKey ID" 
                                           value="<?php echo isset($ossConfig['aliyun']['access_key_id']) ? htmlentities($ossConfig['aliyun']['access_key_id']) : ''; ?>" class="layui-input">
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">AccessKey Secret</label>
                                <div class="layui-input-block">
                                    <input type="password" name="aliyun_access_key_secret" placeholder="请输入AccessKey Secret" 
                                           value="<?php echo isset($ossConfig['aliyun']['access_key_secret']) ? htmlentities($ossConfig['aliyun']['access_key_secret']) : ''; ?>" class="layui-input">
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">Endpoint</label>
                                <div class="layui-input-block">
                                    <input type="text" name="aliyun_endpoint" placeholder="例如：https://oss-cn-hangzhou.aliyuncs.com" 
                                           value="<?php echo isset($ossConfig['aliyun']['endpoint']) ? htmlentities($ossConfig['aliyun']['endpoint']) : ''; ?>" class="layui-input">
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">Bucket</label>
                                <div class="layui-input-block">
                                    <input type="text" name="aliyun_bucket" placeholder="请输入Bucket名称" 
                                           value="<?php echo isset($ossConfig['aliyun']['bucket']) ? htmlentities($ossConfig['aliyun']['bucket']) : ''; ?>" class="layui-input">
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">自定义域名</label>
                                <div class="layui-input-block">
                                    <input type="text" name="aliyun_domain" placeholder="可选，例如：https://cdn.example.com" 
                                           value="<?php echo isset($ossConfig['aliyun']['domain']) ? htmlentities($ossConfig['aliyun']['domain']) : ''; ?>" class="layui-input">
                                    <div class="layui-form-mid layui-word-aux">如果配置了CDN加速域名，请填写此项</div>
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <div class="layui-input-block">
                                    <button type="button" class="layui-btn layui-btn-normal" id="test-aliyun">测试连接</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 腾讯云COS配置 -->
                <div class="provider-config" id="tencent-config" style="display: none;">
                    <div class="layui-card">
                        <div class="layui-card-header">腾讯云COS配置</div>
                        <div class="layui-card-body">
                            <div class="layui-form-item">
                                <label class="layui-form-label">Secret ID</label>
                                <div class="layui-input-block">
                                    <input type="text" name="tencent_secret_id" placeholder="请输入Secret ID" 
                                           value="<?php echo isset($ossConfig['tencent']['secret_id']) ? htmlentities($ossConfig['tencent']['secret_id']) : ''; ?>" class="layui-input">
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">Secret Key</label>
                                <div class="layui-input-block">
                                    <input type="password" name="tencent_secret_key" placeholder="请输入Secret Key" 
                                           value="<?php echo isset($ossConfig['tencent']['secret_key']) ? htmlentities($ossConfig['tencent']['secret_key']) : ''; ?>" class="layui-input">
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">地域</label>
                                <div class="layui-input-block">
                                    <input type="text" name="tencent_region" placeholder="例如：ap-beijing" 
                                           value="<?php echo isset($ossConfig['tencent']['region']) ? htmlentities($ossConfig['tencent']['region']) : ''; ?>" class="layui-input">
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">Bucket</label>
                                <div class="layui-input-block">
                                    <input type="text" name="tencent_bucket" placeholder="请输入Bucket名称" 
                                           value="<?php echo isset($ossConfig['tencent']['bucket']) ? htmlentities($ossConfig['tencent']['bucket']) : ''; ?>" class="layui-input">
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">自定义域名</label>
                                <div class="layui-input-block">
                                    <input type="text" name="tencent_domain" placeholder="可选，例如：https://cdn.example.com"
                                           value="<?php echo isset($ossConfig['tencent']['domain']) ? htmlentities($ossConfig['tencent']['domain']) : ''; ?>" class="layui-input">
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <div class="layui-input-block">
                                    <button type="button" class="layui-btn layui-btn-normal" id="test-tencent">测试连接</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 七牛云配置 -->
                <div class="provider-config" id="qiniu-config" style="display: none;">
                    <div class="layui-card">
                        <div class="layui-card-header">七牛云配置</div>
                        <div class="layui-card-body">
                            <div class="layui-form-item">
                                <label class="layui-form-label">Access Key</label>
                                <div class="layui-input-block">
                                    <input type="text" name="qiniu_access_key" placeholder="请输入Access Key" 
                                           value="<?php echo isset($ossConfig['qiniu']['access_key']) ? htmlentities($ossConfig['qiniu']['access_key']) : ''; ?>" class="layui-input">
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">Secret Key</label>
                                <div class="layui-input-block">
                                    <input type="password" name="qiniu_secret_key" placeholder="请输入Secret Key" 
                                           value="<?php echo isset($ossConfig['qiniu']['secret_key']) ? htmlentities($ossConfig['qiniu']['secret_key']) : ''; ?>" class="layui-input">
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">Bucket</label>
                                <div class="layui-input-block">
                                    <input type="text" name="qiniu_bucket" placeholder="请输入Bucket名称" 
                                           value="<?php echo isset($ossConfig['qiniu']['bucket']) ? htmlentities($ossConfig['qiniu']['bucket']) : ''; ?>" class="layui-input">
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">访问域名</label>
                                <div class="layui-input-block">
                                    <input type="text" name="qiniu_domain" placeholder="例如：https://cdn.example.com"
                                           value="<?php echo isset($ossConfig['qiniu']['domain']) ? htmlentities($ossConfig['qiniu']['domain']) : ''; ?>" class="layui-input">
                                    <div class="layui-form-mid layui-word-aux">七牛云必须配置访问域名</div>
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <div class="layui-input-block">
                                    <button type="button" class="layui-btn layui-btn-normal" id="test-qiniu">测试连接</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="layui-form-item">
                    <div class="layui-input-block">
                        <button class="layui-btn" lay-submit lay-filter="save">保存配置</button>
                        <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                        <button type="button" class="layui-btn layui-btn-warm" id="test-route">测试路由</button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
    
    // 切换服务商
    form.on('select(provider)', function(data){
        $('.provider-config').hide();
        $('#' + data.value + '-config').show();
    });
    
    // 初始化显示
    var currentProvider = $('select[name="provider"]').val();
    $('.provider-config').hide();
    $('#' + currentProvider + '-config').show();
    
    // 测试阿里云连接
    $('#test-aliyun').click(function(){
        var config = {
            provider: 'aliyun',
            aliyun_access_key_id: $('input[name="aliyun_access_key_id"]').val(),
            aliyun_access_key_secret: $('input[name="aliyun_access_key_secret"]').val(),
            aliyun_endpoint: $('input[name="aliyun_endpoint"]').val(),
            aliyun_bucket: $('input[name="aliyun_bucket"]').val()
        };

        if (!config.aliyun_access_key_id || !config.aliyun_access_key_secret || !config.aliyun_endpoint || !config.aliyun_bucket) {
            layer.msg('请先填写完整的配置信息');
            return;
        }
        
        layer.load(2);
        $.post('<?php echo url("testConnection"); ?>', config, function(res){
            layer.closeAll('loading');
            console.log('Response:', res); // 调试信息
            if (res.code === 200) {
                layer.msg(res.msg, {icon: 1});
            } else {
                layer.msg(res.msg, {icon: 2});
            }
        }).fail(function(xhr, status, error) {
            layer.closeAll('loading');
            console.error('Request failed:', xhr, status, error);
            layer.msg('请求失败：' + error, {icon: 2});
        });
    });

    // 测试腾讯云连接
    $('#test-tencent').click(function(){
        var config = {
            provider: 'tencent',
            tencent_secret_id: $('input[name="tencent_secret_id"]').val(),
            tencent_secret_key: $('input[name="tencent_secret_key"]').val(),
            tencent_region: $('input[name="tencent_region"]').val(),
            tencent_bucket: $('input[name="tencent_bucket"]').val()
        };

        if (!config.tencent_secret_id || !config.tencent_secret_key || !config.tencent_region || !config.tencent_bucket) {
            layer.msg('请先填写完整的配置信息');
            return;
        }

        layer.load(2);
        $.post('<?php echo url("testConnection"); ?>', config, function(res){
            layer.closeAll('loading');
            console.log('Tencent Response:', res);
            if (res.code === 200) {
                layer.msg(res.msg, {icon: 1});
            } else {
                layer.msg(res.msg, {icon: 2});
            }
        }).fail(function(xhr, status, error) {
            layer.closeAll('loading');
            console.error('Tencent Request failed:', xhr, status, error);
            layer.msg('请求失败：' + error, {icon: 2});
        });
    });

    // 测试七牛云连接
    $('#test-qiniu').click(function(){
        var config = {
            provider: 'qiniu',
            qiniu_access_key: $('input[name="qiniu_access_key"]').val(),
            qiniu_secret_key: $('input[name="qiniu_secret_key"]').val(),
            qiniu_bucket: $('input[name="qiniu_bucket"]').val(),
            qiniu_domain: $('input[name="qiniu_domain"]').val()
        };

        if (!config.qiniu_access_key || !config.qiniu_secret_key || !config.qiniu_bucket || !config.qiniu_domain) {
            layer.msg('请先填写完整的配置信息');
            return;
        }

        layer.load(2);
        $.post('<?php echo url("testConnection"); ?>', config, function(res){
            layer.closeAll('loading');
            console.log('Qiniu Response:', res);
            if (res.code === 200) {
                layer.msg(res.msg, {icon: 1});
            } else {
                layer.msg(res.msg, {icon: 2});
            }
        }).fail(function(xhr, status, error) {
            layer.closeAll('loading');
            console.error('Qiniu Request failed:', xhr, status, error);
            layer.msg('请求失败：' + error, {icon: 2});
        });
    });

    // 测试路由
    $('#test-route').click(function(){
        console.log('Testing route...');
        $.post('<?php echo url("test"); ?>', {}, function(res){
            console.log('Route test response:', res);
            if (res.code === 200) {
                layer.msg(res.msg, {icon: 1});
            } else {
                layer.msg(res.msg, {icon: 2});
            }
        }).fail(function(xhr, status, error) {
            console.error('Route test failed:', xhr, status, error);
            layer.msg('路由测试失败：' + error, {icon: 2});
        });
    });

    // 提交表单
    form.on('submit(save)', function(data){
        layer.load(2);
        $.post(data.form.action, data.field, function(res){
            layer.closeAll('loading');
            if (res.code === 200) {
                layer.msg('保存成功', {icon: 1});
            } else {
                layer.msg(res.msg, {icon: 2});
            }
        });
        return false;
    });
});
</script>

<style>
.provider-config {
    margin-top: 20px;
}
.card {
    margin-bottom: 20px;
}
</style>

                </div>
            </div>
          </div>
        </div>
      </div>
    </main>
  </div>
</div>

<script type="text/javascript" src="/static/admin/index/js/jquery.min.js"></script>
<script type="text/javascript" src="/static/admin/index/js/bootstrap.min.js"></script>
<script type="text/javascript" src="/static/admin/index/js/perfect-scrollbar.min.js"></script>
<script type="text/javascript" src="/static/admin/index/js/main.js"></script>
<script src="/static/admin/index/js/layui.js"></script>
<!--图表插件-->
<script type="text/javascript" src="/static/admin/index/js/Chart.js"></script>
</body>
</html>

<script>
layui.use(['form', 'layer', 'jquery'], function(){
    var form = layui.form;
    var layer = layui.layer;
    var $ = layui.jquery;

    // 切换服务商
    form.on('select(provider)', function(data){
        $('.provider-config').hide();
        $('#' + data.value + '-config').show();
    });

    // 初始化显示
    var currentProvider = $('select[name="provider"]').val();
    $('.provider-config').hide();
    $('#' + currentProvider + '-config').show();

    // 测试阿里云连接
    $('#test-aliyun').click(function(){
        var config = {
            provider: 'aliyun',
            aliyun_access_key_id: $('input[name="aliyun_access_key_id"]').val(),
            aliyun_access_key_secret: $('input[name="aliyun_access_key_secret"]').val(),
            aliyun_endpoint: $('input[name="aliyun_endpoint"]').val(),
            aliyun_bucket: $('input[name="aliyun_bucket"]').val()
        };

        if (!config.aliyun_access_key_id || !config.aliyun_access_key_secret || !config.aliyun_endpoint || !config.aliyun_bucket) {
            layer.msg('请先填写完整的配置信息');
            return;
        }

        layer.load(2);
        $.post('<?php echo url("testConnection"); ?>', config, function(res){
            layer.closeAll('loading');
            console.log('Response:', res); // 调试信息
            if (res.code === 200) {
                layer.msg(res.msg, {icon: 1});
            } else {
                layer.msg(res.msg, {icon: 2});
            }
        }).fail(function(xhr, status, error) {
            layer.closeAll('loading');
            console.error('Request failed:', xhr, status, error);
            layer.msg('请求失败：' + error, {icon: 2});
        });
    });

    // 测试腾讯云连接
    $('#test-tencent').click(function(){
        var config = {
            provider: 'tencent',
            tencent_secret_id: $('input[name="tencent_secret_id"]').val(),
            tencent_secret_key: $('input[name="tencent_secret_key"]').val(),
            tencent_region: $('input[name="tencent_region"]').val(),
            tencent_bucket: $('input[name="tencent_bucket"]').val()
        };

        if (!config.tencent_secret_id || !config.tencent_secret_key || !config.tencent_region || !config.tencent_bucket) {
            layer.msg('请先填写完整的配置信息');
            return;
        }

        layer.load(2);
        $.post('<?php echo url("testConnection"); ?>', config, function(res){
            layer.closeAll('loading');
            console.log('Tencent Response:', res);
            if (res.code === 200) {
                layer.msg(res.msg, {icon: 1});
            } else {
                layer.msg(res.msg, {icon: 2});
            }
        }).fail(function(xhr, status, error) {
            layer.closeAll('loading');
            console.error('Tencent Request failed:', xhr, status, error);
            layer.msg('请求失败：' + error, {icon: 2});
        });
    });

    // 测试七牛云连接
    $('#test-qiniu').click(function(){
        var config = {
            provider: 'qiniu',
            qiniu_access_key: $('input[name="qiniu_access_key"]').val(),
            qiniu_secret_key: $('input[name="qiniu_secret_key"]').val(),
            qiniu_bucket: $('input[name="qiniu_bucket"]').val(),
            qiniu_domain: $('input[name="qiniu_domain"]').val()
        };

        if (!config.qiniu_access_key || !config.qiniu_secret_key || !config.qiniu_bucket || !config.qiniu_domain) {
            layer.msg('请先填写完整的配置信息');
            return;
        }

        layer.load(2);
        $.post('<?php echo url("testConnection"); ?>', config, function(res){
            layer.closeAll('loading');
            console.log('Qiniu Response:', res);
            if (res.code === 200) {
                layer.msg(res.msg, {icon: 1});
            } else {
                layer.msg(res.msg, {icon: 2});
            }
        }).fail(function(xhr, status, error) {
            layer.closeAll('loading');
            console.error('Qiniu Request failed:', xhr, status, error);
            layer.msg('请求失败：' + error, {icon: 2});
        });
    });

    // 测试路由
    $('#test-route').click(function(){
        console.log('Testing route...');
        $.post('<?php echo url("test"); ?>', {}, function(res){
            console.log('Route test response:', res);
            if (res.code === 200) {
                layer.msg(res.msg, {icon: 1});
            } else {
                layer.msg(res.msg, {icon: 2});
            }
        }).fail(function(xhr, status, error) {
            console.error('Route test failed:', xhr, status, error);
            layer.msg('路由测试失败：' + error, {icon: 2});
        });
    });

    // 提交表单
    form.on('submit(save)', function(data){
        layer.load(2);
        $.post(data.form.action, data.field, function(res){
            layer.closeAll('loading');
            if (res.code === 200) {
                layer.msg('保存成功', {icon: 1});
            } else {
                layer.msg(res.msg, {icon: 2});
            }
        });
        return false;
    });
});
</script>

<style>
.provider-config {
    margin-top: 20px;
}
.card {
    margin-bottom: 20px;
}
</style>
