<?php /*a:4:{s:67:"/www/wwwroot/tcyp/application/admin/view/xuanfeilist/operation.html";i:1751567456;s:57:"/www/wwwroot/tcyp/application/admin/view/public/meta.html";i:1722181302;s:58:"/www/wwwroot/tcyp/application/admin/view/public/style.html";i:1638604914;s:63:"/www/wwwroot/tcyp/application/admin/view/public/onlyfooter.html";i:1717323400;}*/ ?>
<!DOCTYPE html>
<html lang="zh">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" />
<title>火狐狸娱乐</title>
<link rel="icon" href="favicon.ico" type="image/ico">
<meta name="author" content="yinqi">

<link href="/static/admin/index/css/bootstrap.min.css" rel="stylesheet">
<link href="/static/admin/index/css/materialdesignicons.min.css" rel="stylesheet">
<link href="/static/admin/index/css/style.css" rel="stylesheet">
<link rel="stylesheet" type="text/css" href="/static/admin/index/css/layui.css" />
<style type="text/css" media="all">
.layui-form-label {
    width: 100px;
}
.layui-layout{
    margin-top: 10px;
}
.sidebar-header {
    height: 70px;
}
.sidebar-header a {
    font-size: 30px;
    color:#fff;
    line-height: 68px;
}

</style>
</head>
  
<body data-logobg="color_8" data-sidebarbg="color_8">
<div class="layout-web">
  <div class="layout-container">
    <!--左侧导航-->
<style type="text/css" media="all">
    .layui-upload-img {
    width: 92px;
    height: 92px;
}
</style>
<main class="layui-layout">
	<div class="container-fluid">
		<div class="row">
			<div class="col-md-12">
				<div class="card">
					<div class="card-header">
					</div>
					<div class="card-body">
						<form class="layui-form" id="loginfrom">
						    <input type="hidden" id="id" name="id" value="<?php echo isset($info['id'])?$info['id']:''; ?>">
							<div class="form-group">
								<label>选妃名称(越南语)</label>
								<input class="form-control" type="text" id="xuanfei_name" name="xuanfei_name"
								placeholder="请输入您的中文选妃名称" value="<?php echo isset($info['xuanfei_name'])?$info['xuanfei_name']:''; ?>">
							</div>
							<!--<div class="form-group">
								<label>选妃名称(英文)</label>
								<input class="form-control" type="text" id="en_us" name="en_us"
								placeholder="请输入您的英文选妃名称" value="<?php echo isset($info['en_us'])?$info['en_us']:''; ?>">
							</div>
							<div class="form-group">
								<label>选妃名称(西班牙语)</label>
								<input class="form-control" type="text" id="es_spa" name="es_spa"
								placeholder="请输入您的西班牙语选妃名称" value="<?php echo isset($info['es_spa'])?$info['es_spa']:''; ?>">
							</div>
							<div class="form-group">
								<label>选妃名称(马来语)</label>
								<input class="form-control" type="text" id="ms_my" name="ms_my"
									   placeholder="请输入您的马来语选妃名称" value="<?php echo isset($info['ms_my'])?$info['ms_my']:''; ?>">
							</div>
-->
						<!--	<div class="form-group">
								<label>选妃名称(越南语)</label>
								<input class="form-control" type="text" id="yn_yu" name="yn_yu"
									   placeholder="请输入您的越南语选妃名称" value="<?php echo isset($info['yn_yu'])?$info['yn_yu']:''; ?>">
							</div>-->


							<div class="form-group">
									<label>女生素材多图片上传</label>
									<div class="layui-upload">
										<button type="button" class="layui-btn" id="test2">选择多张图片</button>
										<blockquote class="layui-elem-quote layui-quote-nm" style="margin-top: 10px;">
											预览图：
                                <div class="layui-upload-list" id="demo2">
                                    <?php if(!(empty($info['id']) || (($info['id'] instanceof \think\Collection || $info['id'] instanceof \think\Paginator ) && $info['id']->isEmpty()))): foreach($info['vod_pic'] as $key=>$vo): ?>
                                            <img src="../../../<?php echo htmlentities($vo); ?>" alt="" class="layui-upload-img">
                                            <input type="hidden" id="pc_src" name="pc_src[]" value="<?php echo htmlentities($vo); ?>" />
                                        <?php endforeach; ?>
                                    <?php endif; ?>
											</div>
										</blockquote>
									</div>
								</div>

                            <!-- 短视频上传 -->
                            <div class="form-group">
                                <label>短视频上传</label>
                                <div class="layui-upload">
                                    <button type="button" class="layui-btn layui-btn-normal" id="videoUpload">
                                        <i class="layui-icon">&#xe67c;</i>上传短视频
                                    </button>
                                    <div class="layui-upload-list">
                                        <div id="videoPreview" style="margin-top: 10px; <?php if(!(empty($info['video_url']) || (($info['video_url'] instanceof \think\Collection || $info['video_url'] instanceof \think\Paginator ) && $info['video_url']->isEmpty()))): ?>display:block;<?php else: ?>display:none;<?php endif; ?>">
                                            <video width="300" height="200" controls>
                                                <source id="videoSource" src="<?php echo isset($info['video_url'])?$info['video_url']:''; ?>" type="video/mp4">
                                                您的浏览器不支持视频播放。
                                            </video>
                                            <input type="hidden" id="video_url" name="video_url" value="<?php echo isset($info['video_url'])?$info['video_url']:''; ?>" />
                                            <p id="videoInfo" style="margin-top: 5px; color: #666; font-size: 12px;">
                                                <?php if(!(empty($info['video_url']) || (($info['video_url'] instanceof \think\Collection || $info['video_url'] instanceof \think\Paginator ) && $info['video_url']->isEmpty()))): ?>
                                                    视频时长: <?php echo htmlentities((isset($info['video_duration']) && ($info['video_duration'] !== '')?$info['video_duration']:0)); ?>秒 |
                                                    文件大小: <span id="videoSizeText"><?php echo htmlentities((isset($info['video_size_formatted']) && ($info['video_size_formatted'] !== '')?$info['video_size_formatted']:'0 Bytes')); ?></span>
                                                <?php endif; ?>
                                            </p>
                                        </div>
                                    </div>
                                </div>
                                <small class="help-block">支持MP4格式，建议文件大小不超过50MB，时长不超过60秒</small>
                            </div>

                            <!-- 视频预览图上传 -->
                            <div class="form-group">
                                <label>视频预览图</label>
                                <div class="layui-upload">
                                    <button type="button" class="layui-btn layui-btn-primary" id="videoPreviewUpload">
                                        <i class="layui-icon">&#xe67c;</i>上传预览图
                                    </button>
                                    <div class="layui-upload-list">
                                        <div id="videoPreviewImg" style="margin-top: 10px; <?php if(!(empty($info['video_preview']) || (($info['video_preview'] instanceof \think\Collection || $info['video_preview'] instanceof \think\Paginator ) && $info['video_preview']->isEmpty()))): ?>display:block;<?php else: ?>display:none;<?php endif; ?>">
                                            <img src="<?php echo isset($info['video_preview'])?$info['video_preview']:''; ?>" alt="视频预览图" style="max-width: 200px; height: auto;">
                                            <input type="hidden" id="video_preview" name="video_preview" value="<?php echo isset($info['video_preview'])?$info['video_preview']:''; ?>" />
                                        </div>
                                    </div>
                                </div>
                                <small class="help-block">视频封面图，建议尺寸16:9，如不上传将使用视频第一帧</small>
                            </div>
							<div class="form-group">
                                <label>
                                    所属地区
                                </label>
                                <select id="class_id" name="class_id">
                                  <option value="">请选择</option>
                                  <?php foreach($class as $key=>$vo): ?>
                                      <option value="<?php echo htmlentities($vo['id']); ?>" <?php if($operation == 'edit'): if($vo['id'] == $info['class_id']): ?> selected="" <?php endif; ?><?php endif; ?>><?php echo htmlentities($vo['name']); ?></option>
                                  <?php endforeach; ?>
                                </select>
							</div>

							<div class="form-group">
								<label>男生素材多图片上传</label>
								<div class="layui-upload">
									<button type="button" class="layui-btn" id="test3">选择多张图片</button>
									<div class="upload-tips">
										<div class="tip-item">• 支持格式：JPG、PNG、GIF等图片格式</div>
										<div class="tip-item">• 单个文件最大10MB，最多选择10个文件</div>
										<div class="tip-item">• 网络不好时会显示上传进度，失败文件可重新选择上传</div>
									</div>
									<blockquote class="layui-elem-quote layui-quote-nm" style="margin-top: 10px;">
										预览图：
										<div class="layui-upload-list" id="demo3">
											<?php if($operation == 'edit' && isset($info['male_images']) && !empty($info['male_images'])): 
													$male_images = json_decode($info['male_images'], true);
													if(is_array($male_images)) {
														foreach($male_images as $img) {
															echo '<img src="'.$img.'" alt="男生素材" class="layui-upload-img" style="width: 100px; height: 100px; margin: 5px;">';
															echo '<input type="hidden" name="male_images[]" value="'.$img.'" />';
														}
													}
												 ?>
											<?php endif; ?>
										</div>
									</blockquote>
								</div>
							</div>
							<div class="form-group">
								<button class="layui-btn" type="button" lay-submit lay-filter="save">
								    <?php if($operation == 'add'): ?>提交<?php else: ?>更新<?php endif; ?>
								</button>
							</div>
						</form>
					</div>
				</div>
			</div>
		</div>
	</div>
</main>
 </div>
</div>
<script type="text/javascript" src="/static/admin/index/js/jquery.min.js"></script>
<script type="text/javascript" src="/static/admin/index/js/bootstrap.min.js"></script>
<script type="text/javascript" src="/static/admin/index/js/perfect-scrollbar.min.js"></script>
<script src="/static/admin/index/js/layui.js"></script>
</body>
</html>
<script>

layui.use(['form', 'upload'], function(){
  var layer = layui.layer
  ,form = layui.form
  ,upload = layui.upload;
	//拖拽上传
// 	upload.render({
// 		elem: '#ico'
// 		,url: "<?php echo url('doupload'); ?>" //此处用的是第三方的 http 请求演示，实际使用时改成您自己的上传接口即可。
// 		,before: function(obj){
// 			layer.msg('上传中', {icon: 16, time: 0});
// 		}
// 		,done: function(res){
// 			$("#vod_pic1").val(res.data);
// 			layui.$('#uploadDemoView').removeClass('layui-hide').find('img').attr('src', res.data);
// 			layui.$('#vod_pic').val('');
// 			layer.msg('上传成功');
// 		}
// 	});

  //多图片上传
  upload.render({
    elem: '#test2'
    ,url: "<?php echo url('doupload'); ?>"//此处配置你自己的上传接口即可
    ,multiple: true
    ,before: function(obj){
      //预读本地文件示例，不支持ie8
      layer.msg('上传中', {icon: 16, time: 0});
      obj.preview(function(index, file, result){
        $('#demo2').append('<img src="'+ result +'" alt="'+ file.name +'" class="layui-upload-img">')
      });
    }
    ,done: function(res){
      //上传完毕
      if (res.code === 1) {
            return layer.msg(res.msg);
        }
        $('#demo2').append('<input type="hidden" id="pc_src" name="pc_src[]" value="'+res.data+'" />')
        layer.msg('上传成功');
    }
  });

  //男生素材多图片上传 - 优化版本
  var maleUploadIndex = 0;
  var maleUploadTotal = 0;
  var maleUploadSuccess = 0;
  var maleUploadFailed = 0;

  upload.render({
    elem: '#test3'
    ,url: "<?php echo url('doupload'); ?>"
    ,multiple: true
    ,accept: 'images'
    ,acceptMime: 'image/*'
    ,size: 10240 // 10MB限制
    ,number: 10 // 最多10个文件
    ,before: function(obj){
      // 重置计数器
      maleUploadIndex = 0;
      maleUploadTotal = obj.pushFile().length;
      maleUploadSuccess = 0;
      maleUploadFailed = 0;

      // 显示上传进度
      layer.msg('准备上传 ' + maleUploadTotal + ' 个文件...', {icon: 16, time: 2000});

      //预读本地文件
      obj.preview(function(index, file, result){
        $('#demo3').append('<div class="upload-item" data-index="'+index+'"><img src="'+ result +'" alt="'+ file.name +'" class="layui-upload-img" style="width: 100px; height: 100px; margin: 5px;"><div class="upload-status">等待上传...</div></div>')
      });
    }
    ,choose: function(obj){
      // 文件选择后的验证
      var files = obj.pushFile();
      if(Object.keys(files).length > 10){
        layer.msg('最多只能选择10个文件');
        return false;
      }
    }
    ,done: function(res, index, upload){
      maleUploadIndex++;
      var $item = $('.upload-item[data-index="'+index+'"]');

      if (res.code === 200) {
        maleUploadSuccess++;
        $item.find('.upload-status').html('<span style="color: green;">✓ 上传成功</span>');
        $('#demo3').append('<input type="hidden" name="male_images[]" value="'+res.data+'" />');
      } else {
        maleUploadFailed++;
        $item.find('.upload-status').html('<span style="color: red;">✗ ' + (res.msg || '上传失败') + '</span>');
      }

      // 更新进度
      var progress = Math.round((maleUploadIndex / maleUploadTotal) * 100);
      layer.msg('上传进度: ' + progress + '% (' + maleUploadIndex + '/' + maleUploadTotal + ')', {icon: 16, time: 1000});

      // 全部完成后的提示
      if(maleUploadIndex === maleUploadTotal){
        layer.closeAll('loading');
        if(maleUploadFailed === 0){
          layer.msg('全部上传成功！', {icon: 1});
        } else {
          layer.msg('上传完成：成功 ' + maleUploadSuccess + ' 个，失败 ' + maleUploadFailed + ' 个', {icon: 2});
        }
      }
    }
    ,error: function(index, upload){
      maleUploadIndex++;
      maleUploadFailed++;
      var $item = $('.upload-item[data-index="'+index+'"]');
      $item.find('.upload-status').html('<span style="color: red;">✗ 网络错误，请重试</span>');

      // 检查是否全部完成
      if(maleUploadIndex === maleUploadTotal){
        layer.closeAll('loading');
        layer.msg('上传完成：成功 ' + maleUploadSuccess + ' 个，失败 ' + maleUploadFailed + ' 个', {icon: 2});
      }
    }
  });

  //视频上传
  upload.render({
    elem: '#videoUpload'
    ,url: "<?php echo url('doVideoUpload'); ?>"
    ,accept: 'video'
    ,acceptMime: 'video/mp4,video/avi,video/mov,video/wmv'
    ,size: 2147483648 // 2GB
    ,before: function(obj){
      layer.msg('视频上传中，请稍候...', {icon: 16, time: 0});
    }
    ,done: function(res){
      layer.closeAll();
      if (res.code === 1) {
        return layer.msg(res.msg);
      }
      // 更新视频预览
      $('#video_url').val(res.data.video_url);
      $('#videoSource').attr('src', res.data.video_url);
      $('#videoPreview').show();

      // 更新视频信息
      var infoText = '视频时长: ' + (res.data.duration || 0) + '秒 | 文件大小: ' + formatBytes(res.data.size || 0);
      $('#videoInfo').text(infoText);

      layer.msg('视频上传成功');
    }
    ,error: function(){
      layer.closeAll();
      layer.msg('视频上传失败');
    }
  });

  //视频预览图上传
  upload.render({
    elem: '#videoPreviewUpload'
    ,url: "<?php echo url('doupload'); ?>"
    ,accept: 'images'
    ,acceptMime: 'image/jpg,image/jpeg,image/png,image/gif'
    ,before: function(obj){
      layer.msg('上传中', {icon: 16, time: 0});
    }
    ,done: function(res){
      layer.closeAll();
      if (res.code === 1) {
        return layer.msg(res.msg);
      }
      // 更新预览图
      $('#video_preview').val(res.data);
      $('#videoPreviewImg img').attr('src', res.data);
      $('#videoPreviewImg').show();

      layer.msg('预览图上传成功');
    }
    ,error: function(){
      layer.closeAll();
      layer.msg('预览图上传失败');
    }
  });

  // 格式化文件大小
  function formatBytes(bytes, decimals = 2) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const dm = decimals < 0 ? 0 : decimals;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
  }

  // 页面加载完成后的初始化
  $(document).ready(function() {
    // 文件大小已经在服务器端格式化，这里不需要额外处理
    console.log('选妃编辑页面加载完成');
  });

	//监听提交
  form.on('submit(save)', function(data){
        var xuanfei_name = $('#xuanfei_name').val();
        // var vod_play_url = $('#vod_play_url').val();
        // var pc_src = $('#pc_src').val();
        // 检查是否至少上传了女生素材或男生素材中的一种
        var hasFemaleImages = false;
        var hasMaleImages = false;

        // 检查女生素材
        $("input[name='pc_src[]']").each(function(){
            var pc_src = $(this).val();
            if(pc_src != "" && pc_src != null && pc_src != undefined){
                hasFemaleImages = true;
            }
        });

        // 检查男生素材
        $("input[name='male_images[]']").each(function(){
            var male_img = $(this).val();
            if(male_img != "" && male_img != null && male_img != undefined){
                hasMaleImages = true;
            }
        });

        // 至少需要上传一种素材
        if(!hasFemaleImages && !hasMaleImages){
            layer.msg("请至少上传女生素材或男生素材中的一种");
            return false;
        }
        // console.log(pc_src);
        // var vod_pic1 = $('#vod_pic1').val();
        var class_id = $('#class_id').val();
        if(xuanfei_name == "" || xuanfei_name == null || xuanfei_name == undefined){
            layer.msg("请输入视频名称");
            return false;
        }
        // if(vod_play_url == "" || vod_play_url == null || vod_play_url == undefined){
        //     layer.msg("请输入视频链接");
        //     return false;
        // }
       
        if(class_id == "" || class_id == null || class_id == undefined){
            layer.msg("请选择地区");
            return false;
        }
        var loading = layer.load(0, {shade: false});
        $.ajax({
            type: 'post',
            url: "<?php echo url('doSave'); ?>",
            data:$("#loginfrom").serialize(),
            dataType:"json",
            success: function(data) {
                if(data.code === 200){
                    layer.msg(data.msg, {
                      icon: 1,
                      time: 1500 //2秒关闭（如果不配置，默认是3秒）
                    }, function(){
                        layer.closeAll();
                        window.parent.location.reload();
                        var index = parent.layer.getFrameIndex(window.name);
                        parent.layer.close(index);

                    });
                }else{
                     layer.msg(data.msg);
                     return false;
                }
            },
            error: function (XMLHttpRequest, textStatus, errorThrown) {
                layer.closeAll();
                if (textStatus == "timeout") {
                    layer.msg('请求超时！', {
                      icon: 2,
                      time: 1500 //2秒关闭（如果不配置，默认是3秒）
                    }, function(){
                        return false;
                    });
                } else {
                    layer.msg('服务器错误！', {
                      icon: 2,
                      time: 1500 //2秒关闭（如果不配置，默认是3秒）
                    }, function(){
                        return false;
                    });
                }
            },
        });
    });
});

</script>

<style>
.upload-item {
  display: inline-block;
  position: relative;
  margin: 5px;
  border: 1px solid #ddd;
  border-radius: 4px;
  overflow: hidden;
}

.upload-item .layui-upload-img {
  display: block;
  margin: 0;
}

.upload-item .upload-status {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0,0,0,0.7);
  color: white;
  text-align: center;
  padding: 2px;
  font-size: 12px;
}

.upload-tips {
  color: #999;
  font-size: 12px;
  margin-top: 10px;
}

.upload-tips .tip-item {
  margin: 5px 0;
}

/* 优化上传按钮样式 */
.layui-upload-button {
  background-color: #5FB878;
  border-color: #5FB878;
}

.layui-upload-button:hover {
  background-color: #009688;
  border-color: #009688;
}
</style>