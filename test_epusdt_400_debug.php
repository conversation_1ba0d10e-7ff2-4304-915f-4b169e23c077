<?php
/**
 * EPUSDT 400错误调试脚本
 */

echo "=== EPUSDT 400错误调试 ===\n\n";

$api_url = 'https://pay.jsdao.cc';
$api_key = 'B81BA11A6528EC298C7DD88C144B8D882568BC1D';

// 测试不同的可能性
$test_cases = [
    'case1' => [
        'name' => '标准格式（与您Postman中相同）',
        'data' => [
            'order_id' => 'TEST_ORDER_1751149316',
            'amount' => 100.5,
            'notify_url' => 'https://jsdao.cc/api/callback/pay?gateway=Epusdt',
            'redirect_url' => 'https://jsdao.cc/api/my/index'
        ]
    ],
    'case2' => [
        'name' => 'amount为整数',
        'data' => [
            'order_id' => 'TEST_ORDER_' . time(),
            'amount' => 100,
            'notify_url' => 'https://jsdao.cc/api/callback/pay?gateway=Epusdt',
            'redirect_url' => 'https://jsdao.cc/api/my/index'
        ]
    ],
    'case3' => [
        'name' => 'amount为字符串',
        'data' => [
            'order_id' => 'TEST_ORDER_' . time(),
            'amount' => '100.50',
            'notify_url' => 'https://jsdao.cc/api/callback/pay?gateway=Epusdt',
            'redirect_url' => 'https://jsdao.cc/api/my/index'
        ]
    ],
    'case4' => [
        'name' => '简化的URL',
        'data' => [
            'order_id' => 'TEST_ORDER_' . time(),
            'amount' => 100.5,
            'notify_url' => 'https://example.com/notify',
            'redirect_url' => 'https://example.com/redirect'
        ]
    ],
    'case5' => [
        'name' => '最小金额测试',
        'data' => [
            'order_id' => 'TEST_ORDER_' . time(),
            'amount' => 0.01,
            'notify_url' => 'https://jsdao.cc/api/callback/pay?gateway=Epusdt',
            'redirect_url' => 'https://jsdao.cc/api/my/index'
        ]
    ],
    'case6' => [
        'name' => '不包含redirect_url（可选参数）',
        'data' => [
            'order_id' => 'TEST_ORDER_' . time(),
            'amount' => 100.5,
            'notify_url' => 'https://jsdao.cc/api/callback/pay?gateway=Epusdt'
        ]
    ]
];

foreach ($test_cases as $case_id => $test_case) {
    echo "=== {$test_case['name']} ===\n";
    
    $data = $test_case['data'];
    $signature = epusdtSign($data, $api_key);
    $data['signature'] = $signature;
    
    echo "请求数据: " . json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES) . "\n";
    
    // 计算签名过程
    $temp_data = $data;
    unset($temp_data['signature']);
    ksort($temp_data);
    
    $sign_string = '';
    foreach ($temp_data as $key => $value) {
        if ($value === '' || $value === null) continue;
        if ($sign_string !== '') $sign_string .= '&';
        $sign_string .= $key . '=' . $value;
    }
    $sign_string .= $api_key;
    
    echo "签名字符串: " . $sign_string . "\n";
    echo "MD5签名: " . $signature . "\n";
    
    // 发送请求
    $response = sendRequest($api_url . '/api/v1/order/create-transaction', $data);
    echo "响应: " . $response . "\n";
    echo str_repeat("-", 80) . "\n\n";
}

// 额外测试：尝试不同的Content-Type
echo "=== 测试不同的Content-Type ===\n";
$test_data = [
    'order_id' => 'TEST_ORDER_' . time(),
    'amount' => 100.5,
    'notify_url' => 'https://jsdao.cc/api/callback/pay?gateway=Epusdt',
    'redirect_url' => 'https://jsdao.cc/api/my/index'
];
$test_data['signature'] = epusdtSign($test_data, $api_key);

$content_types = [
    'application/json',
    'application/json; charset=utf-8',
    'application/x-www-form-urlencoded'
];

foreach ($content_types as $content_type) {
    echo "测试Content-Type: " . $content_type . "\n";
    
    if ($content_type === 'application/x-www-form-urlencoded') {
        $post_data = http_build_query($test_data);
    } else {
        $post_data = json_encode($test_data);
    }
    
    $response = sendRequestWithContentType($api_url . '/api/v1/order/create-transaction', $post_data, $content_type);
    echo "响应: " . $response . "\n";
    echo str_repeat("-", 50) . "\n";
}

/**
 * EPUSDT官方签名函数
 */
function epusdtSign(array $parameter, string $signKey) {
    ksort($parameter);
    reset($parameter);
    $sign = '';
    
    foreach ($parameter as $key => $val) {
        if ($val == '') continue;
        if ($key != 'signature') {
            if ($sign != '') {
                $sign .= "&";
            }
            $sign .= "$key=$val";
        }
    }
    
    $sign = md5($sign . $signKey);
    return strtolower($sign);
}

/**
 * 发送HTTP请求
 */
function sendRequest($url, $data) {
    return sendRequestWithContentType($url, json_encode($data), 'application/json');
}

/**
 * 发送指定Content-Type的HTTP请求
 */
function sendRequestWithContentType($url, $post_data, $content_type) {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $post_data);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: ' . $content_type,
        'Accept: application/json',
        'User-Agent: Mozilla/5.0 (compatible; EPUSDT-Client/1.0)'
    ]);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($response === false) {
        return "cURL错误: " . $error;
    }
    
    return "HTTP " . $httpCode . " | " . $response;
}

?>
