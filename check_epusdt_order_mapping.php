<?php
/**
 * 检查EPUSDT订单号映射
 */

echo "=== 检查EPUSDT订单号映射 ===\n\n";

try {
    $pdo = new PDO('mysql:host=127.0.0.1;dbname=tcyp;charset=utf8', 'tcyp', 'eDGSBtezJGfHx7eP');
    echo "✅ 数据库连接成功\n\n";
} catch (Exception $e) {
    echo "❌ 数据库连接失败: " . $e->getMessage() . "\n";
    exit;
}

$epusdt_order_id = '2942188878';

echo "查找EPUSDT订单号: {$epusdt_order_id}\n";
echo "----------------------------\n";

// 1. 查找订单映射
$stmt = $pdo->prepare("SELECT * FROM recharge WHERE epusdt_order_id = ?");
$stmt->execute([$epusdt_order_id]);
$order = $stmt->fetch(PDO::FETCH_ASSOC);

if ($order) {
    echo "✅ 找到订单映射:\n";
    echo "原始订单号: " . $order['order_no'] . "\n";
    echo "EPUSDT订单号: " . $order['epusdt_order_id'] . "\n";
    echo "用户ID: " . $order['mid'] . "\n";
    echo "金额: " . $order['money'] . "\n";
    echo "当前状态: " . $order['status'] . " (" . getStatusText($order['status']) . ")\n";
    echo "创建时间: " . date('Y-m-d H:i:s', $order['create_time']) . "\n";
    
    if ($order['pay_time']) {
        echo "支付时间: " . date('Y-m-d H:i:s', $order['pay_time']) . "\n";
    } else {
        echo "支付时间: 未支付\n";
    }
    
    echo "\n";
    
    // 2. 检查用户信息
    $stmt = $pdo->prepare("SELECT id, username, money FROM member WHERE id = ?");
    $stmt->execute([$order['mid']]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($user) {
        echo "✅ 用户信息:\n";
        echo "用户名: " . ($user['username'] ?? 'N/A') . "\n";
        echo "当前余额: " . $user['money'] . "\n\n";
    } else {
        echo "❌ 用户不存在\n\n";
    }
    
    // 3. 模拟回调处理
    echo "📝 如果处理这个回调:\n";
    echo "----------------------------\n";
    
    if ($order['status'] == 2) {
        echo "⚠️ 订单已经处理过了，不会重复处理\n";
    } else {
        echo "🔄 将执行以下操作:\n";
        echo "1. 更新订单状态: {$order['status']} → 2 (支付成功)\n";
        echo "2. 设置支付时间: " . date('Y-m-d H:i:s') . "\n";
        echo "3. 增加用户余额: {$user['money']} + {$order['money']} = " . ($user['money'] + $order['money']) . "\n";
        
        // 询问是否要实际执行
        echo "\n是否要实际执行这个回调处理? (输入 'yes' 确认): ";
        $handle = fopen("php://stdin", "r");
        $line = fgets($handle);
        fclose($handle);
        
        if (trim($line) === 'yes') {
            echo "\n🔄 执行回调处理...\n";
            
            $pdo->beginTransaction();
            try {
                // 更新订单状态
                $stmt = $pdo->prepare("UPDATE recharge SET status = 2, pay_time = ?, update_time = ? WHERE order_no = ? AND status != 2");
                $pay_time = time();
                $updateResult = $stmt->execute([$pay_time, $pay_time, $order['order_no']]);
                
                if ($updateResult && $stmt->rowCount() > 0) {
                    // 增加用户余额
                    $stmt = $pdo->prepare("UPDATE member SET money = money + ? WHERE id = ?");
                    $balanceResult = $stmt->execute([$order['money'], $order['mid']]);
                    
                    if ($balanceResult) {
                        $pdo->commit();
                        echo "✅ 回调处理成功!\n";
                        
                        // 显示更新后的状态
                        $stmt = $pdo->prepare("SELECT money FROM member WHERE id = ?");
                        $stmt->execute([$order['mid']]);
                        $newBalance = $stmt->fetchColumn();
                        
                        echo "✅ 用户新余额: " . $newBalance . "\n";
                    } else {
                        $pdo->rollback();
                        echo "❌ 用户余额更新失败\n";
                    }
                } else {
                    $pdo->rollback();
                    echo "⚠️ 订单状态未更新（可能已经处理过）\n";
                }
            } catch (Exception $e) {
                $pdo->rollback();
                echo "❌ 处理失败: " . $e->getMessage() . "\n";
            }
        } else {
            echo "❌ 取消执行\n";
        }
    }
    
} else {
    echo "❌ 未找到订单映射\n";
    echo "可能的原因:\n";
    echo "1. 订单号映射未正确保存\n";
    echo "2. EPUSDT订单号不匹配\n";
    echo "3. 数据库中没有对应的充值记录\n\n";
    
    // 查找最近的EPUSDT订单
    echo "📋 最近的EPUSDT订单:\n";
    echo "----------------------------\n";
    $stmt = $pdo->prepare("SELECT order_no, epusdt_order_id, money, status, FROM_UNIXTIME(create_time) as create_time FROM recharge WHERE epusdt_order_id IS NOT NULL ORDER BY create_time DESC LIMIT 5");
    $stmt->execute();
    $recent_orders = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if ($recent_orders) {
        foreach ($recent_orders as $recent_order) {
            echo "订单号: {$recent_order['order_no']}, EPUSDT: {$recent_order['epusdt_order_id']}, 金额: {$recent_order['money']}, 状态: {$recent_order['status']}, 时间: {$recent_order['create_time']}\n";
        }
    } else {
        echo "没有找到任何EPUSDT订单记录\n";
    }
}

function getStatusText($status) {
    switch ($status) {
        case 0: return '未支付';
        case 1: return '已支付';
        case 2: return '支付成功';
        case 3: return '充值失败';
        default: return '未知状态';
    }
}

echo "\n=== 检查完成 ===\n";
?>
