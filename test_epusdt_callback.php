<?php
/**
 * EPUSDT回调测试脚本
 * 用于测试EPUSDT回调处理是否正常
 */

// 模拟EPUSDT回调数据
$test_callback_data = [
    'order_id' => 'test123456',
    'amount' => 100.50,
    'status' => 2, // 2表示支付成功
    'notify_url' => 'https://jsdao.cc/api/callback/pay?gateway=Epusdt',
    'redirect_url' => 'https://vip.jsdao.cc/#/Mine',
    'signature' => '' // 稍后计算
];

// EPUSDT API密钥（从配置中获取）
$api_key = 'B81BA11A6528EC298C7DD88C144B8D882568BC1D';

// 计算签名（按照EPUSDT的签名算法）
$str = 'amount='.$test_callback_data['amount'].'&notify_url='.$test_callback_data['notify_url'].'&order_id='.$test_callback_data['order_id'].'&redirect_url='.$test_callback_data['redirect_url'].$api_key;
$test_callback_data['signature'] = md5($str);

echo "=== EPUSDT回调测试 ===\n";
echo "1. 测试回调数据:\n";
echo json_encode($test_callback_data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n\n";

echo "2. 签名计算字符串: " . $str . "\n";
echo "3. 计算的签名: " . $test_callback_data['signature'] . "\n\n";

// 测试回调URL
$callback_url = 'https://jsdao.cc/api/callback/pay?gateway=Epusdt';
echo "4. 回调URL: " . $callback_url . "\n\n";

// 发送测试回调请求
echo "5. 发送测试回调请求...\n";

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $callback_url);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($test_callback_data));
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'User-Agent: EPUSDT-Test'
]);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 30);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

$response = curl_exec($ch);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

echo "6. 回调响应:\n";
echo "HTTP状态码: " . $http_code . "\n";
echo "响应内容: " . $response . "\n";
if ($error) {
    echo "CURL错误: " . $error . "\n";
}

echo "\n=== 检查回调日志文件 ===\n";
$log_files = [
    'application/callback_pay_Epusdt.log',
    'application/callback_pay_Epusdt_final.log'
];

foreach ($log_files as $log_file) {
    if (file_exists($log_file)) {
        echo "日志文件 {$log_file} 存在，最新内容:\n";
        $content = file_get_contents($log_file);
        echo substr($content, -1000) . "\n"; // 显示最后1000字符
    } else {
        echo "日志文件 {$log_file} 不存在\n";
    }
}

echo "\n=== 检查数据库中的充值记录 ===\n";
// 这里需要连接数据库检查，但由于没有数据库连接，我们只提供SQL查询
echo "请在数据库中执行以下查询检查充值记录:\n";
echo "SELECT * FROM recharge WHERE order_no = 'test123456' OR epusdt_order_id = 'test123456';\n";
echo "SELECT * FROM member WHERE id = 1; -- 检查用户余额\n";

echo "\n=== 测试完成 ===\n";
echo "如果回调正常，应该看到:\n";
echo "1. HTTP状态码: 200\n";
echo "2. 响应内容: ok\n";
echo "3. 生成了回调日志文件\n";
echo "4. 数据库中订单状态更新为2（成功）\n";
echo "5. 用户余额增加\n";
?>
