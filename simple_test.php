<?php
/**
 * 简单的EPUSDT连接测试
 */

echo "=== 简单连接测试 ===\n";

$url = 'https://pay.jsdao.cc/api/v1/order/create-transaction';
$data = [
    'order_id' => 'TEST_ORDER_' . time(),
    'amount' => '100.5',
    'notify_url' => 'https://jsdao.cc/api/callback/pay?gateway=Epusdt',
    'redirect_url' => 'https://jsdao.cc/api/my/index',
    'signature' => '7d3fe9a2d50f69064c7eb5244e3ce650'
];

echo "测试URL: " . $url . "\n";
echo "测试数据: " . json_encode($data, JSON_PRETTY_PRINT) . "\n\n";

// 方法1: 标准cURL
echo "方法1: 标准cURL\n";
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $url);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
curl_setopt($ch, CURLOPT_TIMEOUT, 10);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

if ($response === false) {
    echo "错误: " . $error . "\n";
} else {
    echo "HTTP状态码: " . $httpCode . "\n";
    echo "响应: " . $response . "\n";
}

echo "\n" . str_repeat("-", 50) . "\n\n";

// 方法2: 先测试根路径
echo "方法2: 测试根路径\n";
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'https://pay.jsdao.cc');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 5);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

if ($response === false) {
    echo "根路径错误: " . $error . "\n";
} else {
    echo "根路径HTTP状态码: " . $httpCode . "\n";
    echo "根路径响应: " . substr($response, 0, 100) . "...\n";
}

echo "\n" . str_repeat("-", 50) . "\n\n";

// 方法3: 使用不同的User-Agent
echo "方法3: 使用浏览器User-Agent\n";
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $url);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
]);
curl_setopt($ch, CURLOPT_TIMEOUT, 10);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

if ($response === false) {
    echo "错误: " . $error . "\n";
} else {
    echo "HTTP状态码: " . $httpCode . "\n";
    echo "响应: " . $response . "\n";
}

?>
