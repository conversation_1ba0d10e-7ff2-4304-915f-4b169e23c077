# 选妃素材分类管理功能

## 功能概述

为选妃管理系统新增了男生素材多图片上传功能，同时明确区分女生素材和男生素材。原有的图片上传功能保持为女生素材，新增的男生素材单独管理，确保接口的向后兼容性。

## 数据库更新

### 1. 执行SQL更新
```sql
-- 为xuanfei_list表添加male_images字段
ALTER TABLE `xuanfei_list` ADD COLUMN `male_images` TEXT NULL COMMENT '男生素材图片JSON数据' AFTER `video_size`;
```

### 2. 字段说明
- `male_images`: TEXT类型，存储JSON格式的图片路径数组
- 示例数据：`["uploads/xuanfei/2024/01/01/image1.jpg", "uploads/xuanfei/2024/01/01/image2.jpg"]`

## 后台管理功能

### 1. 添加/编辑选妃页面
- 原有的"多图片上传"重命名为"女生素材多图片上传"
- 新增"男生素材多图片上传"区域
- 支持选择多张图片同时上传
- 实时预览已上传的图片
- 编辑时显示已有的女生/男生素材图片

### 2. 选妃列表页面
- 原有的"封面图片"列重命名为"女生素材"
- 新增"男生素材"列，显示图片数量
- 点击"查看"按钮可预览所有素材图片
- 支持点击图片放大查看

### 3. 上传功能
- 复用现有的图片上传接口 `/admin/xuanfeilist/doupload`
- 支持多图片批量上传
- 自动生成完整的图片URL路径

### 4. 验证逻辑
- **灵活验证** - 只需要上传女生素材或男生素材中的任意一种即可
- **前端验证** - JavaScript检查至少有一种素材不为空
- **后端验证** - PHP控制器和模型层双重验证
- **兼容性** - 支持只上传女生素材、只上传男生素材、或同时上传两种

## API接口

### 1. 获取选妃详情（原有接口，仅女生素材）
```
POST /api/xuanfei/xuanfeidata
参数: id (选妃ID)
返回: 选妃详细信息，保持原有格式（img_url为女生素材）
```

### 2. 获取女生素材专用接口
```
POST /api/xuanfei/femaleImages
参数: id (选妃ID)
返回: 女生素材图片URL数组
```

### 3. 获取男生素材专用接口
```
POST /api/xuanfei/maleImages
参数: id (选妃ID)
返回: 男生素材图片URL数组
```

### 4. 获取选妃列表（原有接口，仅女生素材）
```
POST /api/xuanfei/xuanfeilist
参数: id (地区ID)
返回: 选妃列表，保持原有格式（img_url为女生素材）
```

## 前端APP集成

### 1. 获取女生素材图片
```javascript
// 获取指定选妃的女生素材
fetch('/api/xuanfei/femaleImages', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'lang': 'yn_yu'
    },
    body: 'id=1'
})
.then(response => response.json())
.then(data => {
    if(data.code === 200) {
        console.log('女生素材图片:', data.data);
    }
});
```

### 2. 获取男生素材图片
```javascript
// 获取指定选妃的男生素材
fetch('/api/xuanfei/maleImages', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'lang': 'yn_yu'
    },
    body: 'id=1'
})
.then(response => response.json())
.then(data => {
    if(data.code === 200) {
        console.log('男生素材图片:', data.data);
    }
});
```

### 3. 从选妃详情获取（原有接口）
```javascript
// 获取选妃详情（仅包含女生素材）
fetch('/api/xuanfei/xuanfeidata', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'lang': 'yn_yu'
    },
    body: 'id=1'
})
.then(response => response.json())
.then(data => {
    if(data.code === 200) {
        // 选妃基本信息
        console.log('选妃名称:', data.data.xuanfei_name);
        console.log('女生素材图片:', data.data.img_url);
        // 注意：此接口不包含男生素材，需要单独调用maleImages接口
    }
});
```

## 返回数据格式

### 选妃详情返回示例（原有接口）
```json
{
    "code": 200,
    "msg": "获取成功",
    "data": {
        "id": 1,
        "xuanfei_name": "选妃名称",
        "img_url": ["http://domain.com/female1.jpg", "http://domain.com/female2.jpg"],
        "video_url": "http://domain.com/video.mp4",
        "class_id": 1
    }
}
```

### 女生素材专用接口返回示例
```json
{
    "code": 200,
    "msg": "获取成功",
    "data": [
        "http://domain.com/female1.jpg",
        "http://domain.com/female2.jpg"
    ]
}
```

### 男生素材专用接口返回示例
```json
{
    "code": 200,
    "msg": "获取成功",
    "data": [
        "http://domain.com/male1.jpg",
        "http://domain.com/male2.jpg",
        "http://domain.com/male3.jpg"
    ]
}
```

## 测试方法

1. 执行数据库更新SQL
2. 访问后台选妃管理页面，测试图片上传功能
3. 使用 `test_male_images_api.php` 测试API接口
4. 在前端APP中集成API调用

## 注意事项

1. 图片上传使用现有的OSS配置，支持阿里云OSS和本地存储
2. 图片URL会自动添加完整的域名前缀
3. 男生素材字段为可选，不影响现有功能
4. API接口保持向后兼容，原有接口不包含男生素材，需要调用专门的接口获取
5. 建议在APP中对男生素材图片进行懒加载优化

## 文件修改清单

### 后台文件
- `application/admin/view/xuanfeilist/operation.html` - 添加上传界面
- `application/admin/view/xuanfeilist/xuanfeilist.html` - 添加列表显示
- `application/admin/model/XuanfeilistModel.php` - 添加数据处理

### API文件
- `application/api/controller/Xuanfei.php` - 添加新接口
- `application/api/model/XuanfeiModel.php` - 添加数据处理

### 数据库
- `add_male_images_field.sql` - 数据库结构更新

### 测试文件
- `test_male_images_api.php` - API接口测试
