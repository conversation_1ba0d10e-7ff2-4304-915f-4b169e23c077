<?php
/**
 * OSS对象存储配置文件
 * Object Storage Service Configuration
 */

return [
    // 是否启用OSS上传 (true: OSS上传, false: 本地上传)
    'enable' => false,
    
    // 阿里云OSS配置
    'aliyun' => [
        'access_key_id' => '',
        'access_key_secret' => '',
        'endpoint' => '', // 例如: https://oss-cn-hangzhou.aliyuncs.com
        'bucket' => '',
        'domain' => '', // 自定义域名，如果有的话
    ],
    
    // 腾讯云COS配置
    'tencent' => [
        'secret_id' => '',
        'secret_key' => '',
        'region' => '', // 例如: ap-beijing
        'bucket' => '',
        'domain' => '',
    ],
    
    // 七牛云配置
    'qiniu' => [
        'access_key' => '',
        'secret_key' => '',
        'bucket' => '',
        'domain' => '',
    ],
    
    // 当前使用的OSS服务商 (aliyun, tencent, qiniu)
    'provider' => 'aliyun',
    
    // 上传路径配置
    'upload_path' => [
        'video' => 'video/', // 视频文件路径
        'image' => 'images/', // 图片文件路径
        'document' => 'documents/', // 文档文件路径
    ],
    
    // 允许的文件类型
    'allowed_types' => [
        'image' => ['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp'],
        'video' => ['mp4', 'avi', 'mov', 'wmv', 'flv', 'mkv', 'webm', 'm4v', '3gp'],
        'document' => ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt'],
    ],

    // 文件大小限制 (字节)
    'max_size' => [
        'image' => 10 * 1024 * 1024, // 10MB
        'video' => 2 * 1024 * 1024 * 1024, // 2GB
        'document' => 20 * 1024 * 1024, // 20MB
    ],
];
