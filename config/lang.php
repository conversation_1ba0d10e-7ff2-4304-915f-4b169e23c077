<?php
/**
 * 多语言配置文件
 * Multilingual Configuration
 */

return [
    // 默认语言设置
    'default_lang' => 'es-es',  // 可选: ko-kr, zh-cn, vi-vn, es-es
    
    // 支持的语言列表
    'supported_langs' => [
        'ko-kr' => [
            'name' => '한국어',
            'name_en' => 'Korean',
            'tips_field' => 'ko_hy',
            'enabled' => true
        ],
        'zh-cn' => [
            'name' => '中文',
            'name_en' => 'Chinese',
            'tips_field' => 'zh_cn',
            'enabled' => true
        ],
        'vi-vn' => [
            'name' => 'Tiếng Việt',
            'name_en' => 'Vietnamese',
            'tips_field' => 'vi_vn',
            'enabled' => true
        ],
        'es-es' => [
            'name' => 'Español',
            'name_en' => 'Spanish',
            'tips_field' => 'es_es',
            'enabled' => true
        ],
        'en-us' => [
            'name' => 'English',
            'name_en' => 'English',
            'tips_field' => 'en_us',
            'enabled' => false  // 暂未启用
        ]
    ],
    
    // 语言检测优先级
    'detection_priority' => [
        'header',    // 优先从请求头获取
        'post',      // 其次从POST参数获取
        'get',       // 最后从GET参数获取
        'default'    // 使用默认语言
    ],
    
    // 语言参数名称
    'lang_param_name' => 'lang',
    
    // 语言请求头名称
    'lang_header_name' => 'lang',
    
    // 是否启用语言缓存
    'enable_cache' => true,
    
    // 回退策略
    'fallback_strategy' => 'default', // default | first_available | none
];
?>
