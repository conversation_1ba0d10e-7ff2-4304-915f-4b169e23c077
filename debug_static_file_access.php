<?php
/**
 * 调试静态文件访问问题
 */

echo "=== 静态文件访问调试 ===\n\n";

// 1. 检查文件是否真的存在
echo "1. 文件存在性检查:\n";
$testFile = "public/base/ico/2025/08/27/20250827166f56b2d895f3b13.jpg";
$alternativeFiles = glob("public/base/ico/2025/08/27/*166f56b2d895f*");

echo "目标文件: $testFile\n";
echo "文件是否存在: " . (file_exists($testFile) ? "是" : "否") . "\n";

if (!empty($alternativeFiles)) {
    echo "找到相似文件:\n";
    foreach ($alternativeFiles as $file) {
        echo "  - $file\n";
        echo "    大小: " . filesize($file) . " 字节\n";
        echo "    可读: " . (is_readable($file) ? "是" : "否") . "\n";
    }
}

// 2. 检查所有今日上传的文件
echo "\n2. 今日上传文件列表:\n";
$todayDir = "public/base/ico/2025/08/27/";
if (is_dir($todayDir)) {
    $files = glob($todayDir . "*");
    echo "目录: $todayDir\n";
    echo "文件数量: " . count($files) . "\n";
    
    foreach ($files as $file) {
        $fileName = basename($file);
        $fileSize = filesize($file);
        $relativePath = str_replace('public/', '', $file);
        $url = "https://jsdao.cc/$relativePath";
        
        echo "  文件: $fileName\n";
        echo "    大小: {$fileSize}字节\n";
        echo "    URL: $url\n";
        echo "\n";
    }
}

// 3. Web服务器配置问题分析
echo "3. Web服务器配置问题:\n";
echo "当前nginx.htaccess配置会将所有请求重定向到index.php\n";
echo "这导致静态文件请求也被重定向，而不是直接提供文件\n\n";

// 4. 解决方案
echo "4. 解决方案:\n";
echo "方案1: 修改nginx配置（推荐）\n";
echo "方案2: 创建静态文件访问脚本\n";
echo "方案3: 使用CDN或外部存储\n\n";

// 5. 创建临时静态文件访问脚本
echo "5. 创建临时解决方案:\n";

$staticAccessScript = 'public/static_file.php';
$scriptContent = '<?php
/**
 * 静态文件访问脚本
 * 临时解决nginx配置问题
 */

// 获取请求的文件路径
$requestUri = $_SERVER["REQUEST_URI"] ?? "";
$path = parse_url($requestUri, PHP_URL_PATH);

// 移除开头的斜杠
$path = ltrim($path, "/");

// 安全检查：防止目录遍历攻击
if (strpos($path, "..") !== false || strpos($path, "\\") !== false) {
    http_response_code(403);
    echo "Access denied";
    exit;
}

// 构建文件路径
$filePath = __DIR__ . "/" . $path;

// 检查文件是否存在
if (!file_exists($filePath) || !is_file($filePath)) {
    http_response_code(404);
    echo "File not found";
    exit;
}

// 检查文件类型
$allowedExtensions = ["jpg", "jpeg", "png", "gif", "ico", "css", "js", "pdf", "txt"];
$extension = strtolower(pathinfo($filePath, PATHINFO_EXTENSION));

if (!in_array($extension, $allowedExtensions)) {
    http_response_code(403);
    echo "File type not allowed";
    exit;
}

// 设置MIME类型
$mimeTypes = [
    "jpg" => "image/jpeg",
    "jpeg" => "image/jpeg",
    "png" => "image/png", 
    "gif" => "image/gif",
    "ico" => "image/x-icon",
    "css" => "text/css",
    "js" => "application/javascript",
    "pdf" => "application/pdf",
    "txt" => "text/plain"
];

$mimeType = $mimeTypes[$extension] ?? "application/octet-stream";

// 输出文件
header("Content-Type: " . $mimeType);
header("Content-Length: " . filesize($filePath));
header("Cache-Control: public, max-age=31536000"); // 缓存1年
header("Expires: " . gmdate("D, d M Y H:i:s", time() + 31536000) . " GMT");

readfile($filePath);
?>';

file_put_contents($staticAccessScript, $scriptContent);
echo "已创建静态文件访问脚本: $staticAccessScript\n";

// 6. 修改nginx配置建议
echo "\n6. nginx配置修改建议:\n";
echo "在nginx配置文件中添加以下规则:\n\n";
echo "```nginx\n";
echo "server {\n";
echo "    listen 80;\n";
echo "    server_name jsdao.cc;\n";
echo "    root /path/to/your/project/public;\n";
echo "    index index.php index.html;\n";
echo "\n";
echo "    # 静态文件直接提供\n";
echo "    location ~* \\.(jpg|jpeg|png|gif|ico|css|js|pdf|txt|svg|woff|woff2|ttf|eot)$ {\n";
echo "        expires 1y;\n";
echo "        add_header Cache-Control \"public, immutable\";\n";
echo "        try_files \$uri =404;\n";
echo "    }\n";
echo "\n";
echo "    # 或者使用临时脚本\n";
echo "    location ~* \\.(jpg|jpeg|png|gif|ico)$ {\n";
echo "        try_files \$uri /static_file.php;\n";
echo "    }\n";
echo "\n";
echo "    # PHP处理\n";
echo "    location ~ \\.php$ {\n";
echo "        try_files \$uri =404;\n";
echo "        fastcgi_pass 127.0.0.1:9000;\n";
echo "        fastcgi_index index.php;\n";
echo "        fastcgi_param SCRIPT_FILENAME \$document_root\$fastcgi_script_name;\n";
echo "        include fastcgi_params;\n";
echo "    }\n";
echo "\n";
echo "    # 其他请求交给ThinkPHP\n";
echo "    location / {\n";
echo "        try_files \$uri \$uri/ /index.php?\$query_string;\n";
echo "    }\n";
echo "}\n";
echo "```\n";

echo "\n=== 调试完成 ===\n";
echo "\n立即可用的解决方案:\n";
echo "1. 使用临时访问脚本测试图片\n";
echo "2. 联系服务器管理员修改nginx配置\n";
echo "3. 重启nginx服务\n";
?>
