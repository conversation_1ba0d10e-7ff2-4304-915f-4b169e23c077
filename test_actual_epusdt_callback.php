<?php
/**
 * 测试实际的EPUSDT回调数据
 */

echo "=== 测试实际EPUSDT回调数据 ===\n\n";

// 实际收到的回调数据
$actual_callback = [
    "trade_id" => "202506291751162447084026",
    "order_id" => "2942188878", 
    "amount" => 23,
    "actual_amount" => 1.22,
    "token" => "TYayP7F7bHYeH7FMx5nbJH7N4p5drPdi7T",
    "block_transaction_id" => "",
    "signature" => "531e07c163b221700f655181d31a1736",
    "status" => 2
];

$api_key = 'B81BA11A6528EC298C7DD88C144B8D882568BC1D';

echo "1. 实际回调数据:\n";
echo json_encode($actual_callback, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n\n";

// 测试不同的签名算法
echo "2. 测试签名验证:\n";
echo "----------------------------\n";

// 方法1：按字母顺序排序所有参数
$params1 = [
    'trade_id' => $actual_callback['trade_id'],
    'order_id' => $actual_callback['order_id'],
    'amount' => $actual_callback['amount'],
    'actual_amount' => $actual_callback['actual_amount'],
    'token' => $actual_callback['token'],
    'status' => $actual_callback['status']
];

ksort($params1);
$signString1 = '';
foreach ($params1 as $key => $value) {
    if ($value !== '') {
        if ($signString1 !== '') {
            $signString1 .= '&';
        }
        $signString1 .= $key . '=' . $value;
    }
}
$signString1 .= $api_key;
$signature1 = md5($signString1);

echo "方法1 - 按字母顺序排序:\n";
echo "签名字符串: " . $signString1 . "\n";
echo "计算签名: " . $signature1 . "\n";
echo "实际签名: " . $actual_callback['signature'] . "\n";
echo "验证结果: " . ($signature1 === $actual_callback['signature'] ? '✅ 成功' : '❌ 失败') . "\n\n";

// 方法2：按原始顺序
$signString2 = 'trade_id=' . $actual_callback['trade_id'] . 
               '&order_id=' . $actual_callback['order_id'] . 
               '&amount=' . $actual_callback['amount'] . 
               '&actual_amount=' . $actual_callback['actual_amount'] . 
               '&token=' . $actual_callback['token'] . 
               '&status=' . $actual_callback['status'] . 
               $api_key;
$signature2 = md5($signString2);

echo "方法2 - 按原始顺序:\n";
echo "签名字符串: " . $signString2 . "\n";
echo "计算签名: " . $signature2 . "\n";
echo "验证结果: " . ($signature2 === $actual_callback['signature'] ? '✅ 成功' : '❌ 失败') . "\n\n";

// 方法3：只包含关键参数
$signString3 = 'order_id=' . $actual_callback['order_id'] . 
               '&amount=' . $actual_callback['amount'] . 
               '&status=' . $actual_callback['status'] . 
               $api_key;
$signature3 = md5($signString3);

echo "方法3 - 只包含关键参数:\n";
echo "签名字符串: " . $signString3 . "\n";
echo "计算签名: " . $signature3 . "\n";
echo "验证结果: " . ($signature3 === $actual_callback['signature'] ? '✅ 成功' : '❌ 失败') . "\n\n";

// 方法4：不包含空值的block_transaction_id
$params4 = [];
foreach ($actual_callback as $key => $value) {
    if ($key !== 'signature' && $value !== '') {
        $params4[$key] = $value;
    }
}
ksort($params4);
$signString4 = '';
foreach ($params4 as $key => $value) {
    if ($signString4 !== '') {
        $signString4 .= '&';
    }
    $signString4 .= $key . '=' . $value;
}
$signString4 .= $api_key;
$signature4 = md5($signString4);

echo "方法4 - 排除空值和signature:\n";
echo "签名字符串: " . $signString4 . "\n";
echo "计算签名: " . $signature4 . "\n";
echo "验证结果: " . ($signature4 === $actual_callback['signature'] ? '✅ 成功' : '❌ 失败') . "\n\n";

// 方法5：尝试不同的参数组合
$combinations = [
    ['trade_id', 'order_id', 'amount', 'actual_amount', 'status'],
    ['order_id', 'amount', 'actual_amount', 'token', 'status'],
    ['trade_id', 'order_id', 'amount', 'status'],
    ['order_id', 'amount', 'status'],
];

echo "方法5 - 测试不同参数组合:\n";
foreach ($combinations as $index => $fields) {
    $signString = '';
    foreach ($fields as $field) {
        if (isset($actual_callback[$field])) {
            if ($signString !== '') {
                $signString .= '&';
            }
            $signString .= $field . '=' . $actual_callback[$field];
        }
    }
    $signString .= $api_key;
    $signature = md5($signString);
    
    echo "组合" . ($index + 1) . " [" . implode(', ', $fields) . "]:\n";
    echo "  签名字符串: " . $signString . "\n";
    echo "  计算签名: " . $signature . "\n";
    echo "  验证结果: " . ($signature === $actual_callback['signature'] ? '✅ 成功' : '❌ 失败') . "\n\n";
}

// 检查订单号映射
echo "3. 检查订单号映射:\n";
echo "----------------------------\n";
echo "EPUSDT订单号: " . $actual_callback['order_id'] . "\n";
echo "需要在数据库中查找 epusdt_order_id = '2942188878' 对应的原始订单号\n\n";

// 模拟订单处理
echo "4. 模拟订单处理:\n";
echo "----------------------------\n";
if ($actual_callback['status'] == 2) {
    echo "✅ 支付状态: 成功 (status = 2)\n";
    echo "💰 支付金额: " . $actual_callback['actual_amount'] . " USDT\n";
    echo "🏦 收款地址: " . $actual_callback['token'] . "\n";
    echo "📋 交易ID: " . $actual_callback['trade_id'] . "\n";
    
    if (!empty($actual_callback['block_transaction_id'])) {
        echo "🔗 区块链交易ID: " . $actual_callback['block_transaction_id'] . "\n";
    } else {
        echo "⏳ 区块链交易ID: 暂未确认\n";
    }
    
    echo "\n📝 处理步骤:\n";
    echo "1. 根据 order_id='2942188878' 查找原始订单号\n";
    echo "2. 更新订单状态为已支付 (status=2)\n";
    echo "3. 增加用户余额 (根据订单中的money字段)\n";
    echo "4. 记录支付时间\n";
} else {
    echo "❌ 支付状态: 失败 (status = " . $actual_callback['status'] . ")\n";
}

echo "\n=== 测试完成 ===\n";
echo "请检查哪种签名验证方法成功，然后更新代码中的签名算法。\n";
?>
