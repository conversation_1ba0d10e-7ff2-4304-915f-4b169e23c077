<?php
/**
 * 调试ThinkPHP上传工作目录
 */

echo "=== ThinkPHP上传目录调试 ===\n\n";

// 1. 检查当前工作目录
echo "1. 工作目录信息:\n";
echo "当前工作目录: " . getcwd() . "\n";
echo "脚本所在目录: " . __DIR__ . "\n";
echo "public目录路径: " . __DIR__ . "/public\n";

// 2. 检查ThinkPHP的根目录设置
echo "\n2. ThinkPHP目录设置:\n";
if (defined('APP_PATH')) {
    echo "APP_PATH: " . APP_PATH . "\n";
} else {
    echo "APP_PATH: 未定义\n";
}

// 3. 模拟ThinkPHP的文件上传逻辑
echo "\n3. 模拟文件上传路径处理:\n";

// 测试不同的路径输入
$testPaths = [
    "base/ico/2025/08/27/",
    "public/base/ico/2025/08/27/",
    "./base/ico/2025/08/27/",
    "./public/base/ico/2025/08/27/"
];

foreach ($testPaths as $testPath) {
    echo "\n测试路径: '$testPath'\n";
    
    // 检查路径是否存在
    $exists = is_dir($testPath);
    echo "  路径存在: " . ($exists ? "是" : "否") . "\n";
    
    // 检查绝对路径
    $realPath = realpath($testPath);
    echo "  绝对路径: " . ($realPath ?: "无法解析") . "\n";
    
    // 如果路径不存在，尝试创建
    if (!$exists) {
        echo "  尝试创建目录...\n";
        if (mkdir($testPath, 0755, true)) {
            echo "  创建成功\n";
            // 立即删除测试目录
            rmdir($testPath);
        } else {
            echo "  创建失败\n";
        }
    }
}

// 4. 检查实际保存的文件
echo "\n4. 检查实际保存的文件:\n";
$actualPaths = [
    "public/base/ico/2025/08/27/",
    "public/public/base/ico/20250827/"
];

foreach ($actualPaths as $path) {
    echo "\n检查路径: $path\n";
    if (is_dir($path)) {
        $files = glob($path . "*");
        echo "  文件数量: " . count($files) . "\n";
        foreach ($files as $file) {
            $fileName = basename($file);
            $fileSize = filesize($file);
            echo "  - $fileName ({$fileSize}字节)\n";
        }
    } else {
        echo "  路径不存在\n";
    }
}

// 5. 分析问题原因
echo "\n5. 问题分析:\n";
echo "根据实际文件保存位置 'public/public/base/ico/'，可以确定:\n";
echo "1. ThinkPHP的file->move()方法是相对于项目根目录工作的\n";
echo "2. 我的修复逻辑错误地添加了public/前缀\n";
echo "3. 正确的路径应该直接使用System控制器传递的路径\n";

// 6. 正确的修复方案
echo "\n6. 正确的修复方案:\n";
echo "应该移除我添加的public/前缀处理逻辑\n";
echo "让ThinkPHP直接使用原始路径: 'base/ico/2025/08/27/'\n";

echo "\n=== 调试完成 ===\n";
?>
