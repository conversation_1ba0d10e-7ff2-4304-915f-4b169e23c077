-- 分步执行：更新银行数据
-- 请按顺序逐步执行，每执行一步检查结果

-- 步骤1：查看当前表结构
DESCRIBE `bank_list`;

-- 步骤2：查看当前数据
SELECT * FROM `bank_list` LIMIT 5;

-- 步骤3：禁用现有银行（根据步骤1的结果，如果没有update_time字段就去掉）
UPDATE `bank_list` SET `status` = 0;

-- 步骤4：插入墨西哥银行数据（基础版本）
INSERT INTO `bank_list` (`name`, `status`) VALUES ('BBVA México', 1);
INSERT INTO `bank_list` (`name`, `status`) VALUES ('Banamex', 1);
INSERT INTO `bank_list` (`name`, `status`) VALUES ('Banorte', 1);
INSERT INTO `bank_list` (`name`, `status`) VALUES ('Santander México', 1);
INSERT INTO `bank_list` (`name`, `status`) VALUES ('HSBC México', 1);
INSERT INTO `bank_list` (`name`, `status`) VALUES ('Banco Azteca', 1);

-- 步骤5：验证结果
SELECT * FROM `bank_list` WHERE `status` = 1;

-- 如果bank表也需要更新，请继续执行以下步骤：

-- 步骤6：查看bank表结构
DESCRIBE `bank`;

-- 步骤7：查看bank表当前数据
SELECT * FROM `bank` LIMIT 5;

-- 步骤8：禁用bank表现有数据
UPDATE `bank` SET `status` = 0;

-- 步骤9：插入bank表数据（根据实际字段调整）
INSERT INTO `bank` (`title`, `name`, `status`) VALUES ('BBVA México', 'BBVA México', 1);
INSERT INTO `bank` (`title`, `name`, `status`) VALUES ('Banamex', 'Banamex', 1);
INSERT INTO `bank` (`title`, `name`, `status`) VALUES ('Banorte', 'Banorte', 1);
INSERT INTO `bank` (`title`, `name`, `status`) VALUES ('Santander México', 'Santander México', 1);
INSERT INTO `bank` (`title`, `name`, `status`) VALUES ('HSBC México', 'HSBC México', 1);
INSERT INTO `bank` (`title`, `name`, `status`) VALUES ('Banco Azteca', 'Banco Azteca', 1);

-- 步骤10：验证bank表结果
SELECT * FROM `bank` WHERE `status` = 1;
