<?php
/**
 * 基于eppsost.php demo逻辑的EPUSDT测试
 */

echo "=== 基于eppsost.php demo逻辑的EPUSDT测试 ===\n\n";

// 完全按照demo的curl_request函数
function curl_request($url, $data=null, $method='post', $header = array("content-type: application/json"), $https=true, $timeout = 5){
    $method = strtoupper($method);
    $ch = curl_init();//初始化
    curl_setopt($ch, CURLOPT_URL, $url);//访问的URL
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);//只获取页面内容，但不输出
    if($https){
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);//https请求 不验证证书
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);//https请求 不验证HOST
    }
    if ($method != "GET") {
        if($method == 'POST'){
            curl_setopt($ch, CURLOPT_POST, true);//请求方式为post请求
        }
        if ($method == 'PUT' || strtoupper($method) == 'DELETE') {
            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $method); //设置请求方式
        }
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);//请求数据
    }
    curl_setopt($ch, CURLOPT_TIMEOUT, $timeout);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $header); //模拟的header头
    //curl_setopt($ch, CURLOPT_HEADER, false);//设置不需要头信息
    $result = curl_exec($ch);//执行请求
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);//关闭curl，释放资源
    
    return [
        'http_code' => $httpCode,
        'result' => $result
    ];
}

function token($length){
    $str = md5(time());
    $token = substr($str,15,$length);
    return $token;
}//随机数生成函数

// 测试参数
$amount = 100.5;
$notify_url = 'https://jsdao.cc/api/callback/pay?gateway=Epusdt';
$redirect_url = 'https://jsdao.cc/api/my/index';
$order_id = 'TEST_ORDER_' . time();
$key = 'B81BA11A6528EC298C7DD88C144B8D882568BC1D';

echo "测试参数:\n";
echo "amount: " . $amount . "\n";
echo "notify_url: " . $notify_url . "\n";
echo "redirect_url: " . $redirect_url . "\n";
echo "order_id: " . $order_id . "\n";
echo "key: " . $key . "\n\n";

// 拼接字符串用于MD5计算
$str = 'amount='.$amount.'&notify_url='.$notify_url.'&order_id='.$order_id.'&redirect_url='.$redirect_url.$key;
echo "签名字符串: " . $str . "\n";

// 用MD5算法计算签名
$signature = md5($str);
echo "MD5签名: " . $signature . "\n\n";

// 生成数据包，用到了的数组转json的jsonencode
$data = json_encode(array(
    'order_id' => $order_id,
    'amount' => $amount,
    'notify_url' => $notify_url,
    'redirect_url' => $redirect_url,
    'signature' => $signature
));

echo "请求数据包:\n";
echo $data . "\n\n";

// 发起Curl请求并获取返回数据到变量
$response = curl_request('https://pay.jsdao.cc/api/v1/order/create-transaction', $data, 'post');

echo "HTTP状态码: " . $response['http_code'] . "\n";
echo "原始响应: " . $response['result'] . "\n\n";

if ($response['http_code'] == 200) {
    // 对返回数据进行json到数组的转换，用到了jsondecode
    $arr = json_decode($response['result'], true);
    
    if ($arr && isset($arr['data'])) {
        // 提取返回数据的数组中的data段落
        $resdata = $arr['data'];
        
        // 提取返回数据的数组中的data段落中的支付链接和转换后数值
        $payurl = isset($resdata['payment_url']) ? $resdata['payment_url'] : '';
        $payamount = isset($resdata['actual_amount']) ? $resdata['actual_amount'] : $amount;
        
        echo "=== 解析结果 ===\n";
        echo "你的支付链接是: " . $payurl . "\n";
        echo "你的计划支付金额是: " . $amount . "CNY\n";
        echo "你的实际交易是: " . $payamount . "USD\n";
        
        if (!empty($payurl)) {
            echo "支付页面链接: " . $payurl . "\n";
            echo "✅ 创建支付订单成功！\n";
        } else {
            echo "❌ 获取支付链接失败\n";
        }
    } else {
        echo "❌ 响应数据格式错误\n";
        echo "完整响应: " . $response['result'] . "\n";
    }
} else {
    echo "❌ HTTP请求失败，状态码: " . $response['http_code'] . "\n";
    echo "错误响应: " . $response['result'] . "\n";
}

// 额外测试：尝试不同的参数组合
echo "\n" . str_repeat("=", 60) . "\n";
echo "=== 额外测试：简化参数 ===\n";

$simple_amount = 1;
$simple_notify_url = 'https://example.com/notify';
$simple_redirect_url = 'https://example.com/redirect';
$simple_order_id = 'SIMPLE_' . time();

$simple_str = 'amount='.$simple_amount.'&notify_url='.$simple_notify_url.'&order_id='.$simple_order_id.'&redirect_url='.$simple_redirect_url.$key;
$simple_signature = md5($simple_str);

$simple_data = json_encode(array(
    'order_id' => $simple_order_id,
    'amount' => $simple_amount,
    'notify_url' => $simple_notify_url,
    'redirect_url' => $simple_redirect_url,
    'signature' => $simple_signature
));

echo "简化测试数据: " . $simple_data . "\n";
echo "签名字符串: " . $simple_str . "\n";
echo "签名: " . $simple_signature . "\n\n";

$simple_response = curl_request('https://pay.jsdao.cc/api/v1/order/create-transaction', $simple_data, 'post');
echo "简化测试结果: HTTP " . $simple_response['http_code'] . " | " . $simple_response['result'] . "\n";

?>
