<?php
/**
 * 详细测试上传功能
 */

echo "=== 详细上传测试 ===\n\n";

// 定义json_exit函数
function json_exit($code, $msg, $data = []) {
    echo "API响应: code=$code, msg=$msg\n";
    if (!empty($data)) {
        echo "数据: " . json_encode($data) . "\n";
    }
    if ($code != 200) {
        throw new Exception("API Error: $msg");
    }
}

// 1. 测试路径问题
echo "1. 路径测试:\n";
$systemPath = "base/ico/" . date('Y/m/d') . '/';
$publicPath = "public/" . $systemPath;

echo "System控制器传递的路径: $systemPath\n";
echo "实际需要的完整路径: $publicPath\n";
echo "System路径是否存在: " . (is_dir($systemPath) ? "是" : "否") . "\n";
echo "Public路径是否存在: " . (is_dir($publicPath) ? "是" : "否") . "\n";

// 创建目录测试
if (!is_dir($publicPath)) {
    echo "尝试创建public路径...\n";
    if (mkdir($publicPath, 0755, true)) {
        echo "Public路径创建成功\n";
    } else {
        echo "Public路径创建失败\n";
    }
}

// 2. 模拟OssModel的doupload方法逻辑
echo "\n2. 模拟OssModel->doupload逻辑:\n";

// 模拟文件上传数据
$_FILES = [
    'file' => [
        'name' => 'test.jpg',
        'type' => 'image/jpeg',
        'tmp_name' => '/tmp/test_upload',
        'error' => 0,
        'size' => 1024
    ]
];

// 创建一个临时测试文件
$testFile = '/tmp/test_upload';
file_put_contents($testFile, 'test image content');

echo "模拟文件信息:\n";
print_r($_FILES['file']);

// 检查文件扩展名
$temp = explode(".", $_FILES["file"]["name"]);
$extension = end($temp);
echo "文件扩展名: $extension\n";

// 验证文件类型
if (!in_array($extension, array("gif", "jpeg", "jpg", "png"))) {
    echo "文件类型不合法\n";
} else {
    echo "文件类型合法\n";
}

// 3. 测试ThinkPHP的file->move方法
echo "\n3. 测试文件移动:\n";

// 模拟ThinkPHP的文件对象
class MockFile {
    private $info;
    
    public function __construct($info) {
        $this->info = $info;
    }
    
    public function getInfo() {
        return $this->info;
    }
    
    public function move($path) {
        echo "尝试移动文件到: $path\n";
        
        // 检查目录是否存在
        if (!is_dir($path)) {
            echo "目录不存在，尝试创建...\n";
            if (mkdir($path, 0755, true)) {
                echo "目录创建成功\n";
            } else {
                echo "目录创建失败\n";
                return false;
            }
        }
        
        // 检查目录权限
        if (!is_writable($path)) {
            echo "目录不可写\n";
            return false;
        }
        
        // 生成文件名
        $fileName = date('YmdHis') . '_' . $this->info['name'];
        $targetPath = $path . '/' . $fileName;
        
        echo "目标文件路径: $targetPath\n";
        
        // 模拟文件移动
        if (copy($this->info['tmp_name'], $targetPath)) {
            echo "文件移动成功\n";
            return new MockFileInfo($fileName);
        } else {
            echo "文件移动失败\n";
            return false;
        }
    }
    
    public function getError() {
        return "模拟错误信息";
    }
}

class MockFileInfo {
    private $saveName;
    
    public function __construct($saveName) {
        $this->saveName = $saveName;
    }
    
    public function getSaveName() {
        return $this->saveName;
    }
}

// 测试不同路径
$testPaths = [
    $systemPath,  // System控制器传递的路径
    $publicPath,  // 加上public前缀的路径
    "public/base/ico/" . date('Y/m/d') . '/'  // 完整路径
];

foreach ($testPaths as $testPath) {
    echo "\n测试路径: $testPath\n";
    
    $mockFile = new MockFile($_FILES['file']);
    $result = $mockFile->move($testPath);
    
    if ($result) {
        echo "移动成功，文件名: " . $result->getSaveName() . "\n";
        
        // 生成URL路径
        $urlPath = str_replace('public/', '', $testPath);
        $relativePath = '/' . trim($urlPath, '/') . '/' . $result->getSaveName();
        echo "生成的URL路径: $relativePath\n";
    } else {
        echo "移动失败\n";
    }
}

// 4. 检查实际的错误原因
echo "\n4. 可能的错误原因分析:\n";

echo "a) 路径问题:\n";
echo "   - System控制器传递: $systemPath\n";
echo "   - 但OssModel可能期望: public/$systemPath\n";

echo "b) 权限问题:\n";
$baseDir = "public/base";
echo "   - base目录存在: " . (is_dir($baseDir) ? "是" : "否") . "\n";
echo "   - base目录可写: " . (is_writable($baseDir) ? "是" : "否") . "\n";

echo "c) ThinkPHP框架问题:\n";
echo "   - 可能是ThinkPHP版本兼容性问题\n";
echo "   - 或者是文件上传处理的问题\n";

// 5. 建议的修复方案
echo "\n5. 建议的修复方案:\n";
echo "a) 修改System控制器，传递完整路径\n";
echo "b) 或者修改OssModel，自动添加public前缀\n";
echo "c) 检查目录权限和创建逻辑\n";

// 清理测试文件
if (file_exists($testFile)) {
    unlink($testFile);
}

echo "\n=== 测试完成 ===\n";
?>
