<?php
/**
 * 测试APP下载链接API接口
 * 访问地址：http://your-domain.com/test_app_download_api.php
 */

// 测试API接口
function testAppDownloadApi() {
    // 假设你的域名是 localhost 或者你的实际域名
    $baseUrl = 'http://localhost'; // 请根据实际情况修改
    
    // 测试获取APP下载链接的API
    $apiUrl = $baseUrl . '/api/system/getAppDownloadUrl';
    
    echo "<h2>测试APP下载链接API</h2>";
    echo "<p>API地址: {$apiUrl}</p>";
    
    // 使用curl发送请求
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $apiUrl);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    echo "<h3>响应结果:</h3>";
    echo "<p>HTTP状态码: {$httpCode}</p>";
    echo "<p>响应内容:</p>";
    echo "<pre>" . htmlspecialchars($response) . "</pre>";
    
    // 解析JSON响应
    $data = json_decode($response, true);
    if ($data) {
        echo "<h3>解析后的数据:</h3>";
        echo "<pre>" . print_r($data, true) . "</pre>";
    }
}

// 执行测试
testAppDownloadApi();

echo "<hr>";
echo "<h2>使用说明</h2>";
echo "<p>1. 后台管理 -> 系统管理 -> 基本配置，在APP下载链接字段中输入下载链接</p>";
echo "<p>2. 前端可以通过以下API获取APP下载链接：</p>";
echo "<p><strong>GET /api/system/getAppDownloadUrl</strong></p>";
echo "<p>返回格式：</p>";
echo "<pre>{
    \"code\": 200,
    \"msg\": \"获取成功\",
    \"data\": {
        \"app_download_url\": \"https://example.com/app.apk\"
    }
}</pre>";
?>
