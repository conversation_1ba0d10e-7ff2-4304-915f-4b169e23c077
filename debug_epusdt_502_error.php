<?php
/**
 * EPUSDT 502错误调试脚本
 * 用于诊断和解决EPUSDT支付成功后的502错误问题
 */

echo "=== EPUSDT 502错误诊断 ===\n\n";

// 1. 检查EPUSDT系统状态
echo "1. 检查EPUSDT系统状态\n";
echo "----------------------------\n";

$epusdt_base_url = 'https://pay.jsdao.cc';
$api_key = 'B81BA11A6528EC298C7DD88C144B8D882568BC1D';

// 检查主要接口
$endpoints_to_check = [
    '/api/v1/order/create-transaction' => 'POST',
    '/pay/check-status/' => 'GET',
    '/api/v1/order/query' => 'POST',
    '/' => 'GET'
];

foreach ($endpoints_to_check as $endpoint => $method) {
    echo "检查接口: {$method} {$epusdt_base_url}{$endpoint}\n";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $epusdt_base_url . $endpoint);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
    
    if ($method === 'POST') {
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode(['test' => 'data']));
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
    }
    
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($response === false) {
        echo "❌ 连接失败: {$error}\n";
    } else {
        echo "✅ HTTP {$http_code}\n";
        if ($http_code >= 500) {
            echo "⚠️  服务器错误 - 这可能是502错误的原因\n";
        }
    }
    echo "\n";
}

// 2. 检查我们系统的回调接口
echo "2. 检查本系统回调接口\n";
echo "----------------------------\n";

$callback_url = 'https://jsdao.cc/api/callback/pay?gateway=Epusdt';
echo "检查回调接口: {$callback_url}\n";

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $callback_url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 10);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode([
    'order_id' => 'test123',
    'amount' => 100,
    'status' => 2,
    'signature' => 'test'
]));
curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);

$response = curl_exec($ch);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

if ($response === false) {
    echo "❌ 回调接口连接失败: {$error}\n";
} else {
    echo "✅ 回调接口 HTTP {$http_code}\n";
    echo "响应: " . substr($response, 0, 200) . "\n";
}

// 3. 检查数据库连接
echo "\n3. 检查数据库连接\n";
echo "----------------------------\n";

try {
    $pdo = new PDO('mysql:host=127.0.0.1;dbname=tcyp;charset=utf8', 'tcyp', 'eDGSBtezJGfHx7eP');
    echo "✅ 数据库连接正常\n";
    
    // 检查最近的充值记录
    $stmt = $pdo->query("SELECT * FROM recharge ORDER BY create_time DESC LIMIT 3");
    $orders = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "最近的充值记录:\n";
    foreach ($orders as $order) {
        $status_text = ['待支付', '处理中', '支付成功', '支付失败'][$order['status']] ?? '未知';
        echo "- 订单: {$order['order_no']}, 状态: {$status_text}, 时间: " . date('Y-m-d H:i:s', $order['create_time']) . "\n";
    }
    
} catch (Exception $e) {
    echo "❌ 数据库连接失败: " . $e->getMessage() . "\n";
}

// 4. 分析可能的原因
echo "\n4. 502错误可能的原因分析\n";
echo "----------------------------\n";
echo "根据错误信息 'https://pay.jsdao.cc/pay/check-status/' 返回502，可能的原因:\n\n";

echo "A. EPUSDT系统问题:\n";
echo "   - EPUSDT服务器负载过高\n";
echo "   - EPUSDT内部服务故障\n";
echo "   - 网络连接问题\n\n";

echo "B. 支付页面JavaScript问题:\n";
echo "   - 支付页面的状态检查脚本出错\n";
echo "   - 轮询频率过高导致服务器拒绝请求\n";
echo "   - 跨域请求被阻止\n\n";

echo "C. 配置问题:\n";
echo "   - API密钥配置错误\n";
echo "   - 回调URL配置错误\n";
echo "   - 防火墙或安全策略阻止\n\n";

// 5. 建议的解决方案
echo "5. 建议的解决方案\n";
echo "----------------------------\n";
echo "1. 联系EPUSDT技术支持，报告502错误\n";
echo "2. 检查EPUSDT系统状态页面或公告\n";
echo "3. 暂时使用我们系统的订单状态查询接口作为备用\n";
echo "4. 优化前端轮询逻辑，减少请求频率\n";
echo "5. 添加错误重试机制\n";
echo "6. 考虑使用WebSocket或Server-Sent Events替代轮询\n\n";

// 6. 测试我们的状态查询接口
echo "6. 测试本系统状态查询接口\n";
echo "----------------------------\n";

$status_check_url = 'https://jsdao.cc/api/recharge/getOrderStatus';
echo "本系统状态查询接口: {$status_check_url}\n";
echo "用户可以使用此接口查询订单状态，避免依赖EPUSDT的状态检查接口\n\n";

echo "=== 诊断完成 ===\n";
echo "如果EPUSDT的check-status接口持续502错误，建议:\n";
echo "1. 引导用户使用我们系统的状态查询功能\n";
echo "2. 在支付页面添加提示，说明可能的延迟\n";
echo "3. 实现自动刷新或手动刷新按钮\n";
echo "4. 联系EPUSDT技术支持解决根本问题\n";
?>
