<?php
/**
 * EPUSDT createPay 方法测试脚本
 */

// 模拟测试数据
$test_data = [
    'sn' => 'TEST_ORDER_' . time(),
    'amount' => 100.50
];

echo "=== EPUSDT createPay 测试数据 ===\n\n";

echo "1. 输入参数 (\$op_data):\n";
echo "订单号: " . $test_data['sn'] . "\n";
echo "金额: " . $test_data['amount'] . " USDT\n\n";

// 模拟配置数据
$config = [
    'api_url' => 'https://pay.jsdao.cc',
    'api_key' => 'B81BA11A6528EC298C7DD88C144B8D882568BC1D',
    'wallet_addresses' => [
        'TRE7XXnKPJF1XfEjAhLfgbQ1pgB6WzU5Lx',
        'TQn9Y2khEsLJW1ChVbFMSMeRDow5KcbLSE',
        'TLBaRhANQoJFTqre9Nf1mjuwNWjCJeYqUL'
    ]
];

// 随机选择收款地址
$selectedAddress = $config['wallet_addresses'][array_rand($config['wallet_addresses'])];

echo "2. 系统配置:\n";
echo "API URL: " . $config['api_url'] . "\n";
echo "API Key: " . $config['api_key'] . "\n";
echo "选中的收款地址: " . $selectedAddress . "\n\n";

// 构建请求数据
$data = [
    'order_id' => $test_data['sn'],
    'amount' => $test_data['amount'],
    'notify_url' => 'https://jsdao.cc/api/callback/pay?gateway=Epusdt',
    'redirect_url' => 'https://jsdao.cc/api/my/index'
];

echo "3. 发送到EPUSDT的数据 (签名前):\n";
foreach ($data as $key => $value) {
    echo "$key: $value\n";
}

// 生成签名 - 根据EPUSDT官方文档
function epusdtSign(array $parameter, string $signKey) {
    ksort($parameter);
    reset($parameter);
    $sign = '';

    foreach ($parameter as $key => $val) {
        if ($val == '') continue;
        if ($key != 'signature') {
            if ($sign != '') {
                $sign .= "&";
            }
            $sign .= "$key=$val";
        }
    }

    $sign = md5($sign . $signKey);
    return strtolower($sign);
}

$signature = epusdtSign($data, $config['api_key']);
$data['signature'] = $signature;

echo "\n4. 签名计算:\n";
echo "签名字符串: " . $signString . "\n";
echo "MD5签名: " . $signature . "\n\n";

echo "5. 完整的请求数据 (包含签名):\n";
foreach ($data as $key => $value) {
    echo "$key: $value\n";
}

echo "\n6. JSON格式的请求数据:\n";
echo json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES) . "\n\n";

echo "7. cURL请求示例:\n";
echo "curl -X POST \\\n";
echo "  " . $config['api_url'] . "/api/v1/order/create-transaction \\\n";
echo "  -H 'Content-Type: application/json' \\\n";
echo "  -H 'Accept: application/json' \\\n";
echo "  -d '" . json_encode($data, JSON_UNESCAPED_SLASHES) . "'\n\n";

echo "8. 预期的成功响应:\n";
$expected_response = [
    'status_code' => 200,
    'message' => 'success',
    'data' => [
        'payment_url' => 'https://pay.jsdao.cc/pay/' . $test_data['sn'],
        'order_id' => $test_data['sn'],
        'amount' => $test_data['amount'],
        'token' => $selectedAddress
    ]
];
echo json_encode($expected_response, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES) . "\n\n";

echo "9. 测试步骤:\n";
echo "1. 确保EPUSDT系统配置正确\n";
echo "2. 确保收款地址已在EPUSDT后台添加\n";
echo "3. 使用上述数据调用createPay方法\n";
echo "4. 检查返回的respCode是否为'SUCCESS'\n";
echo "5. 检查payInfo是否包含有效的支付URL\n";

?>
