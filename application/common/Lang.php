<?php
namespace app\common;

use think\facade\Request;

/**
 * 多语言处理类
 */
class Lang
{
    // 支持的语言列表
    private static $supportedLangs = [
        'ko-kr' => 'ko_hy',  // 韩语
        'zh-cn' => 'zh_cn',  // 中文
        'vi-vn' => 'vi_vn',  // 越南语
        'en-us' => 'en_us',  // 英语
        'es-es' => 'es_es',  // 西班牙语
    ];

    // 默认语言
    private static $defaultLang = 'es-es';  // 可以改为: ko-kr, zh-cn, vi-vn, es-es

    // 语言包缓存
    private static $langCache = [];

    // 配置缓存
    private static $config = null;
    
    /**
     * 获取当前语言
     * @return string
     */
    public static function getCurrentLang()
    {
        $lang = '';

        // 检查是否在ThinkPHP环境中
        if (class_exists('think\facade\Request')) {
            // 从请求头获取语言
            $lang = Request::header('lang');

            // 如果没有，从POST参数获取
            if (empty($lang)) {
                $lang = Request::post('lang');
            }

            // 如果没有，从GET参数获取
            if (empty($lang)) {
                $lang = Request::get('lang');
            }
        } else {
            // 在独立脚本中，从全局变量获取
            if (isset($_SERVER['HTTP_LANG'])) {
                $lang = $_SERVER['HTTP_LANG'];
            } elseif (isset($_POST['lang'])) {
                $lang = $_POST['lang'];
            } elseif (isset($_GET['lang'])) {
                $lang = $_GET['lang'];
            }
        }

        // 语言映射转换
        $langMap = [
            'ko_hy' => 'ko-kr',
            'zh_cn' => 'zh-cn',
            'vi_vn' => 'vi-vn',
            'en_us' => 'en-us',
            'es_es' => 'es-es',  // 添加西班牙语映射
        ];

        if (isset($langMap[$lang])) {
            $lang = $langMap[$lang];
        }

        // 验证语言是否支持
        if (!isset(self::$supportedLangs[$lang])) {
            $lang = self::$defaultLang;
        }

        return $lang;
    }
    
    /**
     * 加载语言包
     * @param string $lang 语言代码
     * @return array
     */
    private static function loadLangPack($lang)
    {
        if (isset(self::$langCache[$lang])) {
            return self::$langCache[$lang];
        }
        
        $langFile = __DIR__ . '/../lang/' . $lang . '.php';

        if (file_exists($langFile)) {
            self::$langCache[$lang] = include $langFile;
        } else {
            // 如果语言包不存在，加载默认语言包
            $defaultFile = __DIR__ . '/../lang/' . self::$defaultLang . '.php';
            if (file_exists($defaultFile)) {
                self::$langCache[$lang] = include $defaultFile;
            } else {
                self::$langCache[$lang] = [];
            }
        }
        
        return self::$langCache[$lang];
    }
    
    /**
     * 获取翻译文本
     * @param string $key 语言键
     * @param array $params 参数替换
     * @param string $lang 指定语言，为空则使用默认语言
     * @return string
     */
    public static function get($key, $params = [], $lang = null)
    {
        if (is_null($lang)) {
            // 检测当前语言
            $lang = self::getCurrentLang();
        }

        $langPack = self::loadLangPack($lang);

        $text = isset($langPack[$key]) ? $langPack[$key] : $key;

        // 参数替换
        if (!empty($params)) {
            foreach ($params as $k => $v) {
                $text = str_replace('{' . $k . '}', $v, $text);
            }
        }

        return $text;
    }
    
    /**
     * 获取所有支持的语言
     * @return array
     */
    public static function getSupportedLangs()
    {
        return self::$supportedLangs;
    }
    
    /**
     * 检查语言是否支持
     * @param string $lang
     * @return bool
     */
    public static function isSupported($lang)
    {
        return isset(self::$supportedLangs[$lang]);
    }
    
    /**
     * 获取数据库tips表对应的语言字段
     * @param string $lang
     * @return string
     */
    public static function getTipsLangField($lang = null)
    {
        if (is_null($lang)) {
            $lang = self::getCurrentLang();
        }

        return isset(self::$supportedLangs[$lang]) ? self::$supportedLangs[$lang] : self::$supportedLangs[self::$defaultLang];
    }

    /**
     * 设置默认语言
     * @param string $lang 语言代码
     * @return bool
     */
    public static function setDefaultLang($lang)
    {
        if (self::isSupported($lang)) {
            self::$defaultLang = $lang;
            return true;
        }
        return false;
    }

    /**
     * 获取当前默认语言
     * @return string
     */
    public static function getDefaultLang()
    {
        return self::$defaultLang;
    }

    /**
     * 加载语言配置
     * @return array
     */
    private static function loadConfig()
    {
        if (self::$config === null) {
            $configFile = __DIR__ . '/../../config/lang.php';
            if (file_exists($configFile)) {
                self::$config = include $configFile;
                // 从配置文件更新默认语言
                if (isset(self::$config['default_lang'])) {
                    self::$defaultLang = self::$config['default_lang'];
                }
            } else {
                self::$config = [];
            }
        }
        return self::$config;
    }

    /**
     * 获取配置项
     * @param string $key
     * @param mixed $default
     * @return mixed
     */
    public static function getConfig($key = null, $default = null)
    {
        $config = self::loadConfig();

        if ($key === null) {
            return $config;
        }

        return isset($config[$key]) ? $config[$key] : $default;
    }
}
