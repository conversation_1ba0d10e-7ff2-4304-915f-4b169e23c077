<?php

namespace app\api\pay;

use think\Db;
use think\facade\Request;

class Epusdt extends PayBase
{
    private $api_url;
    private $api_key;

    public function __construct()
    {
        // 从系统配置中获取EPUSDT配置
        $systemModel = new \app\admin\model\SystemModel();
        $config = $systemModel->getConfig('epusdt');
        
        $this->api_url = isset($config['api_url']) ? rtrim($config['api_url'], '/') : 'https://pay.jsdao.cc';
        $this->api_key = isset($config['api_key']) ? $config['api_key'] : '';
    }

    /**
     * 创建支付订单 - 完全按照eppsost.php demo一模一样
     */
    public function createPay(array $op_data): array
    {
        if (empty($this->api_key)) {
            return ['respCode' => 'FAIL', 'message' => 'EPUSDT配置未设置'];
        }

        // 完全按照demo的变量定义和注释
        $amount = (double)$op_data['amount'];
        $notify_url = 'https://jsdao.cc/api/callback/pay?gateway=Epusdt';//Epusdt的异步回调地址（硬编码确保正确）
        $redirect_url = 'https://vip.jsdao.cc/#/Mine';//Epusdt的同步跳转地址,付款成功后跳转到这里
        $order_id=(string)$this->token(10);//生成随机数用于订单号
        $key=$this->api_key;//Epusdt的自定义密钥

        // 完全按照demo的拼接方式和注释
        $str = 'amount='.$amount.'&notify_url='.$notify_url.'&order_id='.$order_id.'&redirect_url='.$redirect_url.$key;//拼接字符串用于MD5计算
        $signature = md5($str);//用MD5算法计算签名

        // 完全按照demo的数据包生成方式和注释
        $data=json_encode(array( 'order_id' => $order_id,//生成数据包，用到了的数组转json的jsonencode
        'amount' => $amount,
        'notify_url' => $notify_url,
        'redirect_url' => $redirect_url,
        'signature' => $signature));

        // 完全按照demo的请求方式和注释
        $res=$this->curl_request($this->api_url.'/api/v1/order/create-transaction',$data,'post');//发起Curl请求并获取返回数据到变量

        // 完全按照demo的响应处理和注释
        $arr = json_decode($res, true);//对返回数据进行json到数组的转换，用到了jsondecode

        if (!$arr || !isset($arr['data'])) {
            return ['respCode' => 'FAIL', 'message' => '响应数据格式错误'];
        }

        $resdata=$arr['data'];//提取返回数据的数组中的data段落
        $payurl= $resdata['payment_url'];//提取返回数据的数组中的data段落中的支付链接
        $payamount=$resdata['actual_amount'];//提取返回数据的数组中的data段落中的转换后数值

        // 检查是否获取到支付链接
        if (empty($payurl)) {
            return ['respCode' => 'FAIL', 'message' => '获取支付链接失败'];
        }

        // 保存订单号映射到数据库（因为我们生成了新的order_id）
        $this->saveOrderMapping($op_data['sn'], $order_id);

        return [
            'respCode' => 'SUCCESS',
            'payInfo' => $payurl
        ];
    }

    /**
     * 解析支付回调
     */
    public function parsePayCallback($type = ''): array
    {
        $input = file_get_contents('php://input');
        $data = json_decode($input, true);

        if (!$data) {
            return ['status' => 'FAIL', 'message' => '无效的回调数据'];
        }

        // 验证签名
        if (!$this->verifySignature($data)) {
            return ['status' => 'FAIL', 'message' => '签名验证失败'];
        }

        // 检查订单状态
        if (isset($data['status']) && $data['status'] == 2) { // 2表示支付成功
            // 查找原始订单号（因为EPUSDT返回的是我们生成的order_id）
            $originalOrderId = $this->findOriginalOrderId($data['order_id']);

            return [
                'status' => 'SUCCESS',
                'oid' => $originalOrderId ?: $data['order_id'], // 使用原始订单号
                'amount' => $data['amount'],
                'data' => $data // 添加原始数据
            ];
        }

        return ['status' => 'FAIL', 'message' => '支付未成功'];
    }

    /**
     * 支付成功回调
     */
    public function payCallbackSuccess()
    {
        echo 'ok';
    }

    /**
     * 支付失败回调
     */
    public function payCallbackFail()
    {
        echo 'fail';
    }

    /**
     * 创建代付订单（暂不实现）
     */
    public function create_payout(array $oinfo, array $blank_info): bool
    {
        return false;
    }

    /**
     * 解析代付回调（暂不实现）
     */
    public function parsePayoutCallback($type = ''): array
    {
        return ['status' => 'FAIL', 'oid' => '', 'amount' => '', 'data' => [], 'msg' => 'EPUSDT代付功能暂未实现'];
    }

    /**
     * 代付成功回调（暂不实现）
     */
    public function parsePayoutCallbackSuccess()
    {
        echo 'ok';
    }

    /**
     * 代付失败回调（暂不实现）
     */
    public function parsePayoutCallbackFail()
    {
        echo 'fail';
    }

    /**
     * 验证签名 - 根据实际回调数据格式
     */
    private function verifySignature($data)
    {
        if (!isset($data['signature'])) {
            return false;
        }

        $signature = $data['signature'];

        // 构建参数数组，排除signature字段和空值
        $params = [];
        foreach ($data as $key => $value) {
            if ($key !== 'signature' && $value !== '') {
                $params[$key] = $value;
            }
        }

        // 按字母顺序排序参数
        ksort($params);

        // 拼接签名字符串
        $signString = '';
        foreach ($params as $key => $value) {
            if ($signString !== '') {
                $signString .= '&';
            }
            $signString .= $key . '=' . $value;
        }

        // 添加密钥
        $signString .= $this->api_key;

        $calculatedSignature = md5($signString);

        // 记录签名验证信息用于调试
        error_log("EPUSDT Signature Verification:");
        error_log("Sign String: " . $signString);
        error_log("Calculated: " . $calculatedSignature);
        error_log("Received: " . $signature);

        return $signature === $calculatedSignature;
    }

    /**
     * 完全按照demo的curl_request函数
     */
    private function curl_request($url, $data=null, $method='psot', $header = array("content-type: application/json"), $https=true, $timeout = 5){
        $method = strtoupper($method);
        $ch = curl_init();//初始化
        curl_setopt($ch, CURLOPT_URL, $url);//访问的URL
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);//只获取页面内容，但不输出
        if($https){
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);//https请求 不验证证书
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);//https请求 不验证HOST
        }
        if ($method != "GET") {
            if($method == 'POST'){
                curl_setopt($ch, CURLOPT_POST, true);//请求方式为post请求
            }
            if ($method == 'PUT' || strtoupper($method) == 'DELETE') {
                curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $method); //设置请求方式
            }
            curl_setopt($ch, CURLOPT_POSTFIELDS, $data);//请求数据
        }
        curl_setopt($ch, CURLOPT_TIMEOUT, $timeout);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $header); //模拟的header头
        //curl_setopt($ch, CURLOPT_HEADER, false);//设置不需要头信息
        $result = curl_exec($ch);//执行请求
        curl_close($ch);//关闭curl，释放资源
        return $result;
    }

    /**
     * 完全按照demo的token随机数生成函数
     */
    private function token($length){
     $str = md5(time());
     $token = substr($str,15,$length);
     return $token;
    }//随机数生成函数

    /**
     * 保存订单号映射到数据库
     */
    private function saveOrderMapping($original_order_id, $epusdt_order_id)
    {
        try {
            // 保存到数据库中，用于回调时查找原始订单号
            Db::name('recharge')
                ->where('order_no', $original_order_id)
                ->update([
                    'epusdt_order_id' => $epusdt_order_id,
                    'update_time' => time()
                ]);

            error_log("EPUSDT Order Mapping Saved: {$original_order_id} -> {$epusdt_order_id}");
        } catch (Exception $e) {
            error_log("EPUSDT Order Mapping Failed: " . $e->getMessage());
        }
    }

    /**
     * 根据EPUSDT订单号查找原始订单号
     */
    private function findOriginalOrderId($epusdt_order_id)
    {
        try {
            $order = Db::name('recharge')
                ->where('epusdt_order_id', $epusdt_order_id)
                ->field('order_no')
                ->find();

            return $order ? $order['order_no'] : null;
        } catch (Exception $e) {
            error_log("EPUSDT Find Original Order Failed: " . $e->getMessage());
            return null;
        }
    }
}
