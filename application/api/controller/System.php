<?php
namespace app\api\controller;

use think\Controller;
use think\Db;
use app\api\controller\Base;
use think\facade\Request;
use app\common\Lang;
class System extends Base
{
    public function base(){
        $data = $this->SystemModel->getConfig("base");
        $data['ico']=Request::domain().$data['ico'];
        return json_exit_lang(200,'info_get_success',$data);
    }

    /**
     * 获取注册配置信息
     */
    public function getRegisterConfig(){
        $data = $this->SystemModel->getConfig("base");
        $config = [
            'register_bonus' => isset($data['register_bonus']) ? floatval($data['register_bonus']) : 0,
        ];
        return json_exit_lang(200,'info_get_success',$config);
    }
    public function getBankList(){
        $json = file_get_contents('../application/admin/data/bank.json');
        $data = Db::name('bank_list')->where('status',1)->select();

        // 检查是否已存在 Mercado Pago，如果不存在则添加
        $hasMercadoPago = false;
        foreach ($data as $bank) {
            $bankName = isset($bank['text']) ? $bank['text'] : (isset($bank['name']) ? $bank['name'] : '');
            if ($bankName === 'Mercado Pago') {
                $hasMercadoPago = true;
                break;
            }
        }

        // 如果数据库中没有 Mercado Pago，动态添加到返回结果中
        if (!$hasMercadoPago) {
            $mercadoPago = [
                'id' => 999, // 使用一个不冲突的ID
                'value' => 'MERCADO_PAGO',
                'text' => 'Mercado Pago',
                'name' => 'Mercado Pago', // 兼容不同的字段名
                'status' => 1
            ];
            $data[] = $mercadoPago;
        }

        // $data = json_decode($json, true);
        json_exit_lang(200,'info_get_success',$data);
    }
    public function config()
    {

        $lang = Request::header('lang');
        // $text = 'text_'.$lang;
        $text = 'text_ko_hy';
        $banners=$this->BannerModel->where(['status'=>1])->select();
        foreach ($banners as $k=>$v){
            $v['url']=Request::domain().$v['url'];
        }
        $data=[
            'notice'=>$this->NoticeModel->where(['hot'=>1,'status'=>1])->value($text),
            'banners'=>$banners,

            'movielist_0'=> $this->VideoModel->getHotList(),
            'movielist_1'=>$this->VideoModel->getRandHotList(8)

         ];
        return json_exit_lang(200,'info_get_success',$data);

    }

    /**
     * 获取公司简历信息
     */
    public function getCompanyInfo(){
        $data = $this->SystemModel->getConfig("company");

        // 如果没有配置，返回空数据
        if (empty($data)) {
            $data = [
                'company_name' => '',
                'company_description' => '',
                'company_history' => '',
                'company_vision' => '',
                'company_mission' => '',
                'company_values' => '',
                'company_address' => '',
                'company_phone' => '',
                'company_email' => '',
                'company_website' => '',
                'company_logo' => '',
                'company_founded' => '',
                'company_employees' => '',
                'company_industry' => '',
            ];
        }

        // 处理LOGO URL，添加域名前缀
        if (!empty($data['company_logo'])) {
            $data['company_logo'] = Request::domain() . $data['company_logo'];
        }

        return json_exit_lang(200,'info_get_success',$data);
    }

    /**
     * 获取首页弹窗信息
     */
    public function getPopupInfo(){
        $data = $this->SystemModel->getConfig("base");

        // 构建文字公告弹窗信息
        $popupInfo = [
            'enabled' => isset($data['popup_enabled']) ? intval($data['popup_enabled']) : 0,
            'title' => isset($data['popup_title']) ? $data['popup_title'] : '',
            'content' => isset($data['popup_content']) ? $data['popup_content'] : '',
            'button_text' => isset($data['popup_button_text']) ? $data['popup_button_text'] : '确定',
        ];

        // 如果弹窗未启用或没有内容，返回空数据
        if (!$popupInfo['enabled'] || empty($popupInfo['content'])) {
            $popupInfo = [
                'enabled' => 0,
                'title' => '',
                'content' => '',
                'button_text' => '',
            ];
        }

        return json_exit_lang(200,'info_get_success',$popupInfo);
    }

    /**
     * 获取APP下载链接
     */
    public function getAppDownloadUrl(){
        $data = $this->SystemModel->getConfig("base");

        $appDownloadUrl = isset($data['app_download_url']) ? $data['app_download_url'] : '';

        return json_exit_lang(200,'info_get_success',['app_download_url' => $appDownloadUrl]);
    }

}
