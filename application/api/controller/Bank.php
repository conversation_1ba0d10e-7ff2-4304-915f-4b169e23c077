<?php
namespace app\api\controller;
use think\Controller;
use app\api\controller\Base;
use app\api\model\BanksModel;
use app\common\Lang;
class Bank extends Base
{
    public function index(){
        $model = new BanksModel();
        $list = $model->where([])->select();
        foreach ($list as $k=>&$v){
            $v['thumb'] = \think\facade\Request::domain().$v['thumb'];
        }

        json_exit_lang(200,'info_get_success',$list);
    }
}
