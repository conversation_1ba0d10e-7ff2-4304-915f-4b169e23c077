<?php

namespace app\api\controller;

use think\Controller;
use think\facade\Request;
use think\Db;

/**
 * 验证登录控制器
 */
class Callback extends Controller
{
        //统一代收回掉  通道 ， 渠道
    public function pay($gateway = '', $type = '')
    {
        if ($gateway == '') exit();
        $gateway = ucfirst($gateway);
        $log_file = APP_PATH . 'callback_pay_' . $gateway . '.log';
        $log_file_final = APP_PATH . 'callback_pay_' . $gateway . '_final.log';

        // 记录详细的请求信息
        $input_data = file_get_contents('php://input');
        $request_info = [
            'timestamp' => date('Y-m-d H:i:s'),
            'gateway' => $gateway,
            'method' => $_SERVER['REQUEST_METHOD'] ?? 'UNKNOWN',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'UNKNOWN',
            'ip' => $_SERVER['REMOTE_ADDR'] ?? 'UNKNOWN',
            'input_data' => $input_data,
            'get_params' => $_GET,
            'post_params' => $_POST
        ];

        file_put_contents($log_file, json_encode($request_info, JSON_UNESCAPED_UNICODE) . "\n", FILE_APPEND);

        $className = "\\app\\api\\pay\\" . $gateway;
        $payObj = new $className();
        $payout = $payObj->parsePayCallback($type);
        file_put_contents($log_file, '  ret:' . json_encode($payout, JSON_UNESCAPED_UNICODE) . "\n", FILE_APPEND);
        //处理回调逻辑
        $res = $this->checkCallbackOrder($payout, $log_file, $log_file_final);
        if ($res) {
            $payObj->payCallbackSuccess();
        } else {
            $payObj->payCallbackFail();
        }
        exit;
    }
    
        //收款成功 回掉公共逻辑
    //$data = ['status'=>'SUCCESS',oid=>'订单号',amount=>'金额','data'=>'原始数据 array']
    // , $log_file="xxxx.log"
    // ,$log_file_final='xxx.log'
    /**
     * 收款成功 回掉公共逻辑
     * @param $data array
     * @param $log_file string
     * @param $log_file_final string
     * @return bool
     * */
    private function checkCallbackOrder($data, $log_file, $log_file_final)
    {
        file_put_contents($log_file, 'DATA ======' . json_encode($data, JSON_UNESCAPED_UNICODE) . "\n", FILE_APPEND);
        if (!isset($data['status']) || !isset($data['oid']) ||
            !isset($data['amount']) || !isset($data['data'])) {
            //数据包格式不对
            file_put_contents($log_file, 'ERROR ' . json_encode($data, JSON_UNESCAPED_UNICODE) . "\n", FILE_APPEND);
            return false;
        }
     
        // 使用数据库锁防止并发处理同一订单
        $oinfo = Db::name('recharge')->where('order_no',$data['oid'])->lock(true)->find();
        if (!$oinfo) {
            file_put_contents($log_file, $data['oid'] . ' ======订单不存在!' . "\n", FILE_APPEND);
            return false;
        }
        if ($oinfo['status'] == 2) {
            file_put_contents($log_file, $data['oid'] . ' ======订单已处理成功!' . "\n", FILE_APPEND);
            return true;
        }
        //if ($oinfo['status'] != 1) {
        //    file_put_contents($log_file, $data['oid'] . ' ======订单已处理!' . "\n", FILE_APPEND);
        //    return false;
        //}

        if ($data['status'] != 'SUCCESS') {
            file_put_contents($log_file, $data['oid'] . ' ======ERROR' . "\n", FILE_APPEND);
            //更新标状态
            Db::name('recharge')
                ->where('order_no', $oinfo['order_no'])
                ->update([
                    'pay_time' => time(),
                    'status' => 3
                ]);
            return false;
        }
        // if (floatval($data['amount']) != floatval($oinfo['num'])) {
            //file_put_contents($log_file, $oinfo['id'] . ' ======金额不对!' . "\n", FILE_APPEND);
            //return false;
            //修改订单金额
            //$pay_com = Db::name('xy_pay')->where('name2', $oinfo['pay_name'])->value('pay_commission');
            //$pay_com = $pay_com ? floatval($pay_com) : 0;

            // Db::name('xy_recharge')
            //     ->where('id', $oinfo['id'])
            //     ->update(['num' => $data['amount']]);
        // }
    
        $user = Db::name('member')->where('id', $oinfo['mid'])->find();
        if (!$user) {
            file_put_contents($log_file, $data['oid'] . ' ======用户已被删除!' . "\n", FILE_APPEND);
            return false;
        }

        // 使用数据库事务确保数据一致性
        Db::startTrans();
        try {
            // 更新订单状态
            $updateResult = Db::name('recharge')
                ->where('order_no', $data['oid'])
                ->where('status', '<>', 2) // 只更新未成功的订单
                ->update([
                    'pay_time' => time(),
                    'status' => 2,
                    'update_time' => time()
                ]);

            if ($updateResult) {
                // 增加用户余额
                $res = Db::name('member')->where('id', $oinfo['mid'])->setInc('money', $oinfo['money']);
                if ($res) {
                    Db::commit();
                    file_put_contents($log_file, $data['oid'] . ' ======SUCCESS!' . "\n", FILE_APPEND);
                    return true;
                } else {
                    Db::rollback();
                    file_put_contents($log_file, $data['oid'] . ' ======用户余额更新失败!' . "\n", FILE_APPEND);
                    return false;
                }
            } else {
                Db::rollback();
                file_put_contents($log_file, $data['oid'] . ' ======订单状态更新失败或订单已处理!' . "\n", FILE_APPEND);
                return true; // 订单可能已经处理过了
            }
        } catch (Exception $e) {
            Db::rollback();
            file_put_contents($log_file, $data['oid'] . ' ======数据库操作异常: ' . $e->getMessage() . "\n", FILE_APPEND);
            file_put_contents($log_file_final, date('Y-m-d H:i:s') . ': ' . json_encode($data) . "\n", FILE_APPEND);
            return false;
        }
    }
    
        //统一代付回掉  通道，渠道
    public function payout($gateway = '', $type = '')
    {
        if ($gateway == '') exit();
        $gateway = ucfirst($gateway);
        $log_file = APP_PATH . 'callback_payout_' . $gateway . '.log';
        $log_file_final = APP_PATH . 'callback_payout_' . $gateway . '_final.log';
        file_put_contents($log_file, date('Y-m-d H:i:s') . ': ' . file_get_contents('php://input') . "\n", FILE_APPEND);
        $className = "\\app\\api\\pay\\" . $gateway;
        $payObj = new $className();
        $result = $payObj->parsePayoutCallback();
        $res = $this->checkPayoutOrder($result, $log_file);
        if ($res) {
            $payObj->parsePayoutCallbackSuccess();
        } else {
            $payObj->parsePayoutCallbackFail();
        }
        exit;
    }
    
        //出款回掉公共逻辑==错误的情况
    //$data['status'=>'SUCCESS','oid'=>'','amount'=>'','msg'=>''] ,$log_file='xxx.log'
    private function checkPayoutOrder($data, $log_file)
    {
        file_put_contents($log_file, 'DATA ======' . json_encode($data, JSON_UNESCAPED_UNICODE) . "\n", FILE_APPEND);
        if (!isset($data['status']) || !isset($data['oid']) ||
            !isset($data['amount']) || !isset($data['data']) || !isset($data['msg'])) {
            //数据包格式不对
            file_put_contents($log_file, '======DATA ERROR' . "\n", FILE_APPEND);
            return false;
        }
        //失败了  处理订单逻辑
        $oinfo = Db::name('withdraw')->where('order_no',$data['oid'])->find();
        if (!$oinfo) {
            file_put_contents($log_file, $data['oid'] . ' ======提现订单不存在!' . "\n", FILE_APPEND);
            return false;
        }
        //如果订单状态不对的
        /*if ($oinfo['status'] != 1) {
            file_put_contents($log_file, $data['oid'] . ' ======订单已处理!' . "\n", FILE_APPEND);
            return true;
        }*/
        //更新数据库
        Db::name('withdraw')
            ->where('order_no', $data['oid'])
            ->update([
                'pay_time' => time(),
                'status' => ($data['status'] == 'SUCCESS') ? 4 : 5,
                'payout_err_msg' => $data['msg']
            ]);
        if ($data['status'] == 'SUCCESS') {
            return true;
        }
        if ($oinfo['status'] != 2) {
            file_put_contents($log_file, $data['oid'] . ' ======订单状态不对!' . "\n", FILE_APPEND);
            return true;
        }
        file_put_contents($log_file, $data['oid'] . ' ======开始回滚提现!' . "\n", FILE_APPEND);
        $res = Db::name('member')->where('id', $oinfo['mid'])->setInc('money', $oinfo['money']);;
        // $res = model('admin/Users')->payout_rollback($oinfo);
        if ($res) {
            file_put_contents($log_file, $data['oid'] . ' ===ROLLBACK===SUCCESS!' . "\n", FILE_APPEND);
            return true;
        }
        file_put_contents($log_file, $data['oid'] . ' ======数据库操作失败!' . "\n", FILE_APPEND);
        return false;
    }
}