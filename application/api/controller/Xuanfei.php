<?php
namespace app\api\controller;
use think\Request;
use think\Controller;
use app\api\controller\Base;
class <PERSON>an<PERSON>i extends Base
{
    public function address(){
        $this->XuanfeiaddressModel->getaddressList();
    }
    public function xuanfeilist()
    {
        $get = $this->request->param();
        $this->XuanfeiModel->getList($get['id']);
    }

    public function malelist()
    {
        $get = $this->request->param();
        $this->XuanfeiModel->getMaleList($get['id']);
    }
    public function xuanfeidata()
    {
        $get = $this->request->param();
        $this->XuanfeiModel->xuanfeidata($get);
    }

    public function maleImages()
    {
        $get = $this->request->param();
        $this->XuanfeiModel->getMaleImages($get);
    }

    public function femaleImages()
    {
        $get = $this->request->param();
        $this->XuanfeiModel->getFemaleImages($get);
    }
}
