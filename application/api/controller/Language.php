<?php
namespace app\api\controller;

use think\Controller;
use think\facade\Request;
use app\common\Lang;

/**
 * 语言管理控制器
 * Language Management Controller
 */
class Language extends Controller
{
    /**
     * 获取支持的语言列表
     */
    public function getSupportedLanguages()
    {
        $languages = Lang::getSupportedLangs();
        $config = Lang::getConfig('supported_langs', []);
        
        $result = [];
        foreach ($languages as $code => $field) {
            $langInfo = isset($config[$code]) ? $config[$code] : [];
            $result[] = [
                'code' => $code,
                'name' => isset($langInfo['name']) ? $langInfo['name'] : $code,
                'name_en' => isset($langInfo['name_en']) ? $langInfo['name_en'] : $code,
                'enabled' => isset($langInfo['enabled']) ? $langInfo['enabled'] : true,
                'is_default' => $code === Lang::getDefaultLang()
            ];
        }
        
        json_exit_lang(200, 'info_get_success', $result);
    }
    
    /**
     * 获取当前默认语言
     */
    public function getDefaultLanguage()
    {
        $defaultLang = Lang::getDefaultLang();
        $config = Lang::getConfig('supported_langs', []);
        $langInfo = isset($config[$defaultLang]) ? $config[$defaultLang] : [];
        
        $result = [
            'code' => $defaultLang,
            'name' => isset($langInfo['name']) ? $langInfo['name'] : $defaultLang,
            'name_en' => isset($langInfo['name_en']) ? $langInfo['name_en'] : $defaultLang
        ];
        
        json_exit_lang(200, 'info_get_success', $result);
    }
    
    /**
     * 设置默认语言
     */
    public function setDefaultLanguage()
    {
        $lang = Request::post('lang');
        
        if (empty($lang)) {
            json_exit_lang(400, 'lang_code_required');
        }
        
        if (!Lang::isSupported($lang)) {
            json_exit_lang(400, 'lang_not_supported');
        }
        
        // 动态设置默认语言
        if (Lang::setDefaultLang($lang)) {
            // 更新配置文件
            $this->updateConfigFile($lang);
            json_exit_lang(200, 'default_lang_updated');
        } else {
            json_exit_lang(500, 'default_lang_update_failed');
        }
    }
    
    /**
     * 更新配置文件
     */
    private function updateConfigFile($lang)
    {
        $configFile = __DIR__ . '/../../../config/lang.php';
        if (file_exists($configFile)) {
            $content = file_get_contents($configFile);
            $pattern = "/'default_lang'\s*=>\s*'[^']*'/";
            $replacement = "'default_lang' => '$lang'";
            $newContent = preg_replace($pattern, $replacement, $content);
            file_put_contents($configFile, $newContent);
        }
    }
    
    /**
     * 测试多语言消息
     */
    public function testMessages()
    {
        $testKeys = [
            'login_success',
            'register_success',
            'withdraw_success',
            'auth_error',
            'network_error'
        ];
        
        $languages = Lang::getSupportedLangs();
        $result = [];
        
        foreach ($testKeys as $key) {
            $messages = [];
            foreach ($languages as $code => $field) {
                $messages[$code] = Lang::get($key, [], $code);
            }
            $result[$key] = $messages;
        }
        
        json_exit_lang(200, 'info_get_success', $result);
    }
}
?>
