<?php
namespace app\api\controller;
use think\Controller;
use app\api\controller\Base;
use app\api\model\RechargeModel;
use app\api\model\MemberModel;
use app\common\Lang;
use think\facade\Request;

class Recharge extends Base
{
    /**
     * 获取充值配置信息
     */
    public function getRechargeConfig()
    {
        // 获取EPUSDT配置
        $epusdtConfig = $this->SystemModel->getConfig('epusdt');
        
        if (empty($epusdtConfig) || !isset($epusdtConfig['enabled']) || $epusdtConfig['enabled'] != 1) {
            json_exit_lang(401, 'recharge_not_available');
        }
        
        $config = [
            'min_amount' => isset($epusdtConfig['min_amount']) ? floatval($epusdtConfig['min_amount']) : 10,
            'max_amount' => isset($epusdtConfig['max_amount']) ? floatval($epusdtConfig['max_amount']) : 10000,
            'payment_methods' => [
                [
                    'code' => 'epusdt',
                    'name' => 'USDT支付',
                    'icon' => '/static/images/payment/usdt.png',
                    'enabled' => true
                ]
            ]
        ];
        
        json_exit_lang(200, 'info_get_success', $config);
    }
    
    /**
     * 创建充值订单
     */
    public function createOrder()
    {
        $post = $this->request->param();
        
        // 验证用户登录
        $id = base64_decode(Request::header('token'));
        if (!$id) {
            json_exit_lang(401, 'auth_error');
        }
        
        $memberModel = new MemberModel();
        $userInfo = $memberModel->where(['id' => $id])->find();
        if (!$userInfo) {
            json_exit_lang(401, 'user_not_found');
        }
        
        // 验证充值金额
        if (!isset($post['amount']) || !is_numeric($post['amount'])) {
            json_exit_lang(401, 'amount_invalid');
        }
        
        $amount = floatval($post['amount']);
        if ($amount <= 0) {
            json_exit_lang(401, 'amount_must_positive');
        }
        
        // 获取EPUSDT配置
        $epusdtConfig = $this->SystemModel->getConfig('epusdt');
        if (empty($epusdtConfig) || !isset($epusdtConfig['enabled']) || $epusdtConfig['enabled'] != 1) {
            json_exit_lang(401, 'recharge_not_available');
        }
        
        $minAmount = isset($epusdtConfig['min_amount']) ? floatval($epusdtConfig['min_amount']) : 10;
        $maxAmount = isset($epusdtConfig['max_amount']) ? floatval($epusdtConfig['max_amount']) : 10000;
        
        if ($amount < $minAmount) {
            json_exit_lang(401, 'amount_too_small', ['min' => $minAmount]);
        }
        
        if ($amount > $maxAmount) {
            json_exit_lang(401, 'amount_too_large', ['max' => $maxAmount]);
        }
        
        // 验证支付方式
        $payWay = isset($post['pay_way']) ? $post['pay_way'] : 'Epusdt';
        if ($payWay !== 'Epusdt') {
            json_exit_lang(401, 'payment_method_not_supported');
        }
        
        // 创建充值订单
        $rechargeModel = new RechargeModel();
        $orderData = [
            'money' => $amount,
            'pay_way' => $payWay
        ];
        
        $rechargeModel->recharge($orderData);
    }
    
    /**
     * 查询充值订单状态
     */
    public function getOrderStatus()
    {
        $post = $this->request->param();
        
        if (!isset($post['order_no']) || empty($post['order_no'])) {
            json_exit_lang(401, 'order_no_required');
        }
        
        // 验证用户登录
        $id = base64_decode(Request::header('token'));
        if (!$id) {
            json_exit_lang(401, 'auth_error');
        }
        
        $rechargeModel = new RechargeModel();
        $order = $rechargeModel->where([
            'order_no' => $post['order_no'],
            'mid' => $id
        ])->find();
        
        if (!$order) {
            json_exit_lang(401, 'order_not_found');
        }
        
        $statusText = '';
        switch ($order['status']) {
            case 0:
                $statusText = '待支付';
                break;
            case 1:
                $statusText = '支付成功';
                break;
            case 2:
                $statusText = '支付失败';
                break;
            default:
                $statusText = '未知状态';
        }
        
        $result = [
            'order_no' => $order['order_no'],
            'amount' => $order['money'],
            'status' => $order['status'],
            'status_text' => $statusText,
            'create_time' => date('Y-m-d H:i:s', $order['create_time']),
            'pay_time' => $order['pay_time'] ? date('Y-m-d H:i:s', $order['pay_time']) : null
        ];
        
        json_exit_lang(200, 'info_get_success', $result);
    }
    
    /**
     * 获取用户充值记录
     */
    public function getRechargeHistory()
    {
        // 验证用户登录
        $id = base64_decode(Request::header('token'));
        if (!$id) {
            json_exit_lang(401, 'auth_error');
        }
        
        $page = $this->request->param('page', 1);
        $limit = $this->request->param('limit', 10);
        
        $rechargeModel = new RechargeModel();
        $list = $rechargeModel->where(['mid' => $id])
            ->order('create_time', 'desc')
            ->page($page, $limit)
            ->select();
        
        $total = $rechargeModel->where(['mid' => $id])->count();
        
        foreach ($list as &$item) {
            $statusText = '';
            switch ($item['status']) {
                case 0:
                    $statusText = '待支付';
                    break;
                case 1:
                    $statusText = '支付成功';
                    break;
                case 2:
                    $statusText = '支付失败';
                    break;
                default:
                    $statusText = '未知状态';
            }
            
            $item['status_text'] = $statusText;
            $item['create_time'] = date('Y-m-d H:i:s', $item['create_time']);
            $item['pay_time'] = $item['pay_time'] ? date('Y-m-d H:i:s', $item['pay_time']) : null;
        }
        
        $result = [
            'list' => $list,
            'total' => $total,
            'page' => $page,
            'limit' => $limit
        ];
        
        json_exit_lang(200, 'info_get_success', $result);
    }
}
