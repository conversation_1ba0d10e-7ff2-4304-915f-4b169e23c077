<?php
namespace app\api\model;

use app\api\model\LotteryModel;
use think\Model;
use think\facade\Session;
use app\api\model\MemberModel;
use app\api\model\LotterykjModel;
use app\common\Lang;
class GameModel extends Model
{
    // 设置当前模型对应的完整数据表名称
    protected $table = 'game';
    // 模型初始化
    protected static function init()
    {
        //TODO:初始化内容
    }
    public function placeOrder($post){
        $MemberModel = new MemberModel;
        $LotteryModel = new LotteryModel;
        $member_info = $MemberModel->where(['id'=>$post['mid']])->find();
        $lottery_info = $LotteryModel->where(['id'=>$post['lid']])->find();
        if($lottery_info && $member_info){
            $itemlist = explode(",",$post['item']);
            $allmoney = count($itemlist) * $post['money'];
            if($member_info['money'] - $allmoney < 0){
                json_exit_lang(401,'balance_insufficient');
            }else{
                $new_money = $member_info['money'] - $allmoney;
                if(!$MemberModel->save(['money'=>$new_money,'update_time'=>time()],['id'=>$post['mid']])){
                    json_exit_lang(401,'deduct_failed');
                }
            }
            if ($member_info['amount_code']-$allmoney < 0){
                $allmoney = $member_info['amount_code'];
            }
            $MemberModel->where(['id'=>$post['mid']])->update(['amount_code'=>$member_info['amount_code']-$allmoney]);

            $second = $lottery_info['rule'] * 60;  //获取彩种的每一期时间
            $next_time = (date('H')*60*60 + date('i') * 60);  //获取当前整数时间
            if($next_time + $second >= 86400){
                $next_time = $next_time + $second - 86400 ;
                $next_expect = date('Ymd').$lottery_info['rule'].intval($next_time/$second); //获取下一期期号       
            }else{
                $next_expect = date('Ymd').$lottery_info['rule'].intval(($next_time+$second)/$second); //获取下一期期号       
            }
            foreach ($itemlist as $k=>$v) {
                $a = $k+1;
                $b = $k;
                $time = time();
                $data['type'] = $v;
                $data['mid'] = $member_info['id'];
                $data['lid'] = $lottery_info['id'];
                $data['money'] = $post['money'];
                $data['status'] = 0;
                $data['expect'] = $next_expect;
                $data['peilv'] = $this->getLotteryPeilv($data['lid'],$data['type']);
                $data['is_win'] = 0;
                $data['create_time'] = $time;
                $data['before_betting'] = $member_info['money']-$b*$post['money'];
                $data['after_betting'] = $member_info['money']-$a*$post['money'];

                $info = $this->where(['mid'=>$member_info['id'],'type'=>$v,'expect'=>$next_expect,'create_time'=>$time])->find();

                if(!empty($info) || !$this->insert($data)){
                    json_exit_lang(401,'bet_error');
                }
            }
            json_exit_lang(200,'bet_success');
        }else {
            json_exit_lang(401,'bet_param_error',$post);
        }
        
    }
    public function getLotteryPeilv($lid,$type){
        $LotteryModel = new LotteryModel;
        $peilv = $LotteryModel->peilvJsonToDataBase($lid);
        foreach ($peilv as $k=>$v){
            if($v['type'] == $type){
                return $v['proportion'];
            }
        }
        return 0;
    }
    public function settle(){
        ini_set('max_execution_time', '0');
        $list = $this->where(['status'=>0])->select();
        $MemberModel = new MemberModel;
        $LotteryModel = new LotteryModel;
        $LotterykjModel = new LotterykjModel;
        if(!$list->isEmpty()){
            foreach($list as $k=>$v){
                $member_info = $MemberModel->where(['id'=>$v['mid']])->find();
                $lottery_info = $LotteryModel->where(['id'=>$v['lid']])->find();
                $kj_info = $LotterykjModel->where(['expect'=>$v['expect'],'key'=>$lottery_info['key']])->find();
                $data=[];

                if($kj_info){
                    // 计算开奖号码总和
                    $opencode = explode(",",$kj_info['opencode']);
                    $num = 0;
                    foreach ($opencode as $ok=>$ov){
                        $num += $ov;
                    }

                    // 判断大小单双
                    $is_big = ($num >= 11 && $num <= 18);    // 大：11-18
                    $is_small = ($num >= 3 && $num <= 10);   // 小：3-10
                    $is_odd = ($num % 2 == 1);               // 单：奇数
                    $is_even = ($num % 2 == 0);              // 双：偶数

                    $is_win = false;

                    // 处理新的投注类型
                    if($v['type'] == "big"){
                        $is_win = $is_big;
                    }elseif($v['type'] == "small"){
                        $is_win = $is_small;
                    }elseif($v['type'] == "odd"){
                        $is_win = $is_odd;
                    }elseif($v['type'] == "even"){
                        $is_win = $is_even;
                    }elseif($v['type'] == "big_odd"){
                        $is_win = ($is_big && $is_odd);
                    }elseif($v['type'] == "big_even"){
                        $is_win = ($is_big && $is_even);
                    }elseif($v['type'] == "small_odd"){
                        $is_win = ($is_small && $is_odd);
                    }elseif($v['type'] == "small_even"){
                        $is_win = ($is_small && $is_even);
                    }
                    // 兼容旧的投注类型
                    elseif($v['type'] == "漂亮"){//大
                        $is_win = $is_big;
                    }elseif ($v['type'] == "性感") {//小
                        $is_win = $is_small;
                    }elseif ($v['type'] == "迷人") {//单
                        $is_win = $is_odd;
                    }elseif ($v['type'] == "可愛") {//双
                        $is_win = $is_even;
                    }elseif ($v['type'] >= "3" && $v['type'] <= "18") {
                        $is_win = ($num == $v['type']);
                    }

                    // 设置结算结果
                    if($is_win){
                        $data['is_win'] = 1;
                        $data['status'] = 1;
                        $data['update_time'] = time();
                        $data['profit'] = $v['peilv'] * (int)$v['money']; // 奖金 = 赔率 × 投注金额
                        $jieguo = "盈利";
                    }else{
                        $data['is_win'] = 2;
                        $data['status'] = 1;
                        $data['update_time'] = time();
                        $data['profit'] = 0; // 不中奖时奖金为0
                        $jieguo = "亏损";
                    }

                    // 更新投注记录
                    $this->where(['id'=>$v['id']])->update($data);

                    // 中奖时增加用户余额
                    if($data['is_win'] == 1){
                        // 直接增加奖金到用户余额（下注时已经扣除了投注金额）
                        $MemberModel->where('id',$v['mid'])->setInc('money', $data['profit']);
                    }
                    // 不中奖时不操作余额（下注时已经扣除了投注金额）
                    echo("[".date("Y-m-d H:is",time()).
                        "] 用户：".$member_info['username'].
                        " |玩法：".$v['type'].
                        " |下注金额：".$v['money'].
                        " |盈亏：".$jieguo.
                        " |盈亏金额：".$data['profit'].
                        " |期号：".$v['expect'].
                        " |彩种：".$lottery_info['name'].
                        " |赔率：".$v['peilv'].
                        " |下注时间：".date("Y-m-d H:is",$v['create_time']).
                        " |状态："."结算成功".
                        "\n");
                    file_put_contents("../application/api/log/jiesuan.log","[".date("Y-m-d H:is",time()).
                        "] 用户：".$member_info['username'].
                        " |玩法：".$v['type'].
                        " |下注金额：".$v['money'].
                        " |盈亏：".$jieguo.
                        " |盈亏金额：".$data['profit'].
                        " |期号：".$v['expect'].
                        " |彩种：".$lottery_info['name'].
                        " |赔率：".$v['peilv'].
                        " |下注时间：".date("Y-m-d H:is",$v['create_time']).
                        " |状态："."结算成功".
                        "\n", FILE_APPEND);
                }else{
                    echo("[".date("Y-m-d H:is",time()).
                        "] 用户：".$member_info['username'].
                        " |玩法：".$v['type'].
                        " |下注金额：".$v['money'].
                        " |期号：".$v['expect'].
                        " |彩种：".$lottery_info['name'].
                        " |赔率：".$v['peilv'].
                        " |下注时间：".date("Y-m-d H:is",$v['create_time']).
                        " |状态："."等待结算中".
                        "\n");
                    file_put_contents("../application/api/log/jiesuan.log","[".date("Y-m-d H:is",time()).
                        "] 用户：".$member_info['username'].
                        " |玩法：".$v['type'].
                        " |下注金额：".$v['money'].
                        " |期号：".$v['expect'].
                        " |彩种：".$lottery_info['name'].
                        " |赔率：".$v['peilv'].
                        " |下注时间：".date("Y-m-d H:is",$v['create_time']).
                        " |状态："."等待结算中".
                        "\n", FILE_APPEND);
                }
            }
        }else{
            echo("等待结算中......"."\n");
        }
    }


    public function updateKj(){
        ini_set('max_execution_time', '0');
        $lottery = new LotteryModel;
        $YulotteryModel = new YulotteryModel;
        $lottery_list = $lottery->lotteryList('all');
        $updateList = $this->where('status',0)->group('expect')->field('expect')->select();

        foreach ($lottery_list as $k=>$v){ //遍历彩种
            $second = $v['rule'] * 60;  //获取彩种的每一期时间
            $now_time = (date('H')*60*60 + date('i') * 60);  //获取当前整数时间
            $now_expect = date('Ymd').$v['rule'].intval($now_time/$second); //获取当前期号
            $LotterykjModel = new LotterykjModel();

//            $now_info = $LotterykjModel->where(['key'=>$v['key'],'expect'=>$now_expect])->find();  //去数据库寻找当前信息是否存在

            foreach ($updateList as $key=>$val){
               if($now_expect != $val['expect']){
                   $now_expect = $val['expect'];
                   $is_yukaijiang = $YulotteryModel->where(['key'=>$v['key'],'expect'=>$now_expect])->find();  //获取是否有欲开奖
                   if(empty($is_yukaijiang)){  //不存在欲开奖走第一个分支
                       $data['key'] = $v['key'];  //获取key
                       $data['expect'] = $now_expect;  //获取期号
                       $data['opencode'] = rand(1, 6).",".rand(1, 6).",".rand(1, 6); //随机生成开奖
                       $data['is_yukaijiang'] = 0;  //自动开奖标识符
                       // 计算这一期的实际开始时间
                       $current_time = (date('H')*60*60 + date('i') * 60); // 当前秒数
                       $current_period = intval($current_time / $second); // 当前期数
                       $period_start_time = $current_period * $second; // 当前期开始时间（秒）
                       $today_start = strtotime(date('Y-m-d 00:00:00')); // 今天0点的时间戳
                       $actual_start_time = $today_start + $period_start_time; // 实际开始时间戳

                       $data['create_time'] = $actual_start_time; //使用期号对应的实际开始时间
                       $data['update_time'] = time(); //更新时间
                   }else{//存在走第二个分支
                       $data['key'] = $is_yukaijiang['key'];  //获取欲开奖的key
                       $data['expect'] = $is_yukaijiang['expect'];  //欲开奖的期号
                       $data['opencode'] = $is_yukaijiang['opencode'];//欲开奖的号码
                       $data['is_yukaijiang'] = 1;  //欲开奖标识符
                       // 计算这一期的实际开始时间
                       $current_time = (date('H')*60*60 + date('i') * 60); // 当前秒数
                       $current_period = intval($current_time / $second); // 当前期数
                       $period_start_time = $current_period * $second; // 当前期开始时间（秒）
                       $today_start = strtotime(date('Y-m-d 00:00:00')); // 今天0点的时间戳
                       $actual_start_time = $today_start + $period_start_time; // 实际开始时间戳

                       $data['create_time'] = $actual_start_time;  //使用期号对应的实际开始时间
                       $data['update_time'] = time();  //欲开奖更新时间
                   }
                   $yukaijiangtext = $data['is_yukaijiang'] ?"欲开奖":"自动开奖";
                   
                   $now_info = $LotterykjModel->where(['key'=>$v['key'],'expect'=>$val['expect']])->find();  //去数据库寻找当前信息是否存在
           
                   if(empty($now_info)){
                       if($LotterykjModel->insert($data)){
                           echo("标识符:".$v['key']."---彩种名称:".$v['name']."---欲开奖:".$yukaijiangtext."---开奖成功--- 期号:".$now_expect."---开奖号码:".$data['opencode']."---开奖时间:".date("Y-m-d H:i",time())."---下一期开奖时间:".date("Y-m-d H:i",time()+$second)."\n");
                           file_put_contents("../application/api/log/kj.log","标识符:".$v['key']."---彩种名称:".$v['name'].$yukaijiangtext."---开奖成功--- 期号:".$now_expect."---开奖号码:".$data['opencode']."---开奖时间:".date("Y-m-d H:i",time())."---下一期开奖时间：".date("Y-m-d H:i",time()+$second)."\n", FILE_APPEND);
                           continue;
                       }else {
                           echo("标识符:".$v['key']."---彩种名称：".$v['name']."---欲开奖:".$yukaijiangtext."---开奖失败--- 期号:".$now_expect."---开奖号码:".$data['opencode']."---开奖时间:".date("Y-m-d H:i",time())."---下一期开奖时间：".date("Y-m-d H:i",time()+$second)."\n");
                           file_put_contents("../application/api/log/kj.log","标识符:".$v['key']."---彩种名称:".$v['name'].$yukaijiangtext."---开奖失败--- 期号:".$now_expect."---开奖号码:".$data['opencode']."---开奖时间:".date("Y-m-d H:i",time())."---下一期开奖时间:".date("Y-m-d H:i",time()+$second)."\n", FILE_APPEND);
                           continue;
                       }
                   }else{
                       echo("等待开奖中......"."\n");
                       continue;
                   }
               }
            }
        }
    }

    public function updateNewKj(){
        ini_set('max_execution_time', '0');
        $lottery = new LotteryModel;
        $YulotteryModel = new YulotteryModel;
        $lottery_list = $lottery->lotteryList('all');
//        $updateList = $this->where('status',0)->group('expect')->field('expect')->select();

        $exceptArr = [];
        foreach ($lottery_list as $k=>$v){ //遍历彩种
            $second = $v['rule'] * 60;  //获取彩种的每一期时间
            $now_time = (date('H')*60*60 + date('i') * 60);  //获取当前整数时间
            $num = intval($now_time/$second);
            $now_expect = date('Ymd').$v['rule'].$num; //获取当前期号
            $LotterykjModel = new LotterykjModel();

            for($a = 0;$a < $num;$a++){
                $exceptArr[] = $now_expect = date('Ymd').$v['rule'].$a;
            }

            foreach ($exceptArr as $key=>$val){
                if($now_expect != $val){
                    $now_expect = $val;
                    $is_yukaijiang = $YulotteryModel->where(['key'=>$v['key'],'expect'=>$now_expect])->find();  //获取是否有欲开奖
                    if(empty($is_yukaijiang)){  //不存在欲开奖走第一个分支
                        $data['key'] = $v['key'];  //获取key
                        $data['expect'] = $now_expect;  //获取期号
                        $data['opencode'] = rand(1, 6).",".rand(1, 6).",".rand(1, 6); //随机生成开奖
                        $data['is_yukaijiang'] = 0;  //自动开奖标识符
                        // 从期号中提取期数，计算对应的开始时间
                        $expect_parts = str_split($now_expect);
                        $date_part = substr($now_expect, 0, 8); // 日期部分 YYYYMMDD
                        $rule_part = substr($now_expect, 8, 1); // 规则部分
                        $period_part = (int)substr($now_expect, 9); // 期数部分

                        $period_start_time = $period_part * $second; // 这一期开始时间（秒）
                        $day_start = strtotime($date_part); // 这一天0点的时间戳
                        $actual_start_time = $day_start + $period_start_time; // 实际开始时间戳

                        $data['create_time'] = $actual_start_time; //使用期号对应的实际开始时间
                        $data['update_time'] = time(); //更新时间
                    }else{//存在走第二个分支
                        $data['key'] = $is_yukaijiang['key'];  //获取欲开奖的key
                        $data['expect'] = $is_yukaijiang['expect'];  //欲开奖的期号
                        $data['opencode'] = $is_yukaijiang['opencode'];//欲开奖的号码
                        $data['is_yukaijiang'] = 1;  //欲开奖标识符
                        // 从期号中提取期数，计算对应的开始时间
                        $expect_parts = str_split($now_expect);
                        $date_part = substr($now_expect, 0, 8); // 日期部分 YYYYMMDD
                        $rule_part = substr($now_expect, 8, 1); // 规则部分
                        $period_part = (int)substr($now_expect, 9); // 期数部分

                        $period_start_time = $period_part * $second; // 这一期开始时间（秒）
                        $day_start = strtotime($date_part); // 这一天0点的时间戳
                        $actual_start_time = $day_start + $period_start_time; // 实际开始时间戳

                        $data['create_time'] = $actual_start_time;  //使用期号对应的实际开始时间
                        $data['update_time'] = time();  //欲开奖更新时间
                    }
                    $yukaijiangtext = $data['is_yukaijiang'] ?"欲开奖":"自动开奖";

                    $now_info = $LotterykjModel->where(['key'=>$v['key'],'expect'=>$val])->find();  //去数据库寻找当前信息是否存在

                    if(empty($now_info)){
                        if($LotterykjModel->insert($data)){
                            echo("标识符:".$v['key']."---彩种名称:".$v['name']."---欲开奖:".$yukaijiangtext."---开奖成功--- 期号:".$now_expect."---开奖号码:".$data['opencode']."---开奖时间:".date("Y-m-d H:i",time())."---下一期开奖时间:".date("Y-m-d H:i",time()+$second)."\n");
                            file_put_contents("../application/api/log/kj.log","标识符:".$v['key']."---彩种名称:".$v['name'].$yukaijiangtext."---开奖成功--- 期号:".$now_expect."---开奖号码:".$data['opencode']."---开奖时间:".date("Y-m-d H:i",time())."---下一期开奖时间：".date("Y-m-d H:i",time()+$second)."\n", FILE_APPEND);
                            continue;
                        }else {
                            echo("标识符:".$v['key']."---彩种名称：".$v['name']."---欲开奖:".$yukaijiangtext."---开奖失败--- 期号:".$now_expect."---开奖号码:".$data['opencode']."---开奖时间:".date("Y-m-d H:i",time())."---下一期开奖时间：".date("Y-m-d H:i",time()+$second)."\n");
                            file_put_contents("../application/api/log/kj.log","标识符:".$v['key']."---彩种名称:".$v['name'].$yukaijiangtext."---开奖失败--- 期号:".$now_expect."---开奖号码:".$data['opencode']."---开奖时间:".date("Y-m-d H:i",time())."---下一期开奖时间:".date("Y-m-d H:i",time()+$second)."\n", FILE_APPEND);
                            continue;
                        }
                    }else{
                        echo("等待开奖中......"."\n");
                        continue;
                    }
                }
            }

//


        }



    }
}