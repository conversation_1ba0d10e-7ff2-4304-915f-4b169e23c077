<?php
namespace app\api\model;

use think\Model;
use think\facade\Session;
use think\facade\Request;
use think\facade\Cookie;
// use app\api\model\UserModel;
use app\common\Lang;
class XuanfeiModel extends Model
{
    // 设置当前模型对应的完整数据表名称
    protected $table = 'xuanfei_list';
    // 模型初始化
    protected static function init()
    {
        //TODO:初始化内容
    }
    public function getList($id){
        if (empty($id)) {
            json_exit_lang(401,'id_empty');
        }
        $lang = Request::header('lang');
        $list = $this->where(['class_id'=>$id])->order('id','asc')->select()->toArray();
        $filtered_list = [];

        foreach ($list as $k=>$v){
            $v['xuanfei_name'] = $v[$lang];

            // 安全处理女生素材图片
            $img_urls = json_decode($v['img_url'], true);
            if (is_array($img_urls) && count($img_urls) > 0) {
                // 只有当有图片时才添加到返回列表中
                $v['img_url'] = 'http://'.$_SERVER['SERVER_NAME'] .'/'. $img_urls[0];
                $v['has_female_images'] = true;

                // 添加视频相关信息
                if (!empty($v['video_url'])) {
                    $v['video_url'] = 'http://'.$_SERVER['SERVER_NAME'] .'/'. $v['video_url'];
                }
                if (!empty($v['video_preview'])) {
                    $v['video_preview'] = 'http://'.$_SERVER['SERVER_NAME'] .'/'. $v['video_preview'];
                }
                // 确保视频时长和大小字段存在
                $v['video_duration'] = isset($v['video_duration']) ? intval($v['video_duration']) : 0;
                $v['video_size'] = isset($v['video_size']) ? intval($v['video_size']) : 0;

                // 只有有图片的记录才添加到结果中
                $filtered_list[] = $v;
            }
            // 没有图片的记录直接跳过，不添加到返回列表中
        }
        json_exit_lang(200,'info_get_success',$filtered_list);
    }

    public function getMaleList($id){
        if (empty($id)) {
            json_exit_lang(401,'id_empty');
        }
        $lang = Request::header('lang');
        $list = $this->where(['class_id'=>$id])->order('id','asc')->select()->toArray();
        $filtered_list = [];

        foreach ($list as $k=>$v){
            $v['xuanfei_name'] = $v[$lang];

            // 安全处理男生素材图片
            $male_images = json_decode($v['male_images'], true);
            if (is_array($male_images) && count($male_images) > 0) {
                // 只有当有男生素材图片时才添加到返回列表中
                $v['img_url'] = 'http://'.$_SERVER['SERVER_NAME'] .'/'. $male_images[0];
                $v['has_male_images'] = true;

                // 添加视频相关信息
                if (!empty($v['video_url'])) {
                    $v['video_url'] = 'http://'.$_SERVER['SERVER_NAME'] .'/'. $v['video_url'];
                }
                if (!empty($v['video_preview'])) {
                    $v['video_preview'] = 'http://'.$_SERVER['SERVER_NAME'] .'/'. $v['video_preview'];
                }
                // 确保视频时长和大小字段存在
                $v['video_duration'] = isset($v['video_duration']) ? intval($v['video_duration']) : 0;
                $v['video_size'] = isset($v['video_size']) ? intval($v['video_size']) : 0;

                // 只有有男生素材图片的记录才添加到结果中
                $filtered_list[] = $v;
            }
            // 没有男生素材图片的记录直接跳过，不添加到返回列表中
        }
        json_exit_lang(200,'info_get_success',$filtered_list);
    }
    
    public function xuanfeidata($id)
    {
        $lang = Request::header('lang');
        if (empty($id)) {
            json_exit_lang(401,'id_empty');
        }
        $data = $this->where(['id'=>$id])->find();
        $data['xuanfei_name'] = $data[$lang];
        // 安全处理女生素材图片数组
        $img_urls = json_decode($data['img_url'], true);
        $a = [];
        if (is_array($img_urls) && count($img_urls) > 0) {
            foreach ($img_urls as $k => $v){
                if (!empty($v)) { // 确保图片路径不为空
                    $a[$k] = 'http://'.$_SERVER['SERVER_NAME'] .'/'. $v;
                }
            }
        }

        // 只有当有图片时才返回img_url字段
        if (count($a) > 0) {
            $data['img_url'] = $a;
        } else {
            // 如果没有图片，删除img_url字段
            unset($data['img_url']);
        }

        // 添加视频相关信息
        if (!empty($data['video_url'])) {
            $data['video_url'] = 'http://'.$_SERVER['SERVER_NAME'] .'/'. $data['video_url'];
        }
        if (!empty($data['video_preview'])) {
            $data['video_preview'] = 'http://'.$_SERVER['SERVER_NAME'] .'/'. $data['video_preview'];
        }
        // 确保视频时长和大小字段存在
        $data['video_duration'] = isset($data['video_duration']) ? intval($data['video_duration']) : 0;
        $data['video_size'] = isset($data['video_size']) ? intval($data['video_size']) : 0;

        // 原有xuanfeidata接口保持为女生素材，不添加男生素材
        // 保持原有接口的兼容性

        json_exit_lang(200,'info_get_success',$data);
    }

    public function getMaleImages($get)
    {
        if (empty($get['id'])) {
            json_exit_lang(401,'id_empty');
        }

        $data = $this->where(['id'=>$get['id']])->find();
        if (empty($data)) {
            json_exit_lang(401,'data_not_found');
        }

        $result = [];

        // 处理男生素材图片
        if (!empty($data['male_images'])) {
            $male_images = json_decode($data['male_images'], true);
            if (is_array($male_images)) {
                $result = array_map(function($img) {
                    return 'http://'.$_SERVER['SERVER_NAME'] .'/'. $img;
                }, $male_images);
            }
        }

        json_exit_lang(200,'info_get_success',$result);
    }

    public function getFemaleImages($get)
    {
        if (empty($get['id'])) {
            json_exit_lang(401,'id_empty');
        }

        $data = $this->where(['id'=>$get['id']])->find();
        if (empty($data)) {
            json_exit_lang(401,'data_not_found');
        }

        $result = [];

        // 处理女生素材图片（原有的img_url字段）
        if (!empty($data['img_url'])) {
            $female_images = json_decode($data['img_url'], true);
            if (is_array($female_images)) {
                $result = array_map(function($img) {
                    return 'http://'.$_SERVER['SERVER_NAME'] .'/'. $img;
                }, $female_images);
            }
        }

        json_exit_lang(200,'info_get_success',$result);
    }
}