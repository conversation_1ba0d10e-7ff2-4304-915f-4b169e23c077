<?php
namespace app\api\model;

use think\Model;
use think\facade\Session;
use think\facade\Request;

// use app\api\model\UserModel;
class VideoclassModel extends Model
{
    // 设置当前模型对应的完整数据表名称
    protected $table = 'video_class';
    // 模型初始化
    protected static function init()
    {
        //TODO:初始化内容
    }
    public function getItemList(){
        $lang = Request::header('lang');
        $list= $this->where(['status'=>1])->order('sort','asc')->select();
        foreach ($list as $k=>&$v){
        //   $v['name'] = $v[$lang];
        }
        return $list;
    }
}