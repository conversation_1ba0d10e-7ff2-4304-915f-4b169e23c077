<?php
namespace app\api\model;

use think\Model;
use think\facade\Session;
use think\facade\Request;
use think\facade\Cookie;
// use app\api\model\UserModel;
use app\common\Lang;
class VideoModel extends Model
{
    // 设置当前模型对应的完整数据表名称
    protected $table = 'video';
    // 模型初始化
    protected static function init()
    {
        //TODO:初始化内容
    }
    public function getHotList(){
         $lang = Request::header('lang');
        $list = $this->where(['vod_status'=>1,'vod_hot'=>1])->order('id','asc')->select();
        foreach ($list as $k=>&$v) {
            $v['title'] = $v['vod_name_'.$lang];
            $v['cover'] = $v['vod_pic'];
            $v['time'] = $v['vod_duration'];
            $v['create_time'] = date("Y-m-d H:i:s",$v['create_time']);
        }
        return $list;
    }
    public function getVideoInfo($post){
        $id = base64_decode(Request::header('token'));
        $lang = Request::header('lang');
        $info = $this->where(['id'=>$post['id']])->find();
        if($info){
            $info['vod_name'] = $info['vod_name_'.$lang];
            $info['count'] = $info['vod_score_num'];
            json_exit_lang(200,'info_get_success',$info);
        }else{
            json_exit_lang(401,'auth_error');
        }
    }    
    public function classToID($id){
        $VideoclassModel = new VideoclassModel;
        $data = $VideoclassModel->getItemList();
        return $data[$id];
    }    
    public function getVideoList($post){
        $id = base64_decode(Request::header('token'));
        $lang = Request::header('lang');
        $info = $this->classToID($post['id']);

        // 构建查询条件
        $where = ['vod_class_id' => $info['id']];

        // 修复分页问题：前端页码从0开始，但ThinkPHP的page()需要从1开始
        // 将前端的页码转换为ThinkPHP的页码
        $page = isset($post['page']) ? (int)$post['page'] : 0;
        $page = $page + 1; // 前端0对应后端1，前端1对应后端2

        // 添加调试日志
        error_log("分页调试 - 前端传入page: " . ($post['page'] ?? 'null') . ", 实际使用page: " . $page);

        // 一次性获取分页数据和总数，避免重复查询
        $list = $this->limit(8)->page($page)->where($where)->order('update_time','desc')->select();
        $count = $this->where($where)->count();

        if($list){
            foreach ($list as $k=>&$v){
                $picArr= explode('/', $v['vod_pic']);
                if($picArr[2]!='fmlb.netlbtu.com' && $picArr[2]!='lbfm.lbpictupian.com'){
                    $v['vod_pic']=Request::domain().$v['vod_pic'];
                }
                $v['vod_name'] = $v['vod_name_'.$lang];
                $v['count'] = $v['vod_score_num'];
            }
            json_exit_lang(200,'info_get_success',['data'=>$list,'count'=>$count]);
        }else{
            json_exit_lang(401,'auth_error');
        }
    }
    public function getRandHotList($count){
        $lang = Request::header('lang');
        $list = $this->where(['vod_status'=>1])->orderRaw('rand()')->limit($count)->order('id','asc')->select();
        foreach ($list as $k=>&$v) {
            $v['vod_name'] = $v['vod_name_'.$lang];
            $v['cover'] = $v['vod_pic'];
            $v['time'] = $v['vod_duration'];
            $v['count'] = $v['vod_score_num'];
            $v['create_time'] = date("Y-m-d H:i:s",$v['create_time']);
        }
        return $list;        
    }
}