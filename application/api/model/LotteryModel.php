<?php
namespace app\api\model;

use think\Model;
use think\facade\Session;
use think\facade\Request;
use app\api\model\LotterykjModel;
use app\api\model\LotterypeilvModel;
use app\common\Lang;
class LotteryModel extends Model
{
    // 设置当前模型对应的完整数据表名称
    protected $table = 'lottery';
    // 模型初始化
    protected static function init()
    {
        //TODO:初始化内容
    }
    public function getLotteryPeilv($post){
        $list = $this->peilvJsonToDataBase($post['id']);
        foreach ($list as $k=>$v){
            if($v['status'] != 1){
                unset($list[$k]);
            }
        }
        
        json_exit_lang(200,'info_get_success',$list);
    }
    public function peilvJsonToDataBase($id){
        $info = $this->where(['id'=>$id])->find();
        $lang = Request::header('lang');
        if($info){
            $json = file_get_contents('../application/admin/data/peilv_ko_hy.json'); 
            $data = json_decode($json, true);
            $LotterypeilvModel = new LotterypeilvModel;
            $lottery_peilv = $LotterypeilvModel->where(['lid'=>$info['id']])->select();
            $arr=[];
            foreach ($data as $k=>$v){
                if(count($lottery_peilv) == 0){ //判断是否没有任何自定义数组
                    $arr = $data;  //
                }else{
                    foreach($lottery_peilv as $key=>$value){
                        $is_ = false;
                        if($v['type'] == $value['type']){
                            $arr[] = $value;
                            $is_ = true;
                            break;
                        }
                    }
                    if($is_ === false){
                        $arr[] = $v;
                    }
                }
            }
            return $arr;
        }else{
            json_exit_lang(401,'data_not_exists');
        }
    }
    public function getLotteryOneList($post){
        $id = base64_decode(Request::header('token'));
        $member = new MemberModel;
        $LotterykjModel = new LotterykjModel;
        $info = $member->where(['id'=>$id])->find();
        if($info){
            $lottery_info = $this->where(['key'=>$post['key']])->find();
            if(!$lottery_info){
                json_exit_lang(401,'data_collection_failed');
            }else{
                $data = $LotterykjModel->where(['key'=>$lottery_info['key']])->limit(30)->order('id','desc')->select();
                foreach ($data as $k=>&$v) {
                    $opencode = explode(",",$v['opencode']);
                    $opencode[0] = (int)$opencode[0];
                    $opencode[1] = (int)$opencode[1];
                    $opencode[2] = (int)$opencode[2];                    
                    $v['opencode'] = $opencode;
                }
                json_exit_lang(200,'info_get_success',$data);
            }
        }else {
            json_exit_lang(401,'auth_error');
        }
    }
    public function getLotteryInfo($post){
        $lang = Request::header('lang');
        $id = base64_decode(Request::header('token'));
        $member = new MemberModel;
        $info = $member->where(['id'=>$id])->find();
        if($info){
            $lottery_info = $this->where(['key'=>$post['key']])->find();
            if(empty($lottery_info)){
                json_exit_lang(401,'data_collection_failed');
            }else{
                // $lottery_info['name']=$lottery_info['name_'.$lang];
                $lottery_info['desc']=$lottery_info['desc_'.$lang];
                $lottery_info['second'] = $lottery_info['rule'] * 60;
                $now_time = (date('H')*60*60 + date('i') * 60);
                $lottery_info['now_expect'] = date('Ymd').$lottery_info['rule'].intval($now_time/$lottery_info['second']);
                $second = $lottery_info['rule'] * 60;  //获取彩种的每一期时间
                $next_time = (date('H')*60*60 + date('i') * 60);  //获取当前整数时间
                if($next_time + $second >= 86400){
                    $next_time = $next_time + $second - 86400 ;
                    $next_expect = date('Ymd').$lottery_info['rule'].intval($next_time/$second); //获取下一期期号       
                }else{
                    $next_expect = date('Ymd').$lottery_info['rule'].intval(($next_time+$second)/$second); //获取下一期期号       
                }
                $lottery_info['next_expect'] = $next_expect;
                $LotterykjModel = new LotterykjModel;
                $now_info = $LotterykjModel->where(['key'=>$lottery_info['key'],'expect'=>$lottery_info['now_expect']])->find();
                if($now_info){
                    // 重新计算倒计时 - 基于期号和当前时间
                    $current_time = (date('H')*60*60 + date('i') * 60 + date('s')); // 当前秒数
                    $current_period = intval($current_time / $lottery_info['second']); // 当前期数
                    $next_period_start = ($current_period + 1) * $lottery_info['second']; // 下一期开始时间

                    // 如果下一期开始时间超过一天，则是明天的第一期
                    if($next_period_start >= 86400) {
                        $lottery_info['second'] = 86400 - $current_time + ($next_period_start - 86400);
                    } else {
                        $lottery_info['second'] = $next_period_start - $current_time;
                    }

                    // 确保倒计时不为负数
                    if($lottery_info['second'] <= 0) {
                        $lottery_info['second'] = $lottery_info['rule'] * 60; // 重置为一个周期
                    }

                    $opencode = explode(",",$now_info['opencode']);
                    $opencode[0] = (int)$opencode[0];
                    $opencode[1] = (int)$opencode[1];
                    $opencode[2] = (int)$opencode[2];
                    $lottery_info['opencode'] = $opencode;
                }
                json_exit_lang(200,'info_get_success',$lottery_info);
            }
        }else{
            json_exit_lang(401,'auth_error');
        } 
    }    
    public function hotLottery(){
       return $this->where(['status'=>1,'hot'=>1])->order('id','asc')->select();
    }
    public function lotteryList($info){
        $lang = Request::header('lang');
        $fields = "id,name,name_zh_cn,name_en_us,name_es_spa,name_ms_my,name_yn_yu,name_idn_yu,status,rule,cid,condition,hot,key,ico,create_time,update_time";  //desc三分钟一期
        if($info =="all"){
            $list= $this->field($fields)->where(['status'=>1])->order('id','asc')->select();
        }else {
            $list= $this->field($fields)->where(['status'=>1,'cid'=>$info['id']])->order('id','asc')->select();
        }
         foreach ($list as $k=>&$v){
             if ($lang !=null){
                //   $v['name'] = $v['name_'.$lang];
             }
           
        }
        return $list;
    }
}