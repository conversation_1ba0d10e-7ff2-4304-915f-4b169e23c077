<?php
namespace app\api\model;

use think\Model;
use think\facade\Session;
use think\facade\Request;
use think\facade\Cookie;
use app\admin\model\UserModel;
use app\admin\model\BlackIpModel;
use think\Db;
use app\common\Lang;
use app\api\model\RechargeModel;
use app\api\model\SystemModel;
use app\api\model\Member_registerModel;
class MemberModel extends Model
{
    // 设置当前模型对应的完整数据表名称
    protected $table = 'member';
    // 模型初始化
    protected static function init()
    {
        //TODO:初始化内容
    }
    public function uploadHeaderImg($post){
        $id = base64_decode(Request::header('token'));
        $lang = Request::header('lang');
        $info = $this->where(['id'=>$id])->find();
        if($info){
            $data['update_time'] = time();
            $data['header_img'] = $post['header_img'];
            $res = $this->save($data,['id'=>$id]);
            if ($res){
                $tips=Db::name('tips')->find(3);
                json_exit_Base64(200,  $tips[$lang]);//更换头像成功
            }else{
                $tips=Db::name('tips')->find(4);
                json_exit_Base64(401, $tips[$lang]);//更新头像失败
            }
        }else{
            $tips=Db::name('tips')->find(5);
            json_exit_Base64(401, $tips[$lang]);//鉴权错误
        }
    }
    public function setBank($post){
        $id = base64_decode(Request::header('token'));
        $info = $this->where(['id'=>$id])->find();
        if($info){
            if(empty($post['username'])){
                json_exit_lang(401,'real_name_required');
            }
            if(empty($post['mobile'])){
                json_exit_lang(401,'contact_info_required');
            }
            // if(empty($info['name'])){
            //     json_exit_Base64(401,'Hãy nhập tên thật của bạn và sau đó điền vào thẻ ngân hàng!');
            // }
            // if(empty($info['paypassword'])){
            //     json_exit_Base64(401,'Vui lòng nhập mật khẩu rút tiền trước khi điền thẻ ngân hàng!');
            // }    
            $BankModel = new BankModel;
            if($BankModel->where(['uid'=>$info['id']])->find()){
                json_exit_lang(401,'bank_card_already_set');
            }
            $data['uid'] = $info['id'];
            $data['bankid'] = $post['bankid'];
            $data['username'] = $post['username'];
            $data['mobile'] = $post['mobile'];
            $data['bank_code'] = $post['bank_code'];
            $data['bankinfo'] = $post['bank'];
            $data['create_time'] = time();
            $data['update_time'] = time();
           
            $this->where(['id'=>$id])->update(['name' => $post['username'],'paypassword'=>md5($post['opw'])]);
            
            $BankModel->insert($data) ? json_exit_lang(200,'bank_bind_success') : json_exit_lang(401,'bank_bind_failed');
        }else{
            json_exit_lang(401,'auth_error');
        }
    }
    public function setPayPassword($post){
        $id = base64_decode(Request::header('token'));
        $info = $this->where(['id'=>$id])->find();
        if($info){
            $data['update_time'] = time();
            $data['paypassword'] = md5($post['paypassword']);
            $this->save($data,['id'=>$id]) ? json_exit_lang(200,'pay_password_set_success') : json_exit_lang(401,'pay_password_set_failed');
        }else{
            json_exit_lang(401,'auth_error');
        }
    }  
    public function setLoginPassword($post){
        $id = base64_decode(Request::header('token'));
        $info = $this->where(['id'=>$id])->find();
        if(md5($post['old_password']) !== $info['password']){
            json_exit_lang(401,'old_password_error');
        }
        if($info){
            $data['update_time'] = time();
            $data['password'] = md5($post['new_password']);
            $this->save($data,['id'=>$id]) ? json_exit_lang(200,'password_updated') : json_exit_lang(401,'password_update_failed');
        }else{
            json_exit_lang(401,'auth_error');
        }
    }     
    public function setName($post){
        
        $id = base64_decode(Request::header('token'));
        $info = $this->where(['id'=>$id])->find();
        
        /*if(!isChineseName($post['name'])){
            
             json_exit_Base64(401,'xin vui lòng nhập tên thật của bạn!');
        }*/
        if($info){
            $data['update_time'] = time();
            $data['name'] = $post['name'];
            $this->save($data,['id'=>$id]) ? json_exit_lang(200,'name_set_success') : json_exit_lang(401,'name_set_failed');
        }else{
            json_exit_lang(401,'auth_error');
        }
    }
    public function setSex($post){
        
        $id = base64_decode(Request::header('token'));
        $lang = Request::header('lang');
        $info = $this->where(['id'=>$id])->find();
        if($info){
            $data['update_time'] = time();
            $data['sex'] = $post['sex'];
            $res = $this->save($data,['id'=>$id]);
            if ($res){
                json_exit_lang(200,'gender_set_success');//设置性别成功
            }else{
                json_exit_lang(401,'gender_set_failed');//设置性别失败
            }
        }else{
            json_exit_lang(401,'auth_error');
        }
    }  
    public function setUserWirhdraw($post){
       
        $id = base64_decode(Request::header('token'));
        $info = $this->where(['id'=>$id])->find();
        if($info){
            if ($info['score'] < 100){
                json_exit_lang(401,'credit_insufficient');
            }
             
            // if($info[''] < $role['min']){
            //     json_exit_Base64(401,'提幣金額不能小於:'. $role['min']);
            // }
            // echo 11;die;
            $role = $this->funGetUserWithdrawRole($info['id']);
            if($post['money'] < $role['min']){
                json_exit_lang(401,'withdraw_amount_min_error', [], ['{min}' => $role['min']]);
            }
            if($post['money'] > $role['max']){
                json_exit_lang(401,'withdraw_amount_max_error', [], ['{max}' => $role['max']]);
            }
            $WithdrawModel = new WithdrawModel;
            $count = $WithdrawModel->where(['mid'=>$info['id']])->whereTime('create_time', 'between', [strtotime(date("Y-m-d")), strtotime(date("Y-m-d",strtotime("+1 day")))])->count();
            if($count == $role['num']){
                json_exit_lang(401,'withdraw_limit_exceeded');
            }
            $usermoney = $info['money'] - $post['money'];
            if($usermoney < 0){
                json_exit_lang(401,'withdraw_format_error');
            }
            
            $orderSn='W'.date('YmdHis').rand(10,99);
            $data['mid'] = $info['id'];
            $data['status'] = 1;
            $data['order_no']=$orderSn;
            $data['money'] = $post['money'];
            $data['create_time'] = time();
            $data['update_time'] = time();
            if($this->save(['money'=>$usermoney],['id'=>$info['id']])){
                $WithdrawModel->save($data) ? json_exit_lang(200,'withdraw_success') : json_exit_lang(401,'withdraw_failed');
            }else{
                json_exit_lang(401,'deduct_failed');
            }

        }else{
            json_exit_lang(401,'auth_error');
        }
    }    
    public function funGetUserWithdrawRole($mid){
        $info = $this->where(['id'=>$mid])->find();
        if($info['num'] && $info['min'] && $info['max'] ){
            $data = [
                'num'=>$info['num'],
                'min'=>$info['min'],
                'max'=>$info['max'],
                ];
        }else{
            $SystemModel = new SystemModel;
            $sys_info = $SystemModel->getConfig("base");
            $data = [
                'num'=>$sys_info['withraw_num'],
                'min'=>$sys_info['withraw_min'],
                'max'=>$sys_info['withraw_max'],
                ];
        }
        return $data;
    }
    public function getUserGameList(){
        $id = base64_decode(Request::header('token'));
        $lang = Request::header('lang');
        $info = $this->where(['id'=>$id])->find();
        if($info){
            $GameModel= new GameModel;
            $LotteryModel= new LotteryModel;
            $LotterykjModel = new LotterykjModel;
            $list = $GameModel->order('id','desc')->where(['mid'=>$info['id']])->select();
            
           
            foreach ($list as $k=>$v){
                $v['lottery'] = $LotteryModel->where(['id'=>$v['lid']])->find();
                $data = $LotterykjModel->where(['expect'=>$v['expect'],'key'=>$v['lottery']['key']])->find();
                if(!$data && $v['status']==1){
                    unset($list[$k]);
                }
            }
            
            
            foreach ($list as $k=>&$v){
                $v['lottery'] = $LotteryModel->where(['id'=>$v['lid']])->find();
                if($v['status'] === 0){
                    // 使用多语言系统替代tips表
                    $v['status_text'] = Lang::get('game_settling');//结算中
                    $v['isAdopt'] = false;
                }elseif($v['status'] === 1){
                    // 使用多语言系统替代tips表
                    $v['status_text'] = Lang::get('game_settled');//已结算
                    $v['isAdopt'] = true;
                    $data = $LotterykjModel->where(['expect'=>$v['expect'],'key'=>$v['lottery']['key']])->find();
                    $opencode = explode(",",$data['opencode']);
                    $opencode[0] = (int)$opencode[0];
                    $opencode[1] = (int)$opencode[1];
                    $opencode[2] = (int)$opencode[2];
                    $v['opencode'] = $opencode;
                }
                if($v['is_win'] === 0){
                    // 使用多语言系统替代tips表
                    $v['win_text'] = Lang::get('game_settling');//结算中
                }else if($v['is_win'] === 1){
                    // 使用多语言系统替代tips表
                    $v['win_text'] = Lang::get('game_profit');//盈利
                }else if($v['is_win'] === 2){
                    // 使用多语言系统替代tips表
                    $v['win_text'] = Lang::get('game_loss');//亏损
                }
                if($v['update_time'] == 0){
                    // 使用多语言系统替代tips表
                    $v['update_time'] = Lang::get('game_pending');//待结算
                }else{
                    $v['update_time'] = date("Y-m-d H:i:s",$v['update_time']);
                }
                $v['create_time'] = date("Y-m-d H:i:s",$v['create_time']);
            }
            
            
                       
            $data = [];
            $i=0;
            foreach($list->toArray() as $key=>$value){
                if(isset($data[$value['expect']])){
                    $data[$value['expect']]['id'] = $value['id'];
                    $data[$value['expect']]['count'] +=1;
                    $data[$value['expect']]['money'] +=$value['money'];
                    $data[$value['expect']]['data'][]  = $value;
                }else{
                    $data[$value['expect']]['id'] = $value['id'];
                    $data[$value['expect']]['status']=1;
                    $data[$value['expect']]['ico'] =  $value['lottery']['ico'];
                    // $data[$value['expect']]['name'] =  $value['lottery']['name_'.$lang];
                    $data[$value['expect']]['name'] =  $value['lottery']['name'];
                    $data[$value['expect']]['expect']  = $value['expect'];
                    $data[$value['expect']]['count'] =1;
                    $data[$value['expect']]['money'] =$value['money'];
                    $data[$value['expect']]['data'][]  = $value;
                    if(isset($value['opencode'])) {
                        $data[$value['expect']]['opencode'] = $value['opencode'];
                    }else{
                        $data[$value['expect']]['status']=0;
                    }
                }
                $data[$value['expect']]['update_time']  = $value['update_time'];
                $data[$value['expect']]['create_time']  = $value['create_time'];
                $data[$value['expect']]['is_win']  = $value['is_win'];
            }
            
// var_dump($data);die;
            $data = $this->arraySort($data, 'id', SORT_DESC);
            //echo json_encode(['code'=>200,"message"=>"获取信息成功！","data"=>$data]);die;
            json_exit_lang(200,'info_get_success',$data);
        }else{
            json_exit_lang(401,'auth_error');
        }
    }
    
    public function getUserGame2List(){
        $id = base64_decode(Request::header('token'));
        $lang = Request::header('lang');
        $info = $this->where(['id'=>$id])->find();
        if($info){
            $GameModel= new GameModel;
            $LotteryModel= new LotteryModel;
            $LotterykjModel = new LotterykjModel;
            $list = $GameModel->order('id','desc')->where(['mid'=>$info['id']])->select();
            
           
            foreach ($list as $k=>$v){
                $v['lottery'] = $LotteryModel->where(['id'=>$v['lid']])->find();
                $data = $LotterykjModel->where(['expect'=>$v['expect'],'key'=>$v['lottery']['key']])->find();
                if(!$data && $v['status']==1){
                    unset($list[$k]);
                }
            }
            
            
            foreach ($list as $k=>&$v){
                $v['lottery'] = $LotteryModel->where(['id'=>$v['lid']])->find();
                if($v['status'] === 0){
                    // 使用多语言系统替代tips表
                    $v['status_text'] = Lang::get('game_settling');//结算中
                    $v['isAdopt'] = false;
                }elseif($v['status'] === 1){
                    // 使用多语言系统替代tips表
                    $v['status_text'] = Lang::get('game_settled');//已结算
                    $v['isAdopt'] = true;
                    $data = $LotterykjModel->where(['expect'=>$v['expect'],'key'=>$v['lottery']['key']])->find();
                    $opencode = explode(",",$data['opencode']);
                    $opencode[0] = (int)$opencode[0];
                    $opencode[1] = (int)$opencode[1];
                    $opencode[2] = (int)$opencode[2];
                    $v['opencode'] = $opencode;
                }
                if($v['is_win'] === 0){
                    // 使用多语言系统替代tips表
                    $v['win_text'] = Lang::get('game_settling');//结算中
                }else if($v['is_win'] === 1){
                    // 使用多语言系统替代tips表
                    $v['win_text'] = Lang::get('game_profit');//盈利
                }else if($v['is_win'] === 2){
                    // 使用多语言系统替代tips表
                    $v['win_text'] = Lang::get('game_loss');//亏损
                }
                if($v['update_time'] == 0){
                    // 使用多语言系统替代tips表
                    $v['update_time'] = Lang::get('game_pending');//待结算
                }else{
                    $v['update_time'] = date("Y-m-d H:i:s",$v['update_time']);
                }
                $v['create_time'] = date("Y-m-d H:i:s",$v['create_time']);
            }
            
            
                       
            
            
// var_dump($data);die;
            // $data = $this->arraySort($list, 'id', SORT_DESC);
            //echo json_encode(['code'=>200,"message"=>"获取信息成功！","data"=>$data]);die;
            json_exit_lang(200,'info_get_success',$list);
        }else{
            json_exit_lang(401,'auth_error');
        }
    }
    function arraySort($array, $keys, $sort = SORT_DESC) {
        $keysValue = [];
        foreach ($array as $k => $v) {
            $keysValue[$k] = $v[$keys];
        }
        array_multisort($keysValue, $sort, $array);
        return $array;
    }
    public function getUserWithdrawList(){
        $id = base64_decode(Request::header('token'));
        $lang = Request::header('lang');
        $info = $this->where(['id'=>$id])->find();
        if($info){
            $WithdrawModel = new WithdrawModel;
            $list = $WithdrawModel->order('id','desc')->where(['mid'=>$info['id']])->select();
            foreach ($list as $k=>&$v){
                if($v['status'] === 1){
                    // 使用多语言系统替代tips表
                    $v['status_text'] = Lang::get('pending_review');//待审核
                    $v['isAdopt'] = false;
                }elseif($v['status'] === 2){
                    // 使用多语言系统替代tips表
                    $v['status_text'] = Lang::get('review_approved');//审核成功
                    $v['isAdopt'] = true;
                }elseif($v['status'] === 3){
                    // 使用多语言系统替代tips表
                    $v['status_text'] = Lang::get('review_rejected');//审核退回
                    $v['isAdopt'] = false;
                }elseif($v['status']===4){
                    // 使用多语言系统替代tips表
                    $v['status_text'] = Lang::get('transfer_success');//转账成功
                    $v['isAdopt'] = true;
                }elseif($v['status']===5){
                    // 使用多语言系统替代tips表
                    $v['status_text'] = Lang::get('transfer_failed');//转账失败
                    $v['isAdopt'] = true;
                }
                
                if($v['update_time'] == $v['create_time']){
                    // 使用多语言系统替代tips表
                    $v['update_time'] = Lang::get('pending_review');//待审核
                }else{
                    $v['update_time'] = date("Y-m-d H:i:s",$v['update_time']);
                }
                
                $v['create_time'] = date("Y-m-d H:i:s",$v['create_time']);
            }
            
            // 使用多语言系统替代tips表
            json_exit_lang(200,'info_get_success',$list);//获取信息成功
        }else{
            // 使用多语言系统替代tips表
            json_exit_lang(401,'auth_error');//鉴权错误
        }        
    }
    
    public function getUserRechargeList(){
        $id = base64_decode(Request::header('token'));
        $lang = Request::header('lang');
        $info = $this->where(['id'=>$id])->find();
        if($info){
            $RechargeModel = new RechargeModel;
            $list = $RechargeModel->order('id','desc')->where(['mid'=>$info['id']])->select();
            foreach ($list as $k=>&$v){
                if($v['status'] === 1){
                    // 使用多语言系统替代tips表
                    $v['status_text'] = Lang::get('pending_review');//待审核
                    $v['isAdopt'] = false;
                }elseif($v['status'] === 2){
                    // 使用多语言系统替代tips表
                    $v['status_text'] = Lang::get('review_approved');//审核成功
                    $v['isAdopt'] = true;
                }elseif($v['status'] === 3){
                    // 使用多语言系统替代tips表
                    $v['status_text'] = Lang::get('review_rejected');//审核退回
                    $v['isAdopt'] = false;
                }elseif($v['status']===4){
                    // 使用多语言系统替代tips表
                    $v['status_text'] = Lang::get('transfer_success');//转账成功
                    $v['isAdopt'] = true;
                }elseif($v['status']===5){
                    // 使用多语言系统替代tips表
                    $v['status_text'] = Lang::get('transfer_failed');//转账失败
                    $v['isAdopt'] = true;
                }
                
                if($v['update_time'] == $v['create_time']){
                    // 使用多语言系统替代tips表
                    $v['update_time'] = Lang::get('pending_review');//待审核
                }else{
                    $v['update_time'] = date("Y-m-d H:i:s",$v['update_time']);
                }
                
                $v['create_time'] = date("Y-m-d H:i:s",$v['create_time']);
            }
            // echo '<pre>';var_dump($list);die;
            json_exit_lang(200,'info_get_success',$list);//获取信息成功
        }else{
            json_exit_lang(401,'auth_error');//鉴权错误
        }
    }
    
     
    public function getUserWithdrawRole(){
        $id = base64_decode(Request::header('token'));
        $info = $this->where(['id'=>$id])->find();
        if($info){
            if($info['num'] && $info['min'] && $info['max'] ){
                $data = [
                    'num'=>$info['num'],
                    'min'=>$info['min'],
                    'max'=>$info['max'],
                    ];
            }else{
                $SystemModel = new SystemModel;
                $sys_info = $SystemModel->getConfig("base");
                $data = [
                    'num'=>$sys_info['withraw_num'],
                    'min'=>$sys_info['withraw_min'],
                    'max'=>$sys_info['withraw_max'],
                    ];
            }
            json_exit_lang(200,'info_get_success',$data);
        }else{
            json_exit_lang(401,'auth_error');
        }
    }
    public function getPersonalreport(){
        $id = base64_decode(Request::header('token'));
        $info = $this->where(['id'=>$id])->find();
        if($info){
            $GameModel = new GameModel;
            $WithdrawModel = new WithdrawModel;
            $RechargeModel = new RechargeModel;
            $win_money = $GameModel->where(['mid'=>$info['id'],'is_win'=>1])->sum('profit');
            $play_money = $GameModel->where(['mid'=>$info['id']])->sum('money');
            $Withdraw = $WithdrawModel->where(['mid'=>$info['id'],'status'=>2])->sum('money');
            $recharge = $RechargeModel->where(['mid'=>$info['id']])->sum('money');
            $data = [
                'win_money'=>$win_money,
                'play_money'=>$play_money,
                'withdrawal'=>$Withdraw,
                'recharge'=>$recharge
                ];
            json_exit_lang(200,'info_get_success',$data);
        }else{
            json_exit_lang(401,'auth_error');
        }
    }
    public function getUsesBankInfo(){
        $id = base64_decode(Request::header('token'));
        $info = $this->where(['id'=>$id])->find();
        if($info){
            $BankModel = new BankModel;
            $bankinfo = $BankModel->where(['uid'=>$info['id']])->find();
            if($bankinfo){
                $bankdata =[
                    'is_bank'=>true,
                    'info'=>$bankinfo
                    ];
            }else{
                $bankdata =[
                    'is_bank'=>false
                ];
            }
            json_exit_lang(200,'info_get_success',$bankdata);
        }else{
            json_exit_lang(401,'auth_error');
        }
    }    
    public function getUserInfo(){
        $id = base64_decode(Request::header('token'));
        $info = $this->where(['id'=>$id])->find();
        $sysModel = new SystemModel();
        $sys = $sysModel->getConfig("base");
        $seePriceInt = intval($sys['see_price']);
       if(intval($info['money']) >= $seePriceInt){
           $info['is_see']=1;
       }else{
           $info['is_see']=0;
       }

        if($info){
            // 安全处理sex字段，如果不存在则设置默认值
            $info['sex'] = isset($info['sex']) ? (string)$info['sex'] : '';
            if ($info['status'] != 1){
                $info['statusMessage'] = Lang::get('network_error');
            }

            // 添加VIP相关字段
            $this->addVipInfo($info);

            json_exit_lang(200,'info_get_success',$info);
        }else{
            json_exit_lang(401,'auth_error');
        }
    }

    /**
     * 添加VIP相关信息
     * @param array $info 用户信息数组（引用传递）
     */
    private function addVipInfo(&$info){
        // 如果是VIP0或空值，设置为非会员状态
        if (empty($info['vip']) || $info['vip'] === 'VIP0') {
            $info['vip'] = 'VIP0';
            $info['vip_open_time'] = '';
            $info['vip_remaining_days'] = 0;
            return;
        }

        // 检查是否有VIP开通时间字段，如果没有则使用注册时间作为默认值
        $vipOpenTime = isset($info['vip_open_time']) && $info['vip_open_time'] > 0
            ? $info['vip_open_time']
            : $info['create_time'];

        // VIP开通时间格式化为 YYYY-MM-DD
        $info['vip_open_time'] = date('Y-m-d', $vipOpenTime);

        // 计算VIP剩余天数
        // 如果数据库中有vip_end_time字段，使用该字段；否则基于VIP等级计算默认天数
        if (isset($info['vip_end_time']) && $info['vip_end_time'] > 0) {
            $vipEndTime = $info['vip_end_time'];
        } else {
            // 根据VIP等级设置默认天数（可根据业务需求调整）
            $vipDays = $this->getVipDaysByLevel($info['vip']);
            $vipEndTime = $vipOpenTime + ($vipDays * 24 * 60 * 60);
        }

        // 计算剩余天数
        $currentTime = time();
        $remainingSeconds = $vipEndTime - $currentTime;
        $remainingDays = max(0, ceil($remainingSeconds / (24 * 60 * 60)));

        $info['vip_remaining_days'] = (int)$remainingDays;
    }

    /**
     * 根据VIP等级获取默认天数
     * @param string $vipLevel VIP等级
     * @return int 天数
     */
    private function getVipDaysByLevel($vipLevel){
        // VIP0 或空值返回0天
        if (empty($vipLevel) || $vipLevel === 'VIP0') {
            return 0;
        }

        // 从系统配置中获取VIP配置
        $sysModel = new SystemModel();
        $vipConfig = $sysModel->getConfig("vip");

        // 如果配置存在且有对应等级的配置，使用配置的天数
        if (!empty($vipConfig) && isset($vipConfig[$vipLevel])) {
            return intval($vipConfig[$vipLevel]);
        }

        // 默认配置（兼容旧版本）
        $vipDaysMap = [
            'VIP0' => 0,   // 非会员
            'VIP1' => 30,
            'VIP2' => 60,
            'VIP3' => 90,
            'VIP4' => 180,
            'VIP5' => 365,
        ];

        return isset($vipDaysMap[$vipLevel]) ? $vipDaysMap[$vipLevel] : 0;
    }

    public function getUserIsOnline(){
        $id = base64_decode(Request::header('token'));
        $info = $this->where(['id'=>$id])->find();
        if($info){
            if ($info['is_online'] != 1){
                $info['onlineMessage'] = Lang::get('network_error');
            }
            json_exit_lang(200,'info_get_success',$info);
        }else{
            json_exit_lang(401,'auth_error');
        }
    }

    public function login($post){
        $lang = $post['lang'];
        $info = $this->where(['username'=>$post['username']])->find();
        if(empty($info)){
            json_exit_lang(401,'user_not_exists');//用户不存在
        }else{
            $black_ip=BlackIpModel::where('value',getIP())->value('id');

            if($black_ip>0){
                json_exit_lang(401,'account_disabled'); //账号被禁用
            }
            if($info['password'] != md5($post['password'])){
                json_exit_lang(401,'password_error');//密码错误
            }else{
                if($info['status'] != 1){
                    json_exit_lang(401,'account_disabled'); //账号被禁用
                }
                $data['last_time'] = time();
                $data['ip'] = getIP();
                $data['is_online'] = 1;
                $this->save($data,['id'=>$info['id']]);
                json_exit_lang(200,'login_success',$info);//登录成功
            }
        }
    }

    public function register($post){
       $lang = Request::post('lang');
       if (preg_match("/[\x7f-\xff]/", $post['username'])) {
            json_exit_lang(401,'username_chinese_error');//用户名不能存在中文
       }
       if(mb_strlen($post['username'],'UTF8') < 6 || mb_strlen($post['username'],'UTF8') > 12){
            json_exit_lang(401,'username_length_error');//用户名位数错误
       }
       if(mb_strlen($post['password'],'UTF8') < 6 || mb_strlen($post['password'],'UTF8') > 12){
            json_exit_lang(401,'password_length_error');//密码位数错误
       }
       if(empty($post['code'])){
          json_exit_lang(401,'invite_code_empty'); //邀请码不能为空
       }else{
           $UserModel = new UserModel;
           // 确保邀请码是数字类型
           $invite_code = intval($post['code']);
           if($invite_code == 0){
               json_exit_lang(401,'invite_code_error'); //邀请码格式错误
           }
           $code_info = $UserModel->where(['code'=>$invite_code])->find();
           if(empty($code_info)){
               json_exit_lang(401,'invite_code_error'); //邀请码不存在
           }
       }
        $post['username'] = trim($post['username']);
        $post['password'] = trim($post['password']);
       $info = $this->where(['username'=>$post['username']])->find();
       Db::startTrans();
       if(empty($info)){
            // 获取系统配置中的注册赠送彩金金额
            $SystemModel = new SystemModel;
            $baseConfig = $SystemModel->getConfig("base");
            $registerBonus = isset($baseConfig['register_bonus']) ? floatval($baseConfig['register_bonus']) : 0;

            $data['username'] = $post['username'];
            $data['password'] = md5($post['password']);
            $data['money'] = $registerBonus; // 设置注册赠送的彩金金额
            $data['status'] = 1;
            $data['uid'] = $code_info['id'];
            $data['header_img'] = "https://zxbuk.oss-cn-hongkong.aliyuncs.com/images/avatar/avatar".rand(1,185).".png";
            $data['ip'] = getIP();
            $data['create_time'] = time();
            $data['last_time'] = time();
            $data['update_time'] = time();
            // 设置默认为VIP0（非会员）
            $data['vip'] = 'VIP0';
            $data['vip_open_time'] = 0;
            $data['vip_end_time'] = 0;
            // 设置默认性别为0（未知）
            $data['sex'] = 0;

            if($this->insert($data)){
               $newinfo = $this->where(['username'=>$post['username']])->find();
                $Member_registerModel = new Member_registerModel;

                // 确保所有必需字段都有有效值
                $register_data = [
                    'mid' => intval($newinfo['id']),
                    'code' => intval($post['code']),
                    'uid' => intval($code_info['id']),
                    'ip' => getIP() ?: '127.0.0.1',
                    'create_time' => time(),
                    'update_time' => time()
                ];

                // 验证关键字段
                if(empty($register_data['mid']) || empty($register_data['uid'])) {
                    Db::rollback();
                    json_exit_lang(401,'register_failed');//注册失败
                }

                $status = $Member_registerModel->insert($register_data);
                if($status){
                    // 如果有注册赠送彩金，记录充值记录
                    if($registerBonus > 0){
                        $RechargeModel = new RechargeModel;
                        $recharge_data = [
                            'mid' => $newinfo['id'],
                            'order_no' => 'REG' . date('YmdHis') . rand(1000, 9999),
                            'money' => $registerBonus,
                            'status' => 2, // 已完成
                            'type' => 2, // 2=彩金
                            'desc' => '注册赠送',
                            'uid' => $code_info['id'], // 添加操作用户ID（推荐人）
                            'create_time' => time(),
                            'update_time' => time(),
                            'pay_time' => time(),
                        ];
                        $RechargeModel->save($recharge_data);
                    }

                     Db::commit();
                    json_exit_lang(200,'register_success',$newinfo['id']);//注册成功
                }else{
                    Db::rollback();
                    json_exit_lang(401,'register_failed');//注册失败
                }
                
             
                
            }else{
                Db::rollback();
                json_exit_lang(401,'register_failed');//注册失败
            }
       }else {
                Db::rollback();
            json_exit_lang(401,'username_exists');//用户已存在
       }
    }
}