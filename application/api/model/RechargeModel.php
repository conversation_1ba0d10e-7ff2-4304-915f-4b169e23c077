<?php
namespace app\api\model;

use think\Model;
use think\Db;
use think\facade\Session;
use think\facade\Request;
use think\facade\Cookie;
use app\api\model\MemberModel;
use app\common\Lang;
use Exception;

class RechargeModel extends Model
{
    // 设置当前模型对应的完整数据表名称
    protected $table = 'recharge';
    // 模型初始化
    protected static function init()
    {
        //TODO:初始化内容
    }
    
    public function recharge($post){
         $id = base64_decode(Request::header('token'));
         $info = (new MemberModel())->where(['id'=>$id])->find();
          try {
             $className = "\\app\\api\\pay\\" . $post['pay_way'];
             $pay = new $className();
          }catch (Exception $e) {
            exit();
          }
          if ($post['money'] <=0){
              json_exit_lang(401, 'amount_error');
          }
          $sn = 'R'.date('YmdHis').rand(10,99);
          $opData=[
              'amount'=>$post['money'],
              'sn'=>$sn,
              'uid'=>$id
          ];
          // 使用数据库事务确保并发安全
          Db::startTrans();
          try {
              $temp = [
                  'order_no'=>$sn,
                  'money'=>(string)$post['money'], // 转换为字符串，匹配mediumtext类型
                  'mid'=>$id,
                  'uid'=>0, // 设置操作用户ID，0表示用户自己操作
                  'type'=>1,
                  'desc'=>'EPUSDT充值', // 添加描述
                  'status'=>0,
                  'create_time'=>time(),
                  'update_time'=>time(),
                  'version'=>0 // 初始版本号
              ];
              $this->save($temp);

              // 提交事务
              Db::commit();
          } catch (Exception $e) {
              // 回滚事务
              Db::rollback();

              // 记录详细错误信息
              $errorMsg = "Recharge order creation failed: " . $e->getMessage();
              $errorMsg .= " | Data: " . json_encode($temp);
              $errorMsg .= " | File: " . $e->getFile() . " | Line: " . $e->getLine();
              error_log($errorMsg);

              // 在开发环境下显示详细错误（生产环境请移除）
              if (defined('APP_DEBUG') && APP_DEBUG) {
                  json_exit_lang(401, 'recharge_failed', ['debug' => $e->getMessage()]);
              } else {
                  json_exit_lang(401, 'recharge_failed');
              }
          }
          $resData = $pay->createPay($opData);
          if ($resData['respCode'] != 'SUCCESS') {
             json_exit_lang(401, 'recharge_failed');
          }
          $res=[
               'pay_url'=>$resData['payInfo']
          ];
         // 使用普通json_exit避免Base64编码
         $msg = \app\common\Lang::get('recharge_success');
         json_exit(200, $msg, $res);//充值成功
    }
}