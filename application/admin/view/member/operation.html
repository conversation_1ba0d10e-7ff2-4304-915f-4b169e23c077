{include file='public/meta' /}

<main class="layui-layout">
	<div class="container-fluid">
		<div class="row">
			<div class="col-md-12">
				<div class="card">
					<div class="card-header">
						<h4>
							{if $operation == 'add'}添加用户{else/}编辑用户{/if}
						</h4>
					</div>
					<div class="card-body">
						<form class="layui-form" id="loginfrom">
						    {if $operation == 'edit'}<input type="hidden" id="id" name="id" value="{:isset($info.id)?$info.id:''}">{/if}
							<div class="form-group">
                                <label>
                                    所属代理
                                </label>
                                <select id="uid" name="uid">
                                  <option value="">请选择</option>
                                  {foreach $class as $key=>$vo } 
                                      <option value="{$vo.id}" {if $operation == 'edit'}{if $vo.id == $info.uid} selected="" {/if}{/if}>{$vo.username}</option>
                                  {/foreach}                                
                                </select>
							</div>						    
							<div class="form-group">
								<label>
									用户账号
								</label>
								<input class="form-control" type="text" id="username" name="username"
								placeholder="请输入您的用户账号" {if $operation == 'edit'}  value="{:isset($info.username)?$info.username:''}" {/if}>
							</div>
							<div class="form-group">
								<label>
									用户密码
								</label>
								<input class="form-control" type="text" id="password" name="password"
								placeholder="请输入您的用户密码" >
							</div>	
							<div class="form-group">
								<label>
									提现密码
								</label>
								<input class="form-control" type="text" id="paypassword" name="paypassword"
								placeholder="请输入您的用户密码" >
							</div>								
							<div class="form-group">
								<label>
									用户余额
								</label>
								{if $userinfo.rid==25}
                                    	<input class="form-control" type="text" id="money" name="money"
								placeholder="请输入您的用户余额" {if $operation == 'edit'}  value="{:isset($info.money)?$info.money:''}" {/if}>
								{else}
									<input class="form-control" type="text" id="money" name="money"
								placeholder="" readonly {if $operation == 'edit'}  value="{:isset($info.money)?$info.money:''}" {/if}>
                                {/if}
							
							</div>
							<div class="form-group">
								<label>
									用户姓名（提现时用到）
								</label>
								<input class="form-control" type="text" id="name" name="name"
								placeholder="请输入您的用户姓名" {if $operation == 'edit'}  value="{:isset($info.name)?$info.name:''}" {/if}>
							</div>							
							<div class="form-group">
								<label>
									用户提现次数
								</label>
							
								{if $userinfo.rid==25}
                                    	<input class="form-control" type="text" id="num" name="num"
								placeholder="请输入您的用户提现次数" {if $operation == 'edit'}  value="{:isset($info.num)?$info.num:''}" {else}  value="3" {/if}>
								{else}
										<input class="form-control" type="text" id="num" name="num"
								placeholder="请输入您的用户提现次数" readonly="" {if $operation == 'edit'}  value="{:isset($info.num)?$info.num:''}" {else}  value="3" {/if}>
                                {/if}
							</div>
							
							<div class="form-group">
								<label>
									用户最少提现金额
								</label>
								<input class="form-control" type="text" id="min" name="min"
								placeholder="请输入您的最少提现金额" {if $operation == 'edit'}  value="{:isset($info.min)?$info.min:''}" {else}  value="50"   {/if}>
							</div>
							
							<div class="form-group">
								<label>
									用户最多提现金额
								</label>
								<input class="form-control" type="text" id="max" name="max"
								placeholder="请输入您的用户最多提现金额" {if $operation == 'edit'}  value="{:isset($info.max)?$info.max:''}" {else}  value="100"  {/if}>
							</div>
								<div class="form-group">
								<label>
									会员等级
								</label>
								<select class="form-control" id="vip" name="vip">
									{foreach $vipConfig as $level => $days}
										<option value="{$level}"
											{if $operation == 'edit'}
												{if isset($info.vip) && $info.vip == $level} selected {/if}
											{else}
												{if $level == 'VIP0'} selected {/if}
											{/if}>
											{if $level == 'VIP0'}
												{$level} (非会员)
											{else}
												{$level} ({$days}天)
											{/if}
										</option>
									{/foreach}
								</select>
								<small class="help-block">选择会员等级，VIP0为非会员状态，其他等级括号内为对应的VIP有效天数</small>
							</div>

							<div class="form-group">
								<label>
									VIP开通时间
								</label>
								<input class="form-control" type="date" id="vip_open_date" name="vip_open_date"
								{if $operation == 'edit' && isset($info.vip_open_time) && $info.vip_open_time > 0}
									value="{:date('Y-m-d', $info.vip_open_time)}"
								{else}
									value="{:date('Y-m-d')}"
								{/if}>
								<small class="help-block">VIP开通日期，结束时间将根据选择的VIP等级自动计算</small>
							</div>

	                        <div class="form-group">
								<label>
									会员信用分
								</label>
								<input class="form-control" type="text" id="score" name="score"
								placeholder="请输入会员信用分" {if $operation == 'edit'}  value="{:isset($info.score)?$info.score:''}" {else}  value="100"  {/if}>
							</div>
                            <div class="form-group">
                                <label>
                                    头像
                                </label>
                                <div class="form-group">
                                    <div class="layui-upload-drag" id="ico">
                                      <i class="layui-icon"></i>
                                      <p>点击上传，或将文件拖拽到此处</p>
                                      <div {if $operation == 'add'}class="layui-hide"{/if} id="uploadDemoView">
                                        <hr>
                                        <input type="hidden" id="icoinput" name="header_img" value="{:isset($info.header_img)?$info.header_img:''}">
                                        <img src="{:isset($info.header_img)?$info.header_img:''}" alt="头像" style="max-width: 130px;border-style: solid;width: 125px;height: 125px;border-radius: 50%;">
                                      </div>
                                    </div>
                                </div>
                            </div>
							<div class="form-group">
								<button class="layui-btn" type="button" lay-submit lay-filter="save">
								    {if $operation == 'add'}提交{else/}更新{/if}
								</button>
							</div>
						</form>
					</div>
				</div>
			</div>
		</div>
	</div>
</main>
{include file='public/onlyfooter'/}
<script>

layui.use(['form','upload', 'element'], function(){
  var layer = layui.layer
  ,form = layui.form  
  ,upload = layui.upload
  ,element = layui.element
  ,layer = layui.layer;
  //拖拽上传
  upload.render({
    elem: '#ico'
    ,url: "{:url('doupload')}" //此处用的是第三方的 http 请求演示，实际使用时改成您自己的上传接口即可。
    ,before: function(obj){
      layer.msg('上传中', {icon: 16, time: 0});
    }
    ,done: function(res){
      $("#icoinput").val(res.data);
      layui.$('#uploadDemoView').removeClass('layui-hide').find('img').attr('src', res.data);
      layer.msg('上传成功');
    }
  }); 
   //监听提交
  form.on('submit(save)', function(data){
        var uid = $('#uid').val();
        var username = $('#username').val();
        var password = $('#password').val();
        if(uid == "" || uid == null || uid == undefined){
            layer.msg("请选择所属代理");
            return false;
        } 
        if(username == "" || username == null || username == undefined){
            layer.msg("请输入用户账号");
            return false;
        }

        // 新增时验证密码
        var operation = "{$operation}";
        if(operation == 'add' && (password == "" || password == null || password == undefined)){
            layer.msg("请输入用户密码");
            return false;
        }
         
        var loading = layer.load(0, {shade: false});
        $.ajax({
            type: 'post',
            url: "{:url('doSave')}",
            data:$("#loginfrom").serialize(),
            dataType:"json",
            success: function(data) {
                if(data.code === 200){
                    layer.msg(data.msg, {
                      icon: 1,
                      time: 1500 //2秒关闭（如果不配置，默认是3秒）
                    }, function(){
                        layer.closeAll();
                        window.parent.location.reload();
                        var index = parent.layer.getFrameIndex(window.name);
                        parent.layer.close(index);
                        
                    });                      
                }else{
                     layer.closeAll();
                     layer.msg(data.msg);
                     return false;
                }  
            },
            error: function (XMLHttpRequest, textStatus, errorThrown) {
                layer.closeAll();
                if (textStatus == "timeout") {
                    layer.msg('请求超时！', {
                      icon: 2,
                      time: 1500 //2秒关闭（如果不配置，默认是3秒）
                    }, function(){
                        return false;
                    });                      
                } else {
                    layer.msg('服务器错误！', {
                      icon: 2,
                      time: 1500 //2秒关闭（如果不配置，默认是3秒）
                    }, function(){
                        return false;
                    });                         
                }
            },                    
        }); 
    });
});

</script>