{include file='public/meta' /}

<main class="layui-layout">
	<div class="container-fluid">
		<div class="row">
			<div class="col-md-12">
				<div class="card">
					<div class="card-header">
						<h4>
							{if $operation == 'add'}添加广告{else/}编辑广告{/if}
						</h4>
					</div>
					<div class="card-body">
						<form class="layui-form" id="loginfrom">
						    {if $operation == 'edit'}<input type="hidden" id="id" name="id" value="{:isset($info.id)?$info.id:''}">{/if}
							<div class="form-group">
								<label>
									广告名称
								</label>
								<input class="form-control" type="text" id="name" name="name"
								placeholder="请输入您的分类名称" {if $operation == 'edit'}  value="{:isset($info.name)?$info.name:''}" {/if}>
							</div>
                            <div class="form-group">
                                <label>
                                    图标
                                </label>
                                <div class="form-group">
                                    <div class="layui-upload-drag" id="ico">
                                      <i class="layui-icon"></i>
                                      <p>点击上传，或将文件拖拽到此处</p>
                                      <div {if $operation == 'add'}class="layui-hide"{/if} id="uploadDemoView">
                                        <hr>
                                        <input type="hidden" id="icoinput" name="url" value="{:isset($info.url)?$info.url:''}">
                                        <img src="{:isset($info.url)?$info.url:''}" alt="图标" style="border-style: solid;height: 180px;border-radius: 10px;">
                                      </div>
                                    </div>  
                                </div>
                            </div>   
							<div class="form-group">
								<button class="layui-btn" type="button" lay-submit lay-filter="save">
								    {if $operation == 'add'}提交{else/}更新{/if}	
								</button>
							</div>
						</form>
					</div>
				</div>
			</div>
		</div>
	</div>
</main>
{include file='public/onlyfooter'/}
<script>

layui.use(['form','upload'], function(){
  var layer = layui.layer
  ,upload = layui.upload
  ,form = layui.form;
  //拖拽上传
  upload.render({
    elem: '#ico'
    ,url: "{:url('doupload')}" //此处用的是第三方的 http 请求演示，实际使用时改成您自己的上传接口即可。
    ,before: function(obj){
      layer.msg('上传中', {icon: 16, time: 0});
    }
    ,done: function(res){
      $("#icoinput").val(res.data);
      layui.$('#uploadDemoView').removeClass('layui-hide').find('img').attr('src', res.data);
      layer.msg('上传成功');
    }
  });   
   //监听提交
  form.on('submit(save)', function(data){
        var name = $('#name').val();
        var icoinput = $('#icoinput').val();
        if(name == "" || name == null || name == undefined){
            layer.msg("请输入广告名称");
            return false;
        }   
        if(icoinput == "" || icoinput == null || icoinput == undefined){
            layer.msg("请上传图片");
            return false;
        }          
        var loading = layer.load(0, {shade: false});
        $.ajax({
            type: 'post',
            url: "{:url('doSave')}",
            data:$("#loginfrom").serialize(),
            dataType:"json",
            success: function(data) {
                if(data.code === 200){
                    layer.msg(data.msg, {
                      icon: 1,
                      time: 1500 //2秒关闭（如果不配置，默认是3秒）
                    }, function(){
                        layer.closeAll();
                        window.parent.location.reload();
                        var index = parent.layer.getFrameIndex(window.name);
                        parent.layer.close(index);
                        
                    });                      
                }else{
                     layer.closeAll();
                     layer.msg(data.msg);
                     return false;
                }  
            },
            error: function (XMLHttpRequest, textStatus, errorThrown) {
                layer.closeAll();
                if (textStatus == "timeout") {
                    layer.msg('请求超时！', {
                      icon: 2,
                      time: 1500 //2秒关闭（如果不配置，默认是3秒）
                    }, function(){
                        return false;
                    });                      
                } else {
                    layer.msg('服务器错误！', {
                      icon: 2,
                      time: 1500 //2秒关闭（如果不配置，默认是3秒）
                    }, function(){
                        return false;
                    });                         
                }
            },                    
        }); 
    });
});

</script>