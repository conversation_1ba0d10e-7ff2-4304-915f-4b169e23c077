{include file='public/meta' /}
{include file='public/menu' /}
{include file='public/head' /}

<!--页面主要内容-->
<main class="layout-content">
<div class="container-fluid">
<div class="row">
      <div class="col-lg-12">
          <div class="card">
            <div class="card-header"><h4>投注记录</h4></div>
            <div class="card-body">
                <form class="layui-form" action="">
                    <div class="layui-form-item">
                        <label class="layui-form-label">彩种</label>
                        <div class="layui-input-inline">
                            <select name="cid">
                                <option value="">请选择</option>
                                {foreach $typeList as $key=>$vo }
                                <option value="{$vo.id}">{$vo.name}</option>
                                {/foreach}
                            </select>
                        </div>
                        <label class="layui-form-label">玩家用户名</label>
                        <div class="layui-input-inline">
                            <input type="text" name="username" placeholder="请输入玩家用户名"  autocomplete="off" class="layui-input">
                        </div>
                        <div class="layui-inline" id="time">
                            <label class="layui-form-label">起始时间</label>
                            <div class="layui-input-inline">
                                <input name="start_time" type="text" autocomplete="off" id="startDate" class="layui-input" placeholder="开始日期">
                            </div>
                            <label class="layui-form-label">结束时间</label>
                            <div class="layui-input-inline">
                                <input name="end_time" type="text" autocomplete="off" id="endDate" class="layui-input" placeholder="结束日期">
                            </div>  
                        </div> 
                    </div>
                    <div class="layui-form-item">
                        <div class="layui-inline">
                            <label class="layui-form-label"></label>
                            <div class="layui-input-inline">
                                <input class="layui-btn layui-btn-search" type="button" lay-submit="" lay-filter="submitSearch" value="检索">
                                <button type="reset" class="layui-btn layui-btn-primary layui-border-orange">重置</button>
                            </div>
                        </div>
                    </div>                    
                </form>
           </div>
          </div>
      </div>
      
      <div class="col-lg-12">
        <div class="card">
          <div class="card-body">
            <table style="height: 125px;" border="1" width="640" cellspacing="0" cellpadding="2" class="layui-hide" id="table" lay-filter="table"></table>
          </div>
        </div>
      </div>
</div>

</div>

</main>
{include file='public/footer'/}
<!--End 页面主要内容-->
<script type="text/html" id="toolbar">
  <div class="layui-btn-container">
    <!--<button class="layui-btn layui-btn-sm" lay-event="getCheckData">获取选中行数据</button>
    <button class="layui-btn layui-btn-sm" lay-event="getCheckLength">获取选中数目</button>
    <button class="layui-btn layui-btn-sm" lay-event="isAll">验证是否全选</button>-->
    <button type="button" class="layui-btn layui-btn-sm" lay-event="refresh"><i class="layui-icon">&#xe669;</i>刷新</button>
  </div>
</script>
<script>
    layui.use(['table','element','form','layer','laydate'], function(){
        var $ = layui.$, 
        table = layui.table, 
        form = layui.form, 
        layer = layui.layer, 
        laydate = layui.laydate;
        laydate.render({
            elem: '#time'
            //设置开始日期、日期日期的 input 选择器
            //数组格式为 2.6.6 开始新增，之前版本直接配置 true 或任意分割字符即可
            ,range: ['#startDate', '#endDate']
        });
        function table_reload(field) {
            layer.msg('请稍候！', { icon: 16 , shade: 0.01, time: 2000000});
            table.reload('table', {
                url: "{:url('list')}",
                where: {
                    // cid: field.cid,
                    username: field.username,
                    cid: field.cid,
                    key: field.key,
                    start_time: field.start_time,
                    end_time: field.end_time
                }
            });
    
            layer.close(layer.index);
        }
        form.on('submit(submitSearch)', function(data){
            table_reload(data.field);
        });
        form.on('switch(state)', function(data){
          var state = data.elem.checked ?1:0;
          var id = data.elem.attributes['dataid'].nodeValue;
          var index = layer.load(0, {shade: false});
            $.ajax({
                type: 'post',
                url: "{:url('doEditState')}",
                data:{id:id,state:state},
                dataType:"json",
                success: function(data) {
                    if(data.code === 200){
                        layer.msg(data.msg, {
                          icon: 1,
                          time: 1000 //2秒关闭（如果不配置，默认是3秒）
                        }, function(){
                            layer.closeAll();
                            table.reload('table');
                        });                      
                    }else{
                        layer.msg(data.msg, {
                          icon: 1,
                          time: 1000 //2秒关闭（如果不配置，默认是3秒）
                        }, function(){
                            layer.closeAll();
                            table.reload('table');
                        }); 
                    }  
                },
                error: function (XMLHttpRequest, textStatus, errorThrown) {
                    layer.closeAll();
                    if (textStatus == "timeout") {
                        layer.msg('请求超时！', {
                          icon: 2,
                          time: 1500 //2秒关闭（如果不配置，默认是3秒）
                        }, function(){
                        });                      
                    } else {
                        layer.msg('服务器错误！', {
                          icon: 2,
                          time: 1500 //2秒关闭（如果不配置，默认是3秒）
                        }, function(){
                        });                         
                    }
                },                    
            });                  
        });         
        table.render({
            elem: '#table',
            url: "{:url('list')}"
            ,toolbar: '#toolbar' // 开启头部工具栏，并为其绑定左侧模板
            ,defaultToolbar: ['filter', 'exports', 'print']
            ,title: '彩票列表'
            ,totalRow: true
            ,cols: [[
                {type: 'checkbox', fixed: 'left',totalRowText: '合计:'},
                {field:'id', title:'ID', fixed: 'left', unresize: true, sort: true,width:100},
                {field:'mid', title:'用户', sort: true},
                {field:'lid', title:'彩种', sort: true},
                {field:'expect', title:'期号', sort: true},
                {field:'type', title:'玩法', sort: true, edit: 'text'},
                {field:'money', title:'金额', sort: true, totalRow: true, edit: 'text'},
                {field:'peilv', title:'赔率', sort: true, edit: 'text'},
                {field:'profit', title:'盈亏', totalRow: true, sort: true,templet:function(d){
                    if(d.is_win == 0){
                        return '<div style="color:red">未结算</div>';
                       
                    }else if(d.is_win == 1){
                         return '<div style="color:green">'+"+"+d.profit+'</div>';
                    }else if(d.is_win == 2){
                         return '<div style="color:red">'+"-"+d.profit+'</div>';
                    }
                }},
                {field:'before_betting', title:'投注前金额', sort: true, edit: 'text'},
                {field:'after_betting', title:'投注后金额', sort: true, edit: 'text'},
                // {field:'before_kj', title:'开奖前金额', sort: true, edit: 'text'},
                // {field:'after_kj', title:'开奖后金额', sort: true, edit: 'text'},
                {field:'status', title:'状态', sort: true,templet:function(d){
                    if(d.status === 1){
                        return '<div style="color:green">已结算</div>';
                    }else{
                        return  '<div style="color:red">未结算</div>';
                    }
                    
                }},
                {field:'create_time', title:'下注时间', sort: true},
                {field:'update_time', title:'结算时间', sort: true,templet:function(d){
                    if(d.status === 1){
                        return '<div style="color:green">'+d.update_time+'</div>';
                    }else{
                        return '<div style="color:red">未结算</div>';
                    }
                }},
            ]]
           //主要利用回调渲染
            ,done: function(res, curr, count){
            	//然后把值写上去td[data-field="itemNo" 定义在哪个列后显示
            	console.log(res.data);
            	let money = 0.00;
            	for (var i  =  0 ;i <  res.data.length;i++)
                { 
                    if(res.data[i].is_win  === 2){
                        money -= parseFloat(res.data[i].profit);
                    }else if(res.data[i].is_win  === 1){
                        money += parseFloat(res.data[i].profit);
                    }
                    
                }
                var profit = this.elem.next().find('.layui-table-total td[data-field="profit"] .layui-table-cell');
                if(money <= 0){
                    profit.css("color", "red");
                }else{
                    profit.css("color", "green");
                }
                profit.text(money.toFixed(2));
            }           
            ,page: true
        });
        //监听单元格编辑
          table.on('edit(table)', function(obj){
                var value = obj.value //得到修改后的值
                ,data = obj.data //得到所在行所有键值
                ,field = obj.field; //得到字段
                layer.confirm('确认修改投注数据？', function(index){
                    $.post("{:url('editBuyLottery')}"+"?id="+data.id,data,function(res){
                        if(res.code === 200){
                           layer.msg(res.msg, {
                              icon: 1,
                              time: 2000 //2秒关闭（如果不配置，默认是3秒）
                            }, function(){
                                layer.closeAll();
                                table.reload('table');
                            });
                        }else{
                           layer.msg(res.msg, {
                              icon: 2,
                              time: 2000 //2秒关闭（如果不配置，默认是3秒）
                            }, function(){
                                layer.closeAll();
                                table.reload('table');
                            });
                        }
                    });
                });
          });
        //头工具栏事件
        table.on('toolbar(table)', function(obj){
            var checkStatus = table.checkStatus(obj.config.id);
            switch(obj.event){
                case 'getCheckData':
                    var data = checkStatus.data;
                    layer.alert(JSON.stringify(data));
                    break;
            case 'getCheckLength':
                var data = checkStatus.data;
                layer.msg('选中了：'+ data.length + ' 个');
                break;
            case 'isAll':
                layer.msg(checkStatus.isAll ? '全选': '未全选');
                break;
            case 'refresh':
                table.reload('table')       
                break;
        };
  });
  
  //监听行工具事件
  table.on('tool(table)', function(obj){
    var data = obj.data;
    if(obj.event === 'del'){
      layer.confirm('确认删除数据？', function(index){
        $.ajax({
            type: 'post',
            url: "{:url('doDel')}",
            data:{id:data.id},
            dataType:"json",
            success: function(data) {
                if(data.code === 200){
                    layer.msg(data.msg, {
                      icon: 1,
                      time: 1000 //2秒关闭（如果不配置，默认是3秒）
                    }, function(){
                        obj.del();
                        layer.close(index);
                        table.reload('table');
                    });                      
                }else{
                     layer.msg(data.msg);
                }  
            },
            error: function (XMLHttpRequest, textStatus, errorThrown) {
                layer.closeAll();
                if (textStatus == "timeout") {
                    layer.msg('请求超时！', {
                      icon: 2,
                      time: 1500 //2秒关闭（如果不配置，默认是3秒）
                    }, function(){
                    });                      
                } else {
                    layer.msg('服务器错误！', {
                      icon: 2,
                      time: 1500 //2秒关闭（如果不配置，默认是3秒）
                    }, function(){
                    });                         
                }
            },                    
        });        
      });
    }
  });
});
</script>

