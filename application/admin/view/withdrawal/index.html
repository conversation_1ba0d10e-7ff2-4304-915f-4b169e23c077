{include file='public/meta' /}
{include file='public/menu' /}
{include file='public/head' /}

<!--页面主要内容-->
<main class="layout-content">
<div class="container-fluid">
<div class="row">
      <div class="col-lg-12">
          <div class="card">
            <div class="card-header"><h4>提现管理</h4></div>
            <div class="card-body">
                <form class="layui-form" action="">
                    <div class="layui-form-item">
                        <label class="layui-form-label">用户名</label>
                        <div class="layui-input-inline">
                          <input type="text" name="username" placeholder="请输入关键字搜索"  autocomplete="off" class="layui-input">
                        </div>
                        <label class="layui-form-label">收款人姓名</label>
                        <div class="layui-input-inline">
                            <input type="text" name="bankname" placeholder="请输入关键字搜索"  autocomplete="off" class="layui-input">
                        </div>
                        <div class="layui-inline" id="time">
                            <label class="layui-form-label">起始时间</label>
                            <div class="layui-input-inline">
                                <input name="start_time" type="text" autocomplete="off" id="startDate" class="layui-input" placeholder="开始日期">
                            </div>
                            <label class="layui-form-label">结束时间</label>
                            <div class="layui-input-inline">
                                <input name="end_time" type="text" autocomplete="off" id="endDate" class="layui-input" placeholder="结束日期">
                            </div>  
                        </div> 
                    </div>
                    <div class="layui-form-item">
                        <div class="layui-inline">
                            <label class="layui-form-label"></label>
                            <div class="layui-input-inline">
                                <input class="layui-btn layui-btn-search" type="button" lay-submit="" lay-filter="submitSearch" value="检索">
                                <button type="reset" class="layui-btn layui-btn-primary layui-border-orange">重置</button>
                            </div>
                        </div>
                    </div>                    
                </form>
           </div>
          </div>
      </div>
      
      <div class="col-lg-12">
        <div class="card">
          <div class="card-body">
            <table style="height: 125px;" border="1" width="640" cellspacing="0" cellpadding="2" class="layui-hide" id="table" lay-filter="table"></table>
          </div>
        </div>
      </div>
</div>

</div>

</main>
{include file='public/footer'/}
<!--End 页面主要内容-->
<script type="text/html" id="toolbar">
  <div class="layui-btn-container">
    <!--<button class="layui-btn layui-btn-sm" lay-event="getCheckData">获取选中行数据</button>
    <button class="layui-btn layui-btn-sm" lay-event="getCheckLength">获取选中数目</button>
    <button class="layui-btn layui-btn-sm" lay-event="isAll">验证是否全选</button>-->
    <button type="button" class="layui-btn layui-btn-sm" lay-event="refresh"><i class="layui-icon">&#xe669;</i>刷新</button>
  </div>
</script>
<script type="text/html" id="bar">
{{# if(d.status == 1){ }}
  <a class="layui-btn layui-btn-xs" lay-event="accept">通过</a>
  <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="refuse">退回</a>
 {{# } }}
</script>
<script>
    layui.use(['table','element','form','layer','laydate'], function(){
        var $ = layui.$, 
        table = layui.table, 
        form = layui.form, 
        layer = layui.layer, 
        laydate = layui.laydate;
        laydate.render({
            elem: '#time'
            //设置开始日期、日期日期的 input 选择器
            //数组格式为 2.6.6 开始新增，之前版本直接配置 true 或任意分割字符即可
            ,range: ['#startDate', '#endDate']
        });
        function table_reload(field) {
            layer.msg('请稍候！', { icon: 16 , shade: 0.01, time: 2000000});
            table.reload('table', {
                url: "{:url('list')}",
                where: {
                    username: field.username,
                    bankname: field.bankname,
                    start_time: field.start_time,
                    end_time: field.end_time
                }
            });
    
            layer.close(layer.index);
        }
        form.on('submit(submitSearch)', function(data){
            table_reload(data.field);
        });       
        table.render({
            elem: '#table',
            url: "{:url('list')}"
            ,toolbar: '#toolbar' // 开启头部工具栏，并为其绑定左侧模板
            ,defaultToolbar: ['filter', 'exports', 'print']
            ,title: '提现列表'
            ,cols: [[
                {type: 'checkbox', fixed: 'left'},
                {field:'id', title:'ID', fixed: 'left', unresize: true, sort: true,width:70},
                {field:'order_no', title:'订单号', width:170},
                {field:'username', title:'用户'},
                {field:'name', title:'真实姓名'},
                {field:'bankid', title:'银行卡号'},
                {field:'bankinfo', title:'收款银行'},
                {field:'money', title:'金额', sort: true},
                // {field:'desc', title:'描述', sort: true},
                {field:'status', title:'状态', sort: true,templet:function(d){
                    if(d.status === 1){
                        return '<div style="color:red">待处理</div>';
                    }else if(d.status === 2){
                        return  '<div style="color:green">审核通过</div>';
                    }else if(d.status === 3){
                        return  '<div style="color:red">审核拒绝</div>';
                    }else if(d.status === 4){
                        return  '<div style="color:green">转账成功</div>';
                    }
                }},
                {field:'create_time', title:'提交时间', sort: true,width:170},
                {field:'pay_time', title:'转帐时间', sort: true,width:170},
                <?php if($userinfo['rid']==25) { ?>
                {fixed: 'right', title:'操作', fixed: 'right', unresize: true,width:120, toolbar: '#bar'}
                <?php } ?>
            ]]
            ,page: true
        });
  //监听行工具事件
  table.on('tool(table)', function(obj){
    var data = obj.data;
    if(obj.event === 'del'){
      layer.confirm('确认删除数据？', function(index){
        $.ajax({
            type: 'post',
            url: "{:url('doDel')}",
            data:{id:data.id},
            dataType:"json",
            success: function(data) {
                if(data.code === 200){
                    layer.msg(data.msg, {
                      icon: 1,
                      time: 1000 //2秒关闭（如果不配置，默认是3秒）
                    }, function(){
                        obj.del();
                        layer.close(index);
                        table.reload('table');
                    });                      
                }else{
                     layer.msg(data.msg);
                }  
            },
            error: function (XMLHttpRequest, textStatus, errorThrown) {
                layer.closeAll();
                if (textStatus == "timeout") {
                    layer.msg('请求超时！', {
                      icon: 2,
                      time: 1500 //2秒关闭（如果不配置，默认是3秒）
                    }, function(){
                    });                      
                } else {
                    layer.msg('服务器错误！', {
                      icon: 2,
                      time: 1500 //2秒关闭（如果不配置，默认是3秒）
                    }, function(){
                    });                         
                }
            },                    
        });        
      });
    }else if(obj.event === 'accept'){
          layer.prompt({title: '请输入描述，并确认', formType: 2}, function(text, index){
            layer.close(index);
            $.post("{:url('acceptWithdrawal')}"+"?id="+data.id,{desc:text,status:2},function(res){
                if(res.code === 200){
                   layer.msg(res.msg, {
                      icon: 1,
                      time: 2000 //2秒关闭（如果不配置，默认是3秒）
                    }, function(){
                        layer.closeAll();
                        table.reload('table');
                    });
                }else{
                   layer.msg(res.msg, {
                      icon: 2,
                      time: 2000 //2秒关闭（如果不配置，默认是3秒）
                    }, function(){
                        layer.closeAll();
                        table.reload('table');
                    });
                }
            });
          });
        
    }else if(obj.event === 'refuse'){
          layer.prompt({title: '请输入描述，并确认', formType: 2}, function(text, index){
            layer.close(index);
            $.post("{:url('acceptWithdrawal')}"+"?id="+data.id,{desc:text,status:3},function(res){
                if(res.code === 200){
                   layer.msg(res.msg, {
                      icon: 1,
                      time: 2000 //2秒关闭（如果不配置，默认是3秒）
                    }, function(){
                        layer.closeAll();
                        table.reload('table');
                    });
                }else{
                   layer.msg(res.msg, {
                      icon: 2,
                      time: 2000 //2秒关闭（如果不配置，默认是3秒）
                    }, function(){
                        layer.closeAll();
                        table.reload('table');
                    });
                }
            });
          });
        
    }
  });
});
</script>

