{include file='public/meta' /}
<style type="text/css" media="all">
* {
    box-sizing: ;
}
</style>
<main class="layui-layout">
	<div class="container-fluid">
		<div class="row">
			<div class="col-md-12">
				<div class="card">
					<div class="card-header">
						<h4>
							{if $operation == 'add'}添加管理组{else/}编辑管理组{/if}
						</h4>
					</div>
					<div class="card-body">
						<form class="layui-form" id="loginfrom">
						    {if $operation == 'edit'}<input type="hidden" id="id" name="id" value="{:isset($info.id)?$info.id:''}">{/if}
							<div class="form-group">
								<label>
									管理组名称
								</label>
								<input class="form-control" type="text" id="name" name="name"
								placeholder="请输入您的管理组名称" {if $operation == 'edit'}  value="{:isset($info.name)?$info.name:''}" {/if}>
							</div>
							<div class="form-group">
								<label>
									权限树
								</label>
								<div id="role"></div>
							</div>
							<div class="form-group">
								<button class="layui-btn" type="button" lay-submit lay-filter="save">
								    {if $operation == 'add'}提交{else/}更新{/if}	
								</button>
							</div>
						</form>
					</div>
				</div>
			</div>
		</div>
	</div>
</main>
{include file='public/onlyfooter'/}
<script>

layui.use(['tree', 'util','form'], function(){
  var tree = layui.tree
  ,layer = layui.layer
  ,util = layui.util
  ,form = layui.form;
  {if $operation == 'add'}
      $.get("{:url('role/roleTree')}",function (res) {
          //基本演示
          tree.render({
            elem: '#role'
            ,data: res.data
            ,showCheckbox: true  //是否显示复选框
            ,id: 'role'
            ,isJump: true //是否允许点击节点时弹出新窗口跳转
            ,click: function(obj){
              var data = obj.data;  //获取当前点击的节点数据
            }
         });
        },'json');
    {else/}
      $.get("{:url('role/userRoleTree')}",{id:"{:isset($info.id)?$info.id:''}"},function (res) {
          //基本演示
          tree.render({
            elem: '#role'
            ,data: res.data
            ,showCheckbox: true  //是否显示复选框
            ,id: 'role'
            ,click: function(obj){
              var data = obj.data;  //获取当前点击的节点数据
            }
         });
        },'json');
    {/if}	
   //监听提交
  form.on('submit(save)', function(data){
        var roleData = tree.getChecked('role'); //获取选中节点的数据
        var name = $('#name').val();
        {if $operation == 'add'}
            var id = null;
        {else/}
            var id = $('#id').val();
        {/if}
        if(name == "" || name == null || name == undefined){
            layer.msg("请输入管理组名称");
            return false;
        }   
        if(roleData == "" || roleData == null || roleData == undefined){
            layer.msg("请选择权限树");
            return false;
        }    
        var loading = layer.load(0, {shade: false});
        $.ajax({
            type: 'post',
            url: "{:url('doSave')}",
            data:{name:name,role:roleData,id:id},
            dataType:"json",
            success: function(data) {
                if(data.code === 200){
                    layer.msg(data.msg, {
                      icon: 1,
                      time: 1500 //2秒关闭（如果不配置，默认是3秒）
                    }, function(){
                        layer.closeAll();
                        window.parent.location.reload();
                        var index = parent.layer.getFrameIndex(window.name);
                        parent.layer.close(index);
                        
                    });                      
                }else{
                     layer.msg(data.msg);
                     return false;
                }  
            },
            error: function (XMLHttpRequest, textStatus, errorThrown) {
                layer.closeAll();
                if (textStatus == "timeout") {
                    layer.msg('请求超时！', {
                      icon: 2,
                      time: 1500 //2秒关闭（如果不配置，默认是3秒）
                    }, function(){
                        return false;
                    });                      
                } else {
                    layer.msg('服务器错误！', {
                      icon: 2,
                      time: 1500 //2秒关闭（如果不配置，默认是3秒）
                    }, function(){
                        return false;
                    });                         
                }
            },                    
        }); 
        
  
    });

});

</script>