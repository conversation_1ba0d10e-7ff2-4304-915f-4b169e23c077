{include file='public/meta' /}
{include file='public/menu' /}
{include file='public/head' /}
    <main class="layout-content">
      <div class="container-fluid">
        <div class="row">
          <div class="col-lg-12">
            <div class="card">
                <div class="card-header">
                    <h4>文件上传演示</h4>
                    <small class="help-block">支持图片、视频、文档等多种文件类型，自动适配OSS和本地存储</small>
                </div>
                <div class="card-body">
            
            <!-- 图片上传 -->
            <div class="layui-card">
                <div class="layui-card-header">图片上传</div>
                <div class="layui-card-body">
                    <div class="layui-upload">
                        <button type="button" class="layui-btn" id="imageUpload">选择图片</button>
                        <div class="layui-upload-list">
                            <img class="layui-upload-img" id="imagePreview" style="width: 200px; height: 200px; object-fit: cover; display: none;">
                            <p id="imageText"></p>
                        </div>
                    </div>
                    <input type="hidden" id="imageUrl" name="image_url">
                </div>
            </div>
            
            <!-- 视频上传 -->
            <div class="layui-card">
                <div class="layui-card-header">视频上传</div>
                <div class="layui-card-body">
                    <div class="layui-upload">
                        <button type="button" class="layui-btn layui-btn-normal" id="videoUpload">选择视频</button>
                        <div class="layui-upload-list">
                            <video id="videoPreview" controls style="width: 400px; height: 300px; display: none;"></video>
                            <p id="videoText"></p>
                            <p id="videoInfo"></p>
                        </div>
                    </div>
                    <input type="hidden" id="videoUrl" name="video_url">
                </div>
            </div>
            
            <!-- 文档上传 -->
            <div class="layui-card">
                <div class="layui-card-header">文档上传</div>
                <div class="layui-card-body">
                    <div class="layui-upload">
                        <button type="button" class="layui-btn layui-btn-warm" id="documentUpload">选择文档</button>
                        <div class="layui-upload-list">
                            <div id="documentList"></div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 批量上传 -->
            <div class="layui-card">
                <div class="layui-card-header">批量上传</div>
                <div class="layui-card-body">
                    <div class="layui-upload">
                        <button type="button" class="layui-btn layui-btn-danger" id="batchUpload">批量选择</button>
                        <div class="layui-upload-list">
                            <div id="batchList"></div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 拖拽上传 -->
            <div class="layui-card">
                <div class="layui-card-header">拖拽上传</div>
                <div class="layui-card-body">
                    <div class="layui-upload-drag" id="dragUpload">
                        <i class="layui-icon"></i>
                        <p>点击上传，或将文件拖拽到此处</p>
                        <div class="layui-hide" id="uploadDemoView">
                            <hr>
                            <img src="" alt="上传成功后渲染" style="max-width: 196px">
                        </div>
                    </div>
                </div>
            </div>
            
        </div>
    </div>
</div>

<script>
layui.use(['upload', 'layer'], function(){
    var upload = layui.upload;
    var layer = layui.layer;
    
    // 格式化文件大小
    function formatBytes(bytes, decimals = 2) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const dm = decimals < 0 ? 0 : decimals;
        const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
    }
    
    // 图片上传
    upload.render({
        elem: '#imageUpload',
        url: '{:url("Upload/image")}',
        accept: 'images',
        acceptMime: 'image/*',
        size: 10240, // 10MB
        before: function(obj){
            obj.preview(function(index, file, result){
                $('#imagePreview').attr('src', result).show();
            });
            layer.msg('上传中...', {icon: 16, time: 0});
        },
        done: function(res){
            layer.closeAll('loading');
            if(res.code === 200){
                $('#imageUrl').val(res.data);
                $('#imageText').html('<span style="color: #5FB878;">上传成功</span>');
                layer.msg('图片上传成功');
            } else {
                $('#imageText').html('<span style="color: #FF5722;">上传失败</span>');
                layer.msg(res.msg);
            }
        },
        error: function(){
            layer.closeAll('loading');
            $('#imageText').html('<span style="color: #FF5722;">上传失败</span>');
            layer.msg('上传失败');
        }
    });
    
    // 视频上传
    upload.render({
        elem: '#videoUpload',
        url: '{:url("Upload/video")}',
        accept: 'video',
        acceptMime: 'video/*',
        size: 2147483648, // 2GB
        before: function(obj){
            layer.msg('视频上传中，请稍候...', {icon: 16, time: 0});
        },
        done: function(res){
            layer.closeAll('loading');
            if(res.code === 200){
                $('#videoUrl').val(res.data.video_url);
                $('#videoPreview').attr('src', res.data.video_url).show();
                
                var info = '文件大小: ' + formatBytes(res.data.size);
                if (res.data.duration) {
                    info += ' | 时长: ' + res.data.duration + '秒';
                }
                $('#videoInfo').text(info);
                $('#videoText').html('<span style="color: #5FB878;">上传成功</span>');
                layer.msg('视频上传成功');
            } else {
                $('#videoText').html('<span style="color: #FF5722;">上传失败</span>');
                layer.msg(res.msg);
            }
        },
        error: function(){
            layer.closeAll('loading');
            $('#videoText').html('<span style="color: #FF5722;">上传失败</span>');
            layer.msg('视频上传失败');
        }
    });
    
    // 文档上传
    upload.render({
        elem: '#documentUpload',
        url: '{:url("Upload/document")}',
        accept: 'file',
        acceptMime: '.pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt',
        size: 20480, // 20MB
        before: function(obj){
            layer.msg('文档上传中...', {icon: 16, time: 0});
        },
        done: function(res){
            layer.closeAll('loading');
            if(res.code === 200){
                var html = '<div class="layui-elem-quote">';
                html += '<a href="' + res.data + '" target="_blank">查看文档</a>';
                html += '<span style="color: #5FB878; margin-left: 10px;">上传成功</span>';
                html += '</div>';
                $('#documentList').html(html);
                layer.msg('文档上传成功');
            } else {
                $('#documentList').html('<span style="color: #FF5722;">上传失败: ' + res.msg + '</span>');
                layer.msg(res.msg);
            }
        },
        error: function(){
            layer.closeAll('loading');
            $('#documentList').html('<span style="color: #FF5722;">上传失败</span>');
            layer.msg('文档上传失败');
        }
    });
    
    // 批量上传
    upload.render({
        elem: '#batchUpload',
        url: '{:url("Upload/batch")}',
        multiple: true,
        before: function(obj){
            layer.msg('批量上传中...', {icon: 16, time: 0});
        },
        done: function(res){
            layer.closeAll('loading');
            if(res.code === 200){
                var html = '<div class="layui-elem-quote">';
                html += '<p>成功: ' + res.data.success_count + ' 个文件</p>';
                html += '<p>失败: ' + res.data.error_count + ' 个文件</p>';
                if (res.data.results.length > 0) {
                    html += '<div>成功的文件:</div>';
                    res.data.results.forEach(function(item) {
                        html += '<p><a href="' + item.url + '" target="_blank">文件 ' + (item.index + 1) + '</a></p>';
                    });
                }
                html += '</div>';
                $('#batchList').html(html);
                layer.msg('批量上传完成');
            } else {
                layer.msg(res.msg);
            }
        }
    });
    
    // 拖拽上传
    upload.render({
        elem: '#dragUpload',
        url: '{:url("Upload/image")}',
        accept: 'images',
        acceptMime: 'image/*',
        size: 10240,
        done: function(res){
            if(res.code === 200){
                $('#uploadDemoView').removeClass('layui-hide').find('img').attr('src', res.data);
                layer.msg('上传成功');
            } else {
                layer.msg(res.msg);
            }
        }
    });
});
</script>

<style>
.card {
    margin-bottom: 20px;
}
.layui-upload-img {
    border: 1px solid #e6e6e6;
    border-radius: 2px;
}
.layui-upload-drag {
    height: 200px;
}
</style>

                </div>
            </div>
          </div>
        </div>
      </div>
    </main>
{include file='public/footer' /}
