{include file='public/meta' /}

<main class="layui-layout">
	<div class="container-fluid">
		<div class="row">
			<div class="col-md-12">
				<div class="card">
					<div class="card-header">
						<h4>
							{if $operation == 'add'}添加银行{else/}编辑银行{/if}
						</h4>
					</div>
					<div class="card-body">
						<form class="layui-form" id="loginfrom">
						    {if $operation == 'edit'}
							<input type="hidden" id="id" name="id" value="{:isset($info.id)?$info.id:''}">

							{/if}
					
							<div class="form-group">
								<label>
									银行名称
								</label>
								<input class="form-control" type="text" id="title" name="title"
									   placeholder="请输入您的银行卡用户名" {if $operation == 'edit'}  value="{:isset($info.title)?$info.title:''}" {/if}>
							</div>

                            <div class="form-group">
							<label>
								请输入您的银行信息
							</label>
                            <div class="form-group">
                                <div class="layui-upload-drag" id="ico">
                                  <i class="layui-icon"></i>
                                  <p>点击上传，或将文件拖拽到此处</p>
                                  <div {if !isset($info.thumb)}class="layui-hide"{/if} id="uploadDemoView">
                                    <hr>
                                    <input type="hidden" id="icoinput" name="thumb" value="{:isset($info.thumb)?$info.thumb:''}">
                                    <img src="{:isset($info.thumb)?$info.thumb:''}" alt="图标" style="max-width: 130px;height: 30%;">
                                  </div>
                                </div>  
                            </div>	
						</div>
						<div class="form-group">
								<button class="layui-btn" type="button" lay-submit lay-filter="save">
								    {if $operation == 'add'}提交{else/}更新{/if}	
								</button>
							</div>
						</form>
					</div>
				</div>
			</div>
		</div>
	</div>
</main>
{include file='public/onlyfooter'/}
<script>

layui.use(['form','upload', 'element'], function(){
 var layer = layui.layer
  ,form = layui.form  
  ,upload = layui.upload
  ,element = layui.element
  ,layer = layui.layer;
  //拖拽上传
  upload.render({
    elem: '#ico'
    ,url: "{:url('doupload')}" //此处用的是第三方的 http 请求演示，实际使用时改成您自己的上传接口即可。
    ,before: function(obj){
      layer.msg('上传中', {icon: 16, time: 0});
    }
    ,done: function(res){
      $("#icoinput").val(res.data);
      layui.$('#uploadDemoView').removeClass('layui-hide').find('img').attr('src', res.data);
      layer.msg('上传成功');
    }
  }); 
   //监听提交
  form.on('submit(save)', function(data){
        var title = $('#title').val();
        var thumb = $('#icoinput').val();
        if(title == "" || title == null || title == undefined){
            layer.msg("请输入银行名称");
            return false;
        }   
        if(thumb == "" || thumb == null || thumb == undefined){
            layer.msg("请输入银行信息");
            return false;
        }          
        var loading = layer.load(0, {shade: false});
        $.ajax({
            type: 'post',
            url: "{:url('doSave')}",
            data:$("#loginfrom").serialize(),
            dataType:"json",
            success: function(data) {
                if(data.code === 200){
                    layer.msg(data.msg, {
                      icon: 1,
                      time: 1500 //2秒关闭（如果不配置，默认是3秒）
                    }, function(){
                        layer.closeAll();
                        window.parent.location.reload();
                        var index = parent.layer.getFrameIndex(window.name);
                        parent.layer.close(index);
                        
                    });                      
                }else{
                     layer.closeAll();
                     layer.msg(data.msg);
                     return false;
                }  
            },
            error: function (XMLHttpRequest, textStatus, errorThrown) {
                layer.closeAll();
                if (textStatus == "timeout") {
                    layer.msg('请求超时！', {
                      icon: 2,
                      time: 1500 //2秒关闭（如果不配置，默认是3秒）
                    }, function(){
                        return false;
                    });                      
                } else {
                    layer.msg('服务器错误！', {
                      icon: 2,
                      time: 1500 //2秒关闭（如果不配置，默认是3秒）
                    }, function(){
                        return false;
                    });                         
                }
            },                    
        }); 
    });


	form.on('select(uid)', function (data) {   //选择移交单位 赋值给input框
		var select_text = data.elem[data.elem.selectedIndex].text;
		$("#HandoverCompany").val(select_text );
		$("#uid").next().find("dl").css({ "display": "none" });
		form.render();
	});


	$('#HandoverCompany').bind('input propertychange', function () {
		var value = $("#HandoverCompany").val();
		$("#uid").val(value);
		form.render();
		$("#uid").next().find("dl").css({ "display": "block" });
		var dl = $("#uid").next().find("dl").children();
		var j = -1;
		for (var i = 0; i < dl.length; i++) {
			if (dl[i].innerHTML.indexOf(value) <= -1) {
				dl[i].style.display = "none";
				j++;
			}
			if (j == dl.length-1) {
				$("#uid").next().find("dl").css({ "display": "none" });
			}
		}

	})



});


$('#HandoverCompany').on('input propertychange',function (){
	if($(this).val() !== ''){
		console.log($(this).val());
	}
})

</script>