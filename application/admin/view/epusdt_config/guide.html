{include file='public/header'/}
<main class="lyear-layout-content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-lg-12">
                <div class="card">
                    <div class="card-header">
                        <h4>EPUSDT 配置指南</h4>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-warning">
                            <h5><i class="fa fa-exclamation-triangle"></i> 重要提示</h5>
                            <p>请按照以下步骤正确配置 EPUSDT 支付系统，确保支付功能正常运行。</p>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header bg-primary text-white">
                                        <h5>步骤 1: EPUSDT 后台配置</h5>
                                    </div>
                                    <div class="card-body">
                                        <ol>
                                            <li><strong>登录 EPUSDT 后台</strong>
                                                <br>访问您的 EPUSDT 管理后台
                                            </li>
                                            <li><strong>添加钱包地址</strong>
                                                <br>在"收款地址管理"中添加您的 USDT 钱包地址
                                            </li>
                                            <li><strong>生成 API 密钥</strong>
                                                <br>在"系统设置"中生成 API 密钥
                                            </li>
                                            <li><strong>配置回调地址</strong>
                                                <br>设置回调地址为：
                                                <code>{:url('/api/callback/pay', ['gateway' => 'Epusdt'], true, true)}</code>
                                            </li>
                                        </ol>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header bg-success text-white">
                                        <h5>步骤 2: 本系统配置</h5>
                                    </div>
                                    <div class="card-body">
                                        <ol>
                                            <li><strong>填写 API 信息</strong>
                                                <br>在配置页面填写 EPUSDT 的 API 地址和密钥
                                            </li>
                                            <li><strong>配置收款地址</strong>
                                                <br>添加与 EPUSDT 后台相同的钱包地址
                                            </li>
                                            <li><strong>设置充值金额</strong>
                                                <br>配置最小和最大充值金额
                                            </li>
                                            <li><strong>启用支付</strong>
                                                <br>选择"启用"EPUSDT 支付功能
                                            </li>
                                        </ol>
                                        <div class="mt-3">
                                            <a href="{:url('index')}" class="btn btn-primary">
                                                <i class="fa fa-cog"></i> 前往配置页面
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="card mt-4">
                            <div class="card-header bg-info text-white">
                                <h5>支持的 USDT 地址格式</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-4">
                                        <h6>Bitcoin 网络 (Omni)</h6>
                                        <p class="text-muted">以 1 或 3 开头</p>
                                        <code>**********************************</code>
                                    </div>
                                    <div class="col-md-4">
                                        <h6>Ethereum 网络 (ERC-20)</h6>
                                        <p class="text-muted">以 0x 开头</p>
                                        <code>0x742d35Cc6634C0532925a3b8D4C9db96</code>
                                    </div>
                                    <div class="col-md-4">
                                        <h6>Tron 网络 (TRC-20)</h6>
                                        <p class="text-muted">以 T 开头</p>
                                        <code>TLPpXqSYwxPHnVbYQTYPk1KVWQQQzQzQzQ</code>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="card mt-4">
                            <div class="card-header bg-warning text-white">
                                <h5>常见问题</h5>
                            </div>
                            <div class="card-body">
                                <div class="accordion" id="faqAccordion">
                                    <div class="card">
                                        <div class="card-header" id="faq1">
                                            <h6 class="mb-0">
                                                <button class="btn btn-link" type="button" data-toggle="collapse" data-target="#collapse1">
                                                    Q: 测试连接失败怎么办？
                                                </button>
                                            </h6>
                                        </div>
                                        <div id="collapse1" class="collapse" data-parent="#faqAccordion">
                                            <div class="card-body">
                                                <p>1. 检查 EPUSDT 系统是否正常运行</p>
                                                <p>2. 确认 API 地址格式正确（不要以/结尾）</p>
                                                <p>3. 验证 API 密钥是否正确</p>
                                                <p>4. 检查网络连接和防火墙设置</p>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="card">
                                        <div class="card-header" id="faq2">
                                            <h6 class="mb-0">
                                                <button class="btn btn-link" type="button" data-toggle="collapse" data-target="#collapse2">
                                                    Q: 支付成功但余额没有增加？
                                                </button>
                                            </h6>
                                        </div>
                                        <div id="collapse2" class="collapse" data-parent="#faqAccordion">
                                            <div class="card-body">
                                                <p>1. 检查回调地址是否正确配置</p>
                                                <p>2. 查看 EPUSDT 后台的回调日志</p>
                                                <p>3. 检查本系统的回调处理逻辑</p>
                                                <p>4. 确认订单状态是否正确更新</p>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="card">
                                        <div class="card-header" id="faq3">
                                            <h6 class="mb-0">
                                                <button class="btn btn-link" type="button" data-toggle="collapse" data-target="#collapse3">
                                                    Q: 如何添加多个收款地址？
                                                </button>
                                            </h6>
                                        </div>
                                        <div id="collapse3" class="collapse" data-parent="#faqAccordion">
                                            <div class="card-body">
                                                <p>1. 在配置页面点击"添加收款地址"</p>
                                                <p>2. 输入有效的 USDT 钱包地址</p>
                                                <p>3. 在 EPUSDT 后台添加相同的地址</p>
                                                <p>4. 系统会自动轮询使用这些地址</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>
{include file='public/footer'/}
