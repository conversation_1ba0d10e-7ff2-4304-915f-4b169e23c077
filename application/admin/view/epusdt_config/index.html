{include file='public/meta' /}
{include file='public/menu' /}
{include file='public/head' /}

<main class="layout-content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-lg-12">
                <div class="card">
                    <div class="card-header">
                        <h4>EPUSDT支付配置</h4>
                        <div class="card-header-actions">
                            <a href="{:url('guide')}" class="btn btn-sm btn-info" target="_blank">
                                <i class="fa fa-question-circle"></i> 配置指南
                            </a>
                        </div>
                    </div>
                    <div class="card-body">
                        <form class="layui-form" id="epusdtForm">
                            <div class="form-group">
                                <label>启用EPUSDT支付</label>
                                <div>
                                    <label class="radio-inline">
                                        <input type="radio" name="enabled" value="1"
                                            {if isset($epusdtConfig.enabled) && $epusdtConfig.enabled == 1}checked{/if}> 启用
                                    </label>
                                    <label class="radio-inline">
                                        <input type="radio" name="enabled" value="0"
                                            {if !isset($epusdtConfig.enabled) || $epusdtConfig.enabled == 0}checked{/if}> 禁用
                                    </label>
                                </div>
                                <small class="help-block">是否启用EPUSDT支付功能</small>
                            </div>

                            <div class="form-group">
                                <label>EPUSDT API地址</label>
                                <input class="form-control" type="text" id="api_url" name="api_url"
                                    placeholder="请输入EPUSDT API地址，如：https://pay.jsdao.cc" 
                                    value="{:isset($epusdtConfig.api_url)?$epusdtConfig.api_url:'https://pay.jsdao.cc'}">
                                <small class="help-block">EPUSDT系统的API地址，不要以/结尾</small>
                            </div>

                            <div class="form-group">
                                <label>API密钥</label>
                                <input class="form-control" type="text" id="api_key" name="api_key"
                                    placeholder="请输入EPUSDT API密钥"
                                    value="{:isset($epusdtConfig.api_key)?$epusdtConfig.api_key:''}">
                                <small class="help-block">在EPUSDT后台生成的API密钥</small>
                            </div>

                            <div class="form-group">
                                <label>收款地址管理</label>
                                <div id="walletAddresses">
                                    {if isset($epusdtConfig.wallet_addresses) && !empty($epusdtConfig.wallet_addresses)}
                                        {foreach $epusdtConfig.wallet_addresses as $key => $address}
                                        <div class="wallet-address-item" style="margin-bottom: 10px;">
                                            <div class="input-group">
                                                <input type="text" class="form-control" name="wallet_addresses[]"
                                                    placeholder="请输入USDT钱包地址" value="{$address}">
                                                <div class="input-group-append">
                                                    <button class="btn btn-danger remove-address" type="button">删除</button>
                                                </div>
                                            </div>
                                        </div>
                                        {/foreach}
                                    {else}
                                        <div class="wallet-address-item" style="margin-bottom: 10px;">
                                            <div class="input-group">
                                                <input type="text" class="form-control" name="wallet_addresses[]"
                                                    placeholder="请输入USDT钱包地址" value="">
                                                <div class="input-group-append">
                                                    <button class="btn btn-danger remove-address" type="button">删除</button>
                                                </div>
                                            </div>
                                        </div>
                                    {/if}
                                </div>
                                <button type="button" class="btn btn-success" id="addAddress">添加收款地址</button>
                                <button type="button" class="btn btn-info" id="validateAddresses">验证地址格式</button>
                                <small class="help-block">配置USDT收款地址，支持多个地址轮询使用。支持Bitcoin、Ethereum、Tron格式的USDT地址</small>
                            </div>

                            <div class="form-group">
                                <label>最小充值金额</label>
                                <input class="form-control" type="number" id="min_amount" name="min_amount"
                                    placeholder="请输入最小充值金额" step="0.01" min="0.01"
                                    value="{:isset($epusdtConfig.min_amount)?$epusdtConfig.min_amount:'10'}">
                                <small class="help-block">用户单次充值的最小金额（USDT）</small>
                            </div>

                            <div class="form-group">
                                <label>最大充值金额</label>
                                <input class="form-control" type="number" id="max_amount" name="max_amount"
                                    placeholder="请输入最大充值金额" step="0.01" min="0.01"
                                    value="{:isset($epusdtConfig.max_amount)?$epusdtConfig.max_amount:'10000'}">
                                <small class="help-block">用户单次充值的最大金额（USDT）</small>
                            </div>

                            <div class="form-group">
                                <button class="layui-btn" type="button" id="saveConfig">
                                    保存配置
                                </button>
                                <button class="layui-btn layui-btn-normal" type="button" id="testConnection">
                                    测试连接
                                </button>
                                <button class="layui-btn layui-btn-warm" type="button" id="syncAddresses">
                                    同步到数据库
                                </button>
                                <button class="layui-btn layui-btn-danger" type="button" id="checkEndpoints">
                                    检查API端点
                                </button>
                                <button class="layui-btn" type="button" id="check502Error" style="background-color: #ff5722; color: white;">
                                    检查502错误
                                </button>
                            </div>
                        </form>

                        <div class="alert alert-info mt-3">
                            <h5>配置说明：</h5>
                            <ul>
                                <li>1. 确保EPUSDT系统已正确部署并运行</li>
                                <li>2. API地址格式：https://your-domain.com（不要以/结尾）</li>
                                <li>3. API密钥在EPUSDT后台的"系统设置"中生成</li>
                                <li>4. 收款地址：配置USDT钱包地址，支持Bitcoin、Ethereum、Tron格式</li>
                                <li>5. 回调地址会自动配置为：{:url('/api/callback/pay', ['gateway' => 'Epusdt'], true, true)}</li>
                                <li>6. 建议先使用"验证地址格式"检查地址是否正确</li>
                                <li>7. 使用"测试连接"功能验证API配置是否正确</li>
                                <li>8. 配置完成后需要在EPUSDT后台手动添加相同的钱包地址</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>

{include file='public/footer'/}

<script>
$(document).ready(function(){

    // 添加收款地址
    $('#addAddress').click(function(){
        var newAddressHtml = '<div class="wallet-address-item" style="margin-bottom: 10px;">' +
            '<div class="input-group">' +
            '<input type="text" class="form-control" name="wallet_addresses[]" placeholder="请输入USDT钱包地址" value="">' +
            '<div class="input-group-append">' +
            '<button class="btn btn-danger remove-address" type="button">删除</button>' +
            '</div>' +
            '</div>' +
            '</div>';
        $('#walletAddresses').append(newAddressHtml);
    });

    // 删除收款地址
    $(document).on('click', '.remove-address', function(){
        if ($('.wallet-address-item').length > 1) {
            var $item = $(this).closest('.wallet-address-item');
            var address = $item.find('input[name="wallet_addresses[]"]').val().trim();

            if (address && confirm('确定要删除这个收款地址吗？\n\n地址：' + address + '\n\n此操作将同时从数据库中删除该地址。')) {
                // 调用后端删除接口
                $.ajax({
                    type: 'post',
                    url: "{:url('deleteWalletAddress')}",
                    data: {address: address},
                    dataType: "json",
                    success: function(data) {
                        if(data.code === 200){
                            $item.remove();
                            alert('地址删除成功');
                        } else {
                            alert(data.msg || '删除失败');
                        }
                    },
                    error: function (XMLHttpRequest, textStatus, errorThrown) {
                        alert('删除失败，请稍后重试');
                    }
                });
            } else if (!address) {
                // 如果是空地址，直接删除
                $item.remove();
            }
        } else {
            alert('至少需要保留一个收款地址');
        }
    });

    // 验证收款地址格式
    $('#validateAddresses').click(function(){
        $.ajax({
            type: 'post',
            url: "{:url('validateWalletAddresses')}",
            dataType: "json",
            success: function(data) {
                if(data.code === 200){
                    alert(data.msg);
                } else {
                    alert(data.msg);
                }
            },
            error: function (XMLHttpRequest, textStatus, errorThrown) {
                alert('验证失败！');
            }
        });
    });

    // 保存配置
    $('#saveConfig').click(function(){
        // 验证收款地址
        var addresses = [];
        $('input[name="wallet_addresses[]"]').each(function(){
            var addr = $(this).val().trim();
            if (addr) {
                addresses.push(addr);
            }
        });

        if (addresses.length === 0) {
            alert('请至少配置一个收款地址');
            return;
        }

        var formData = $("#epusdtForm").serialize();

        $.ajax({
            type: 'post',
            url: "{:url('doSave')}",
            data: formData,
            dataType: "json",
            success: function(data) {
                if(data.code === 200){
                    alert(data.msg);
                    window.location.reload();
                } else {
                    alert(data.msg);
                }
            },
            error: function (XMLHttpRequest, textStatus, errorThrown) {
                if (textStatus == "timeout") {
                    alert('请求超时！');
                } else {
                    alert('服务器错误！');
                }
            }
        });
    });

    // 测试连接
    $('#testConnection').click(function(){
        var api_url = $('#api_url').val();
        var api_key = $('#api_key').val();

        if (!api_url || !api_key) {
            alert('请先填写API地址和密钥');
            return;
        }

        $.ajax({
            type: 'post',
            url: "{:url('testConnection')}",
            data: {
                api_url: api_url,
                api_key: api_key
            },
            dataType: "json",
            success: function(data) {
                if(data.code === 200){
                    alert(data.msg);
                } else {
                    alert(data.msg);
                }
            },
            error: function (XMLHttpRequest, textStatus, errorThrown) {
                alert('测试连接失败！');
            }
        });
    });

    // 同步收款地址到数据库
    $('#syncAddresses').click(function(){
        if(confirm('确定要将收款地址同步到数据库吗？\n\n此操作将直接在wallet_address表中添加或更新收款地址')){
            $.ajax({
                type: 'post',
                url: "{:url('syncWalletAddresses')}",
                dataType: "json",
                success: function(data) {
                    if(data.code === 200){
                        alert(data.msg);
                    } else {
                        alert(data.msg);
                    }
                },
                error: function (XMLHttpRequest, textStatus, errorThrown) {
                    alert('同步失败！');
                }
            });
        }
    });

    // 检查API端点
    $('#checkEndpoints').click(function(){
        $.ajax({
            type: 'post',
            url: "{:url('checkApiEndpoints')}",
            dataType: "json",
            success: function(data) {
                if(data.code === 200){
                    var message = 'API端点检查结果:\n\n';
                    if(data.data && data.data.length > 0) {
                        data.data.forEach(function(item) {
                            message += item.endpoint + ': ';
                            message += 'HTTP ' + item.http_code + ' - ';
                            message += item.message + '\n';
                        });
                    }
                    alert(message);
                } else {
                    alert(data.msg);
                }
            },
            error: function (XMLHttpRequest, textStatus, errorThrown) {
                alert('检查失败！');
            }
        });
    });

    // 检查502错误
    $('#check502Error').click(function(){
        $(this).text('检查中...').prop('disabled', true);

        $.ajax({
            type: 'post',
            url: "{:url('check502Error')}",
            dataType: "json",
            success: function(data) {
                if(data.code === 200){
                    var message = '502错误诊断结果:\n\n';

                    // 显示诊断结果
                    if(data.data.diagnosis && data.data.diagnosis.length > 0) {
                        message += '诊断结论:\n';
                        data.data.diagnosis.forEach(function(item) {
                            message += '• ' + item + '\n';
                        });
                        message += '\n';
                    }

                    // 显示详细检查结果
                    if(data.data.results && data.data.results.length > 0) {
                        message += '详细检查结果:\n';
                        data.data.results.forEach(function(item) {
                            var status_icon = '';
                            if(item.severity === 'critical') status_icon = '🔴';
                            else if(item.severity === 'error') status_icon = '❌';
                            else if(item.severity === 'warning') status_icon = '⚠️';
                            else status_icon = '✅';

                            message += status_icon + ' ' + item.endpoint + ' (' + item.method + ')\n';
                            message += '   HTTP ' + item.http_code + ' - ' + item.message + '\n';
                            message += '   响应时间: ' + item.response_time + '\n\n';
                        });
                    }

                    // 如果有502错误，提供解决方案
                    if(data.data.has_502_error) {
                        message += '\n🔧 解决方案:\n';
                        message += '1. 联系EPUSDT技术支持报告502错误\n';
                        message += '2. 临时使用我们的状态查询页面:\n';
                        message += '   ' + data.data.status_checker_url + '\n';
                        message += '3. 引导用户手动刷新或稍后重试\n';
                    }

                    alert(message);
                } else {
                    alert(data.msg || '检查失败');
                }
            },
            error: function (XMLHttpRequest, textStatus, errorThrown) {
                alert('检查失败：' + (textStatus === 'timeout' ? '请求超时' : '网络错误'));
            },
            complete: function() {
                $('#check502Error').text('检查502错误').prop('disabled', false);
            }
        });
    });
});
</script>
