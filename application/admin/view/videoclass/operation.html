{include file='public/meta' /}

<main class="layui-layout">
	<div class="container-fluid">
		<div class="row">
			<div class="col-md-12">
				<div class="card">
					<div class="card-header">
						<h4>
							{if $operation == 'add'}添加视频分类{else/}编辑视频分类{/if}
						</h4>
					</div>
					<div class="card-body">
						<form class="layui-form" id="loginfrom">
						    {if $operation == 'edit'}<input type="hidden" id="id" name="id" value="{:isset($info.id)?$info.id:''}">{/if}
					        <div class="form-group">
								<label>
									分类名称(中文)
								</label>
								<input class="form-control" type="text" id="zh_cn" name="zh_cn"
								placeholder="请输入您的分类名称" {if $operation == 'edit'}  value="{:isset($info.zh_cn)?$info.zh_cn:''}" {/if}>
							</div>
							<!--		<div class="form-group">
								<label>
									分类名称(英文)
								</label>
								<input class="form-control" type="text" id="en_us" name="en_us"
								placeholder="请输入您的英文分类名称" {if $operation == 'edit'}  value="{:isset($info.en_us)?$info.en_us:''}" {/if}>
							</div>
							<div class="form-group">
								<label>
									分类名称(西班牙语)
								</label>
								<input class="form-control" type="text" id="es_spa" name="es_spa"
								placeholder="请输入您的西班牙语分类名称" {if $operation == 'edit'}  value="{:isset($info.es_spa)?$info.es_spa:''}" {/if}>
							</div>
							<div class="form-group">
								<label>
									分类名称(马来语)
								</label>
								<input class="form-control" type="text" id="ms_my" name="ms_my"
								placeholder="请输入您的马来语分类名称" {if $operation == 'edit'}  value="{:isset($info.ms_my)?$info.ms_my:''}" {/if}>
							</div>
								<div class="form-group">
								<label>
									分类名称(越南语)
								</label>
								<input class="form-control" type="text" id="name" name="name"
								placeholder="请输入您的越南语分类名称" {if $operation == 'edit'}  value="{:isset($info.yn_yu)?$info.yn_yu:''}" {/if}>
							</div>-->
							<div class="form-group">
								<button class="layui-btn" type="button" lay-submit lay-filter="save">
								    {if $operation == 'add'}提交{else/}更新{/if}	
								</button>
							</div>
						</form>
					</div>
				</div>
			</div>
		</div>
	</div>
</main>
{include file='public/onlyfooter'/}
<script>

layui.use(['form'], function(){
  var layer = layui.layer
  ,form = layui.form;
   //监听提交
  form.on('submit(save)', function(data){
        var name = $('#zh_cn').val();
        if(name == "" || name == null || name == undefined){
            layer.msg("请输入视频分类名称");
            return false;
        }   
        var loading = layer.load(0, {shade: false});
        $.ajax({
            type: 'post',
            url: "{:url('doSave')}",
            data:$("#loginfrom").serialize(),
            dataType:"json",
            success: function(data) {
                if(data.code === 200){
                    layer.msg(data.msg, {
                      icon: 1,
                      time: 1500 //2秒关闭（如果不配置，默认是3秒）
                    }, function(){
                        layer.closeAll();
                        window.parent.location.reload();
                        var index = parent.layer.getFrameIndex(window.name);
                        parent.layer.close(index);
                        
                    });                      
                }else{
                     layer.msg(data.msg);
                     return false;
                }  
            },
            error: function (XMLHttpRequest, textStatus, errorThrown) {
                layer.closeAll();
                if (textStatus == "timeout") {
                    layer.msg('请求超时！', {
                      icon: 2,
                      time: 1500 //2秒关闭（如果不配置，默认是3秒）
                    }, function(){
                        return false;
                    });                      
                } else {
                    layer.msg('服务器错误！', {
                      icon: 2,
                      time: 1500 //2秒关闭（如果不配置，默认是3秒）
                    }, function(){
                        return false;
                    });                         
                }
            },                    
        }); 
    });
});

</script>