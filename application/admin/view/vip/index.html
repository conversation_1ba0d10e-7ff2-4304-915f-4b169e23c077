{include file='public/meta' /}
{include file='public/menu' /}
{include file='public/head' /}
    <main class="layout-content">
      <div class="container-fluid">
        <div class="row">
          <div class="col-lg-12">
            <div class="card">
				<div class="card-header">
					<h4>
						VIP配置管理
					</h4>
					<small class="help-block">配置不同VIP等级对应的天数，用于计算VIP剩余天数</small>
				</div>
				<div class="card-body">
					<form class="layui-form" id="vipForm">
						<div class="row">
							<div class="col-md-6">
								<div class="form-group">
									<label>VIP0 天数 <span class="text-muted">(非会员)</span></label>
									<input class="form-control" type="number" name="VIP0" value="0" readonly
										style="background-color: #f5f5f5;">
									<small class="help-block">VIP0为非会员状态，固定为0天</small>
								</div>
							</div>
							<div class="col-md-6">
								<div class="form-group">
									<label>VIP1 天数</label>
									<input class="form-control" type="number" name="VIP1" min="1"
										placeholder="请输入VIP1对应的天数"
										value="{:isset($vipConfig.VIP1)?$vipConfig.VIP1:'30'}">
									<small class="help-block">VIP1等级对应的有效天数</small>
								</div>
							</div>
						</div>

						<div class="row">
							<div class="col-md-6">
								<div class="form-group">
									<label>VIP2 天数</label>
									<input class="form-control" type="number" name="VIP2" min="1"
										placeholder="请输入VIP2对应的天数"
										value="{:isset($vipConfig.VIP2)?$vipConfig.VIP2:'60'}">
									<small class="help-block">VIP2等级对应的有效天数</small>
								</div>
							</div>
							<div class="col-md-6">
								<div class="form-group">
									<label>VIP3 天数</label>
									<input class="form-control" type="number" name="VIP3" min="1"
										placeholder="请输入VIP3对应的天数"
										value="{:isset($vipConfig.VIP3)?$vipConfig.VIP3:'90'}">
									<small class="help-block">VIP3等级对应的有效天数</small>
								</div>
							</div>
						</div>
						
						<div class="row">
							<div class="col-md-6">
								<div class="form-group">
									<label>VIP4 天数</label>
									<input class="form-control" type="number" name="VIP4" min="1"
										placeholder="请输入VIP4对应的天数"
										value="{:isset($vipConfig.VIP4)?$vipConfig.VIP4:'180'}">
									<small class="help-block">VIP4等级对应的有效天数</small>
								</div>
							</div>
							<div class="col-md-6">
								<div class="form-group">
									<label>VIP5 天数</label>
									<input class="form-control" type="number" name="VIP5" min="1"
										placeholder="请输入VIP5对应的天数"
										value="{:isset($vipConfig.VIP5)?$vipConfig.VIP5:'365'}">
									<small class="help-block">VIP5等级对应的有效天数</small>
								</div>
							</div>
						</div>
						
						<div class="row">
							<div class="col-md-6">
								<div class="form-group">
									<label>其他VIP等级</label>
									<div class="alert alert-info">
										<small>如需添加更多VIP等级，请联系开发人员</small>
									</div>
								</div>
							</div>
							<div class="col-md-6">
								<div class="form-group">
									<label>说明</label>
									<div class="alert alert-warning">
										<small><strong>注意：</strong>新注册用户默认为VIP0（非会员）状态</small>
									</div>
								</div>
							</div>
						</div>
						
						<div class="form-group">
							<button class="layui-btn" type="button" lay-submit lay-filter="save">
							    保存配置
							</button>
							<button class="layui-btn layui-btn-normal" type="button" onclick="resetToDefault()">
							    恢复默认
							</button>
						</div>
					</form>
				</div>
            </div>
          </div>
        </div>
      </div>
    </main>
{include file='public/footer'/}
<script>
layui.use(['form', 'layer'], function(){
  var layer = layui.layer
  ,form = layui.form;
  
  // 监听提交
  form.on('submit(save)', function(data){
        var loading = layer.load(0, {shade: false});
        $.ajax({
            type: 'post',
            url: "{:url('doSave')}",
            data: $("#vipForm").serialize(),
            dataType: "json",
            success: function(data) {
                layer.close(loading);
                if(data.code === 200){
                    layer.msg(data.msg, {
                      icon: 1,
                      time: 1500
                    }, function(){
                        window.location.reload();
                    });                      
                }else{
                     layer.msg(data.msg, {icon: 2});
                     return false;
                }  
            },
            error: function (XMLHttpRequest, textStatus, errorThrown) {
                layer.close(loading);
                if (textStatus == "timeout") {
                    layer.msg('请求超时！', {icon: 2, time: 1500});                      
                } else {
                    layer.msg('服务器错误！', {icon: 2, time: 1500});                         
                }
            }
        }); 
        return false;
    });
});

// 恢复默认配置
function resetToDefault() {
    layui.layer.confirm('确定要恢复默认配置吗？', {
        btn: ['确定', '取消']
    }, function(index) {
        $('input[name="VIP0"]').val('0');
        $('input[name="VIP1"]').val('30');
        $('input[name="VIP2"]').val('60');
        $('input[name="VIP3"]').val('90');
        $('input[name="VIP4"]').val('180');
        $('input[name="VIP5"]').val('365');
        layui.layer.close(index);
        layui.layer.msg('已恢复默认配置，请点击保存');
    });
}
</script>
