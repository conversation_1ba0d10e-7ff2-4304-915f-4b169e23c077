{include file='public/meta' /}

<main class="layui-layout">
	<div class="container-fluid">
		<div class="row">
			<div class="col-md-12">
				<div class="card">
					<div class="card-header">
						<h4>
							{if $operation == 'add'}添加银行{else/}编辑银行{/if}
						</h4>
					</div>
					<div class="card-body">
						<form class="layui-form" id="loginfrom">
						    {if $operation == 'edit'}
							<input type="hidden" id="id" name="id" value="{:isset($info.id)?$info.id:''}">

							{/if}
							<div class="form-group">
								<label>
									用户名
								</label>
                            {if $operation == 'add'}
<!--								<div class="layui-col-md4">-->
<!--									<div class="layui-input-block">-->
										<input type="text" id="HandoverCompany" class="layui-input" style="position:absolute;z-index:2;width:80%;" lay-verify="required" value=""  autocomplete="off">
										<select type="text" name="uid" id="uid" lay-filter="uid" autocomplete="off"  lay-verify="required" class="layui-select" lay-search>
											{foreach $bankinfo as $key=>$vo }
											<option value="{$vo.id}">{$vo.username}</option>
											{/foreach}
										</select>
<!--									</div>-->
<!--								</div>-->

								{else}
								<input type="hidden" id="uid" name="uid" value="{:isset($info.uid)?$info.uid:''}">
								<input type="text" name="username" id="username" class="layui-input"  lay-verify="required" value="{$info.username}" disabled>
								<!--								<select id="uid" name="uid"  {if $operation == 'edit'}disabled="disabled"{/if}>-->
<!--								<option value="">请选择</option>-->
<!--								{foreach $bankinfo as $key=>$vo }-->
<!--								<option value="{$vo.id}" {if $operation == 'edit'}{if $vo.id == $info.uid} selected=""{/if}{/if}>{$vo.username}</option>-->
<!--								{/foreach}-->
<!--								</select>-->
								{/if}
							</div>

							<div class="form-group">
								<label>
									银行卡用户名
								</label>
								<input class="form-control" type="text" id="name" name="name"
									   placeholder="请输入您的银行卡用户名" {if $operation == 'edit'}  value="{:isset($info.name)?$info.name:''}" {/if}>
							</div>

							<div class="form-group">
								<label>
									银行账号
								</label>
								<input class="form-control" type="text" id="bankid" name="bankid"
								placeholder="请输入您的银行账号" {if $operation == 'edit'}  value="{:isset($info.bankid)?$info.bankid:''}" {/if}>
							</div>
							<div class="form-group">
								<label>
									银行信息
								</label>
                                <input class="form-control" type="text" id="bankinfo" name="bankinfo"
								placeholder="请输入您的银行信息" {if $operation == 'edit'}  value="{:isset($info.bankinfo)?$info.bankinfo:''}" {/if}>
							</div>
							<div class="form-group">
								<button class="layui-btn" type="button" lay-submit lay-filter="save">
								    {if $operation == 'add'}提交{else/}更新{/if}	
								</button>
							</div>
						</form>
					</div>
				</div>
			</div>
		</div>
	</div>
</main>
{include file='public/onlyfooter'/}
<script>

layui.use(['form'], function(){
  var layer = layui.layer
  ,form = layui.form;

   //监听提交
  form.on('submit(save)', function(data){
        var bankinfo = $('#bankinfo').val();
        var uid = $('#uid').val();
        var bankid = $('#bankid').val();
        if(bankinfo == "" || bankinfo == null || bankinfo == undefined){
            layer.msg("请输入银行信息");
            return false;
        }   
        if(uid == "" || uid == null || uid == undefined){
            layer.msg("请选择用户");
            return false;
        }
        if(bankid == "" || bankid == null || bankid == undefined){
            layer.msg("请选择用户");
            return false;
        }          
        var loading = layer.load(0, {shade: false});
        $.ajax({
            type: 'post',
            url: "{:url('doSave')}",
            data:$("#loginfrom").serialize(),
            dataType:"json",
            success: function(data) {
                if(data.code === 200){
                    layer.msg(data.msg, {
                      icon: 1,
                      time: 1500 //2秒关闭（如果不配置，默认是3秒）
                    }, function(){
                        layer.closeAll();
                        window.parent.location.reload();
                        var index = parent.layer.getFrameIndex(window.name);
                        parent.layer.close(index);
                        
                    });                      
                }else{
                     layer.closeAll();
                     layer.msg(data.msg);
                     return false;
                }  
            },
            error: function (XMLHttpRequest, textStatus, errorThrown) {
                layer.closeAll();
                if (textStatus == "timeout") {
                    layer.msg('请求超时！', {
                      icon: 2,
                      time: 1500 //2秒关闭（如果不配置，默认是3秒）
                    }, function(){
                        return false;
                    });                      
                } else {
                    layer.msg('服务器错误！', {
                      icon: 2,
                      time: 1500 //2秒关闭（如果不配置，默认是3秒）
                    }, function(){
                        return false;
                    });                         
                }
            },                    
        }); 
    });


	form.on('select(uid)', function (data) {   //选择移交单位 赋值给input框
		var select_text = data.elem[data.elem.selectedIndex].text;
		$("#HandoverCompany").val(select_text );
		$("#uid").next().find("dl").css({ "display": "none" });
		form.render();
	});


	$('#HandoverCompany').bind('input propertychange', function () {
		var value = $("#HandoverCompany").val();
		$("#uid").val(value);
		form.render();
		$("#uid").next().find("dl").css({ "display": "block" });
		var dl = $("#uid").next().find("dl").children();
		var j = -1;
		for (var i = 0; i < dl.length; i++) {
			if (dl[i].innerHTML.indexOf(value) <= -1) {
				dl[i].style.display = "none";
				j++;
			}
			if (j == dl.length-1) {
				$("#uid").next().find("dl").css({ "display": "none" });
			}
		}

	})



});


$('#HandoverCompany').on('input propertychange',function (){
	if($(this).val() !== ''){
		console.log($(this).val());
	}
})

</script>