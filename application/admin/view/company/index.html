{include file='public/meta' /}
{include file='public/menu' /}
{include file='public/head' /}
    <main class="layout-content">
      <div class="container-fluid">
        <div class="row">
          <div class="col-lg-12">
            <div class="card">
				<div class="card-header">
					<h4>
						公司简历配置
					</h4>
					<small class="help-block">配置公司基本信息，用于对外展示公司简历</small>
				</div>
				<div class="card-body">
					<form class="layui-form" id="companyForm">
						<!-- 基本信息 -->
						<div class="row">
							<div class="col-md-12">
								<h5 class="text-primary">基本信息</h5>
								<hr>
							</div>
						</div>
						
						<div class="row">
							<div class="col-md-6">
								<div class="form-group">
									<label>公司名称 <span class="text-danger">*</span></label>
									<input class="form-control" type="text" name="company_name" 
										placeholder="请输入公司名称" 
										value="{:isset($companyConfig.company_name)?$companyConfig.company_name:''}">
									<small class="help-block">公司的正式名称</small>
								</div>
							</div>
							<div class="col-md-6">
								<div class="form-group">
									<label>所属行业</label>
									<input class="form-control" type="text" name="company_industry" 
										placeholder="请输入所属行业" 
										value="{:isset($companyConfig.company_industry)?$companyConfig.company_industry:''}">
									<small class="help-block">如：互联网、金融、制造业等</small>
								</div>
							</div>
						</div>
						
						<div class="row">
							<div class="col-md-6">
								<div class="form-group">
									<label>成立时间</label>
									<input class="form-control" type="text" name="company_founded" 
										placeholder="请输入成立时间" 
										value="{:isset($companyConfig.company_founded)?$companyConfig.company_founded:''}">
									<small class="help-block">如：2020年、2020年1月等</small>
								</div>
							</div>
							<div class="col-md-6">
								<div class="form-group">
									<label>员工规模</label>
									<input class="form-control" type="text" name="company_employees" 
										placeholder="请输入员工规模" 
										value="{:isset($companyConfig.company_employees)?$companyConfig.company_employees:''}">
									<small class="help-block">如：100-500人、1000+人等</small>
								</div>
							</div>
						</div>
						
						<!-- 公司LOGO -->
						<div class="form-group">
							<label>公司LOGO</label>
							<div class="form-group">
								<div class="layui-upload-drag" id="company_logo">
									<i class="layui-icon"></i>
									<p>点击上传，或将文件拖拽到此处</p>
									<div {if !isset($companyConfig.company_logo) || empty($companyConfig.company_logo)}class="layui-hide"{/if} id="logoView">
										<hr>
										<input type="hidden" id="logoInput" name="company_logo" value="{:isset($companyConfig.company_logo)?$companyConfig.company_logo:''}">
										<img src="{:isset($companyConfig.company_logo)?$companyConfig.company_logo:''}" alt="公司LOGO" style="max-width: 200px;height: auto;">
									</div>
								</div>  
							</div>	
							<small class="help-block">建议尺寸：200x200像素，支持PNG、JPG格式</small>
						</div>
						
						<!-- 公司描述 -->
						<div class="form-group">
							<label>公司简介</label>
							<textarea class="form-control" name="company_description" rows="4" 
								placeholder="请输入公司简介">{:isset($companyConfig.company_description)?$companyConfig.company_description:''}</textarea>
							<small class="help-block">简要介绍公司的业务和特色</small>
						</div>
						
						<!-- 联系信息 -->
						<div class="row">
							<div class="col-md-12">
								<h5 class="text-primary">联系信息</h5>
								<hr>
							</div>
						</div>
						
						<div class="row">
							<div class="col-md-6">
								<div class="form-group">
									<label>公司地址</label>
									<input class="form-control" type="text" name="company_address" 
										placeholder="请输入公司地址" 
										value="{:isset($companyConfig.company_address)?$companyConfig.company_address:''}">
								</div>
							</div>
							<div class="col-md-6">
								<div class="form-group">
									<label>联系电话</label>
									<input class="form-control" type="text" name="company_phone" 
										placeholder="请输入联系电话" 
										value="{:isset($companyConfig.company_phone)?$companyConfig.company_phone:''}">
								</div>
							</div>
						</div>
						
						<div class="row">
							<div class="col-md-6">
								<div class="form-group">
									<label>邮箱地址</label>
									<input class="form-control" type="email" name="company_email" 
										placeholder="请输入邮箱地址" 
										value="{:isset($companyConfig.company_email)?$companyConfig.company_email:''}">
								</div>
							</div>
							<div class="col-md-6">
								<div class="form-group">
									<label>官方网站</label>
									<input class="form-control" type="url" name="company_website" 
										placeholder="请输入官方网站" 
										value="{:isset($companyConfig.company_website)?$companyConfig.company_website:''}">
								</div>
							</div>
						</div>
						
						<div class="form-group">
							<button class="layui-btn" type="button" lay-submit lay-filter="save">
							    保存配置
							</button>
							<button class="layui-btn layui-btn-normal" type="button" onclick="resetForm()">
							    重置
							</button>
						</div>
					</form>
				</div>
            </div>
          </div>
        </div>
      </div>
    </main>
{include file='public/footer'/}
<script>
layui.use(['form', 'upload', 'layer'], function(){
  var layer = layui.layer
  ,form = layui.form
  ,upload = layui.upload;

  // 上传LOGO
  upload.render({
    elem: '#company_logo'
    ,url: "{:url('doupload')}"
    ,before: function(obj){
      layer.msg('上传中', {icon: 16, time: 0});
    }
    ,done: function(res){
      $("#logoInput").val(res.data);
      layui.$('#logoView').removeClass('layui-hide').find('img').attr('src', res.data);
      layer.msg('上传成功');
    }
    ,error: function(){
      layer.msg('上传失败');
    }
  });

  // 监听提交
  form.on('submit(save)', function(data){
        var loading = layer.load(0, {shade: false});
        $.ajax({
            type: 'post',
            url: "{:url('doSave')}",
            data: $("#companyForm").serialize(),
            dataType: "json",
            success: function(data) {
                layer.close(loading);
                if(data.code === 200){
                    layer.msg(data.msg, {
                      icon: 1,
                      time: 1500
                    }, function(){
                        window.location.reload();
                    });
                }else{
                     layer.msg(data.msg, {icon: 2});
                     return false;
                }
            },
            error: function (XMLHttpRequest, textStatus, errorThrown) {
                layer.close(loading);
                if (textStatus == "timeout") {
                    layer.msg('请求超时！', {icon: 2, time: 1500});
                } else {
                    layer.msg('服务器错误！', {icon: 2, time: 1500});
                }
            }
        });
        return false;
    });
});

// 重置表单
function resetForm() {
    layui.layer.confirm('确定要重置表单吗？', {
        btn: ['确定', '取消']
    }, function(index) {
        document.getElementById('companyForm').reset();
        layui.layer.close(index);
        layui.layer.msg('表单已重置');
    });
}
</script>
