{include file='public/meta' /}

<main class="layui-layout">
	<div class="container-fluid">
		<div class="row">
			<div class="col-md-12">
				<div class="card">
					<div class="card-header">
					</div>
					<div class="card-body">
						<form class="layui-form" id="loginfrom">
						    {if $operation == 'edit'}<input type="hidden" id="id" name="id" value="{:isset($info.id)?$info.id:''}">{/if}
							<!--<div class="form-group">-->
							<!--	<label>公告名称</label>-->
							<!--	<input class="form-control" type="text" id="name" name="name"-->
							<!--	placeholder="请输入您的中文分类名称" {if $operation == 'edit'}  value="{:isset($info.name_yn_yu)?$info.name_yn_yu:''}" {/if}>-->
							<!--</div>-->
						<div class="form-group">
								<label>公告名称(英文)</label>
								<input class="form-control" type="text" id="name_en_us" name="name_en_us"
								placeholder="请输入您的英文分类名称" {if $operation == 'edit'}  value="{:isset($info.name_en_us)?$info.name_en_us:''}" {/if}>
							</div>
							<div class="form-group">
								<label>公告内容(英文)</label>
								<textarea class="form-control" id="text_en_us" name="text_en_us" rows="6" placeholder="请输入您的英文公告内容">{if $operation == 'edit'}{:isset($info.text_en_us)?$info.text_en_us:''}{/if}</textarea>	
							</div>
							<div class="form-group">
								<label>公告名称(韩语)</label>
								<input class="form-control" type="text" id="name_ko_hy" name="name_ko_hy"
								placeholder="请输入您的韩语名称" {if $operation == 'edit'}  value="{:isset($info.name_ko_hy)?$info.name_ko_hy:''}" {/if}>
							</div>
								<div class="form-group">
								<label>公告内容(韩语)</label>
								<textarea class="form-control" id="text_ko_hy" name="text_ko_hy" rows="6" placeholder="请输入您的韩语公告内容">{if $operation == 'edit'}{:isset($info.text_ko_hy)?$info.text_ko_hy:''}{/if}</textarea>	
							</div>
							<!--<div class="form-group">-->
							<!--	<label>公告名称(西班牙语)</label>-->
							<!--	<input class="form-control" type="text" id="name_es_spa" name="name_es_spa"-->
							<!--	placeholder="请输入您的西班牙语分类名称" {if $operation == 'edit'}  value="{:isset($info.name_es_spa)?$info.name_es_spa:''}" {/if}>-->
							<!--</div>-->
							<!--<div class="form-group">-->
							<!--	<label>公告名称(马来语)</label>-->
							<!--	<input class="form-control" type="text" id="name_ms_my" name="name_ms_my"-->
							<!--	placeholder="请输入您的马来语分类名称" {if $operation == 'edit'}  value="{:isset($info.name_ms_my)?$info.name_ms_my:''}" {/if}>-->
							<!--</div>-->
							<!--	<div class="form-group">-->
							<!--	<label>公告名称(越南语)</label>-->
							<!--	<input class="form-control" type="text" id="name_yn_yu" name="name_yn_yu"-->
							<!--	placeholder="请输入您的越南语分类名称" {if $operation == 'edit'}  value="{:isset($info.name_yn_yu)?$info.name_yn_yu:''}" {/if}>-->
							<!--</div>-->
						
								<!--	<div class="form-group">
								<label>公告内容(英文)</label>
								<textarea class="form-control" id="text_en_us" name="text_en_us" rows="6" placeholder="请输入您的英文公告内容">{if $operation == 'edit'}{:isset($info.text_en_us)?$info.text_en_us:''}{/if}</textarea>	
							</div>
							<div class="form-group">
								<label>公告内容(西班牙语)</label>
								<textarea class="form-control" id="text_es_spa" name="text_es_spa" rows="6" placeholder="请输入您的西班牙语公告内容">{if $operation == 'edit'}{:isset($info.text_es_spa)?$info.text_es_spa:''}{/if}</textarea>	
							</div>
							<div class="form-group">
								<label>公告内容(马来语)</label>
								<textarea class="form-control" id="text_ms_my" name="text_ms_my" rows="6" placeholder="请输入您的马来语公告内容">{if $operation == 'edit'}{:isset($info.text_ms_my)?$info.text_ms_my:''}{/if}</textarea>	
							</div>
								<div class="form-group">
								<label>公告内容(越南语)</label>
								<textarea class="form-control" id="text_yn_yu" name="text_yn_yu" rows="6" placeholder="请输入您的越南语公告内容">{if $operation == 'edit'}{:isset($info.text_yn_yu)?$info.text_yn_yu:''}{/if}</textarea>	
							</div>-->
							<div class="form-group">
								<button class="layui-btn" type="button" lay-submit lay-filter="save">
								    {if $operation == 'add'}提交{else/}更新{/if}	
								</button>
							</div>
						</form>
					</div>
				</div>
			</div>
		</div>
	</div>
</main>
{include file='public/onlyfooter'/}
<script>

layui.use(['form'], function(){
  var layer = layui.layer
  ,form = layui.form;
   //监听提交
  form.on('submit(save)', function(data){
        var name = $('#name').val();
        // if(name == "" || name == null || name == undefined){
        //     layer.msg("请输入公告名称");
        //     return false;
        // }   
        var loading = layer.load(0, {shade: false});
        $.ajax({
            type: 'post',
            url: "{:url('doSave')}",
            data:$("#loginfrom").serialize(),
            dataType:"json",
            success: function(data) {
                if(data.code === 200){
                    layer.msg(data.msg, {
                      icon: 1,
                      time: 1500 //2秒关闭（如果不配置，默认是3秒）
                    }, function(){
                        layer.closeAll();
                        window.parent.location.reload();
                        var index = parent.layer.getFrameIndex(window.name);
                        parent.layer.close(index);
                        
                    });                      
                }else{
                     layer.closeAll();
                     layer.msg(data.msg);
                     return false;
                }  
            },
            error: function (XMLHttpRequest, textStatus, errorThrown) {
                layer.closeAll();
                if (textStatus == "timeout") {
                    layer.msg('请求超时！', {
                      icon: 2,
                      time: 1500 //2秒关闭（如果不配置，默认是3秒）
                    }, function(){
                        return false;
                    });                      
                } else {
                    layer.msg('服务器错误！', {
                      icon: 2,
                      time: 1500 //2秒关闭（如果不配置，默认是3秒）
                    }, function(){
                        return false;
                    });                         
                }
            },                    
        }); 
    });
});

</script>