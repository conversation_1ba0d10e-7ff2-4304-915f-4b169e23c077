{include file='public/meta' /}
{include file='public/menu' /}
{include file='public/head' /}
    <main class="layout-content">
      <div class="container-fluid">
        <div class="row">
          <div class="col-lg-12">
            <div class="card">
                <div class="card-header">
                    <h4>OSS对象存储配置</h4>
                    <small class="help-block">配置文件上传到云存储服务，支持阿里云OSS、腾讯云COS、七牛云等</small>
                </div>
                <div class="card-body">
                    <form class="layui-form" action="{:url('doSave')}" method="post">
                
                <!-- 基础配置 -->
                <div class="layui-form-item">
                    <label class="layui-form-label">启用OSS</label>
                    <div class="layui-input-block">
                        <input type="checkbox" name="enable" value="1" lay-skin="switch" lay-text="开启|关闭" 
                               {if isset($ossConfig.enable) && $ossConfig.enable}checked{/if}>
                        <div class="layui-form-mid layui-word-aux">开启后文件将上传到云存储，关闭则保存到本地服务器</div>
                    </div>
                </div>
                
                <div class="layui-form-item">
                    <label class="layui-form-label">服务商</label>
                    <div class="layui-input-block">
                        <select name="provider" lay-filter="provider">
                            <option value="aliyun" {if isset($ossConfig.provider) && $ossConfig.provider == 'aliyun'}selected{/if}>阿里云OSS</option>
                            <option value="tencent" {if isset($ossConfig.provider) && $ossConfig.provider == 'tencent'}selected{/if}>腾讯云COS</option>
                            <option value="qiniu" {if isset($ossConfig.provider) && $ossConfig.provider == 'qiniu'}selected{/if}>七牛云</option>
                        </select>
                    </div>
                </div>
                
                <!-- 阿里云OSS配置 -->
                <div class="provider-config" id="aliyun-config">
                    <div class="layui-card">
                        <div class="layui-card-header">阿里云OSS配置</div>
                        <div class="layui-card-body">
                            <div class="layui-form-item">
                                <label class="layui-form-label">AccessKey ID</label>
                                <div class="layui-input-block">
                                    <input type="text" name="aliyun_access_key_id" placeholder="请输入AccessKey ID" 
                                           value="{$ossConfig.aliyun.access_key_id ?? ''}" class="layui-input">
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">AccessKey Secret</label>
                                <div class="layui-input-block">
                                    <input type="password" name="aliyun_access_key_secret" placeholder="请输入AccessKey Secret" 
                                           value="{$ossConfig.aliyun.access_key_secret ?? ''}" class="layui-input">
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">Endpoint</label>
                                <div class="layui-input-block">
                                    <input type="text" name="aliyun_endpoint" placeholder="例如：https://oss-cn-hangzhou.aliyuncs.com" 
                                           value="{$ossConfig.aliyun.endpoint ?? ''}" class="layui-input">
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">Bucket</label>
                                <div class="layui-input-block">
                                    <input type="text" name="aliyun_bucket" placeholder="请输入Bucket名称" 
                                           value="{$ossConfig.aliyun.bucket ?? ''}" class="layui-input">
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">自定义域名</label>
                                <div class="layui-input-block">
                                    <input type="text" name="aliyun_domain" placeholder="可选，例如：https://cdn.example.com" 
                                           value="{$ossConfig.aliyun.domain ?? ''}" class="layui-input">
                                    <div class="layui-form-mid layui-word-aux">如果配置了CDN加速域名，请填写此项</div>
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <div class="layui-input-block">
                                    <button type="button" class="layui-btn layui-btn-normal" id="test-aliyun">测试连接</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 腾讯云COS配置 -->
                <div class="provider-config" id="tencent-config" style="display: none;">
                    <div class="layui-card">
                        <div class="layui-card-header">腾讯云COS配置</div>
                        <div class="layui-card-body">
                            <div class="layui-form-item">
                                <label class="layui-form-label">Secret ID</label>
                                <div class="layui-input-block">
                                    <input type="text" name="tencent_secret_id" placeholder="请输入Secret ID" 
                                           value="{$ossConfig.tencent.secret_id ?? ''}" class="layui-input">
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">Secret Key</label>
                                <div class="layui-input-block">
                                    <input type="password" name="tencent_secret_key" placeholder="请输入Secret Key" 
                                           value="{$ossConfig.tencent.secret_key ?? ''}" class="layui-input">
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">地域</label>
                                <div class="layui-input-block">
                                    <input type="text" name="tencent_region" placeholder="例如：ap-beijing" 
                                           value="{$ossConfig.tencent.region ?? ''}" class="layui-input">
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">Bucket</label>
                                <div class="layui-input-block">
                                    <input type="text" name="tencent_bucket" placeholder="请输入Bucket名称" 
                                           value="{$ossConfig.tencent.bucket ?? ''}" class="layui-input">
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">自定义域名</label>
                                <div class="layui-input-block">
                                    <input type="text" name="tencent_domain" placeholder="可选，例如：https://cdn.example.com"
                                           value="{$ossConfig.tencent.domain ?? ''}" class="layui-input">
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <div class="layui-input-block">
                                    <button type="button" class="layui-btn layui-btn-normal" id="test-tencent">测试连接</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 七牛云配置 -->
                <div class="provider-config" id="qiniu-config" style="display: none;">
                    <div class="layui-card">
                        <div class="layui-card-header">七牛云配置</div>
                        <div class="layui-card-body">
                            <div class="layui-form-item">
                                <label class="layui-form-label">Access Key</label>
                                <div class="layui-input-block">
                                    <input type="text" name="qiniu_access_key" placeholder="请输入Access Key" 
                                           value="{$ossConfig.qiniu.access_key ?? ''}" class="layui-input">
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">Secret Key</label>
                                <div class="layui-input-block">
                                    <input type="password" name="qiniu_secret_key" placeholder="请输入Secret Key" 
                                           value="{$ossConfig.qiniu.secret_key ?? ''}" class="layui-input">
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">Bucket</label>
                                <div class="layui-input-block">
                                    <input type="text" name="qiniu_bucket" placeholder="请输入Bucket名称" 
                                           value="{$ossConfig.qiniu.bucket ?? ''}" class="layui-input">
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">访问域名</label>
                                <div class="layui-input-block">
                                    <input type="text" name="qiniu_domain" placeholder="例如：https://cdn.example.com"
                                           value="{$ossConfig.qiniu.domain ?? ''}" class="layui-input">
                                    <div class="layui-form-mid layui-word-aux">七牛云必须配置访问域名</div>
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <div class="layui-input-block">
                                    <button type="button" class="layui-btn layui-btn-normal" id="test-qiniu">测试连接</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="layui-form-item">
                    <div class="layui-input-block">
                        <button class="layui-btn" lay-submit lay-filter="save">保存配置</button>
                        <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                        <button type="button" class="layui-btn layui-btn-warm" id="test-route">测试路由</button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
    
    // 切换服务商
    form.on('select(provider)', function(data){
        $('.provider-config').hide();
        $('#' + data.value + '-config').show();
    });
    
    // 初始化显示
    var currentProvider = $('select[name="provider"]').val();
    $('.provider-config').hide();
    $('#' + currentProvider + '-config').show();
    
    // 测试阿里云连接
    $('#test-aliyun').click(function(){
        var config = {
            provider: 'aliyun',
            aliyun_access_key_id: $('input[name="aliyun_access_key_id"]').val(),
            aliyun_access_key_secret: $('input[name="aliyun_access_key_secret"]').val(),
            aliyun_endpoint: $('input[name="aliyun_endpoint"]').val(),
            aliyun_bucket: $('input[name="aliyun_bucket"]').val()
        };

        if (!config.aliyun_access_key_id || !config.aliyun_access_key_secret || !config.aliyun_endpoint || !config.aliyun_bucket) {
            layer.msg('请先填写完整的配置信息');
            return;
        }
        
        layer.load(2);
        $.post('{:url("testConnection")}', config, function(res){
            layer.closeAll('loading');
            console.log('Response:', res); // 调试信息
            if (res.code === 200) {
                layer.msg(res.msg, {icon: 1});
            } else {
                layer.msg(res.msg, {icon: 2});
            }
        }).fail(function(xhr, status, error) {
            layer.closeAll('loading');
            console.error('Request failed:', xhr, status, error);
            layer.msg('请求失败：' + error, {icon: 2});
        });
    });

    // 测试腾讯云连接
    $('#test-tencent').click(function(){
        var config = {
            provider: 'tencent',
            tencent_secret_id: $('input[name="tencent_secret_id"]').val(),
            tencent_secret_key: $('input[name="tencent_secret_key"]').val(),
            tencent_region: $('input[name="tencent_region"]').val(),
            tencent_bucket: $('input[name="tencent_bucket"]').val()
        };

        if (!config.tencent_secret_id || !config.tencent_secret_key || !config.tencent_region || !config.tencent_bucket) {
            layer.msg('请先填写完整的配置信息');
            return;
        }

        layer.load(2);
        $.post('{:url("testConnection")}', config, function(res){
            layer.closeAll('loading');
            console.log('Tencent Response:', res);
            if (res.code === 200) {
                layer.msg(res.msg, {icon: 1});
            } else {
                layer.msg(res.msg, {icon: 2});
            }
        }).fail(function(xhr, status, error) {
            layer.closeAll('loading');
            console.error('Tencent Request failed:', xhr, status, error);
            layer.msg('请求失败：' + error, {icon: 2});
        });
    });

    // 测试七牛云连接
    $('#test-qiniu').click(function(){
        var config = {
            provider: 'qiniu',
            qiniu_access_key: $('input[name="qiniu_access_key"]').val(),
            qiniu_secret_key: $('input[name="qiniu_secret_key"]').val(),
            qiniu_bucket: $('input[name="qiniu_bucket"]').val(),
            qiniu_domain: $('input[name="qiniu_domain"]').val()
        };

        if (!config.qiniu_access_key || !config.qiniu_secret_key || !config.qiniu_bucket || !config.qiniu_domain) {
            layer.msg('请先填写完整的配置信息');
            return;
        }

        layer.load(2);
        $.post('{:url("testConnection")}', config, function(res){
            layer.closeAll('loading');
            console.log('Qiniu Response:', res);
            if (res.code === 200) {
                layer.msg(res.msg, {icon: 1});
            } else {
                layer.msg(res.msg, {icon: 2});
            }
        }).fail(function(xhr, status, error) {
            layer.closeAll('loading');
            console.error('Qiniu Request failed:', xhr, status, error);
            layer.msg('请求失败：' + error, {icon: 2});
        });
    });

    // 测试路由
    $('#test-route').click(function(){
        console.log('Testing route...');
        $.post('{:url("test")}', {}, function(res){
            console.log('Route test response:', res);
            if (res.code === 200) {
                layer.msg(res.msg, {icon: 1});
            } else {
                layer.msg(res.msg, {icon: 2});
            }
        }).fail(function(xhr, status, error) {
            console.error('Route test failed:', xhr, status, error);
            layer.msg('路由测试失败：' + error, {icon: 2});
        });
    });

    // 提交表单
    form.on('submit(save)', function(data){
        layer.load(2);
        $.post(data.form.action, data.field, function(res){
            layer.closeAll('loading');
            if (res.code === 200) {
                layer.msg('保存成功', {icon: 1});
            } else {
                layer.msg(res.msg, {icon: 2});
            }
        });
        return false;
    });
});
</script>

<style>
.provider-config {
    margin-top: 20px;
}
.card {
    margin-bottom: 20px;
}
</style>

                </div>
            </div>
          </div>
        </div>
      </div>
    </main>
{include file='public/footer' /}

<script>
layui.use(['form', 'layer', 'jquery'], function(){
    var form = layui.form;
    var layer = layui.layer;
    var $ = layui.jquery;

    // 切换服务商
    form.on('select(provider)', function(data){
        $('.provider-config').hide();
        $('#' + data.value + '-config').show();
    });

    // 初始化显示
    var currentProvider = $('select[name="provider"]').val();
    $('.provider-config').hide();
    $('#' + currentProvider + '-config').show();

    // 测试阿里云连接
    $('#test-aliyun').click(function(){
        var config = {
            provider: 'aliyun',
            aliyun_access_key_id: $('input[name="aliyun_access_key_id"]').val(),
            aliyun_access_key_secret: $('input[name="aliyun_access_key_secret"]').val(),
            aliyun_endpoint: $('input[name="aliyun_endpoint"]').val(),
            aliyun_bucket: $('input[name="aliyun_bucket"]').val()
        };

        if (!config.aliyun_access_key_id || !config.aliyun_access_key_secret || !config.aliyun_endpoint || !config.aliyun_bucket) {
            layer.msg('请先填写完整的配置信息');
            return;
        }

        layer.load(2);
        $.post('{:url("testConnection")}', config, function(res){
            layer.closeAll('loading');
            console.log('Response:', res); // 调试信息
            if (res.code === 200) {
                layer.msg(res.msg, {icon: 1});
            } else {
                layer.msg(res.msg, {icon: 2});
            }
        }).fail(function(xhr, status, error) {
            layer.closeAll('loading');
            console.error('Request failed:', xhr, status, error);
            layer.msg('请求失败：' + error, {icon: 2});
        });
    });

    // 测试腾讯云连接
    $('#test-tencent').click(function(){
        var config = {
            provider: 'tencent',
            tencent_secret_id: $('input[name="tencent_secret_id"]').val(),
            tencent_secret_key: $('input[name="tencent_secret_key"]').val(),
            tencent_region: $('input[name="tencent_region"]').val(),
            tencent_bucket: $('input[name="tencent_bucket"]').val()
        };

        if (!config.tencent_secret_id || !config.tencent_secret_key || !config.tencent_region || !config.tencent_bucket) {
            layer.msg('请先填写完整的配置信息');
            return;
        }

        layer.load(2);
        $.post('{:url("testConnection")}', config, function(res){
            layer.closeAll('loading');
            console.log('Tencent Response:', res);
            if (res.code === 200) {
                layer.msg(res.msg, {icon: 1});
            } else {
                layer.msg(res.msg, {icon: 2});
            }
        }).fail(function(xhr, status, error) {
            layer.closeAll('loading');
            console.error('Tencent Request failed:', xhr, status, error);
            layer.msg('请求失败：' + error, {icon: 2});
        });
    });

    // 测试七牛云连接
    $('#test-qiniu').click(function(){
        var config = {
            provider: 'qiniu',
            qiniu_access_key: $('input[name="qiniu_access_key"]').val(),
            qiniu_secret_key: $('input[name="qiniu_secret_key"]').val(),
            qiniu_bucket: $('input[name="qiniu_bucket"]').val(),
            qiniu_domain: $('input[name="qiniu_domain"]').val()
        };

        if (!config.qiniu_access_key || !config.qiniu_secret_key || !config.qiniu_bucket || !config.qiniu_domain) {
            layer.msg('请先填写完整的配置信息');
            return;
        }

        layer.load(2);
        $.post('{:url("testConnection")}', config, function(res){
            layer.closeAll('loading');
            console.log('Qiniu Response:', res);
            if (res.code === 200) {
                layer.msg(res.msg, {icon: 1});
            } else {
                layer.msg(res.msg, {icon: 2});
            }
        }).fail(function(xhr, status, error) {
            layer.closeAll('loading');
            console.error('Qiniu Request failed:', xhr, status, error);
            layer.msg('请求失败：' + error, {icon: 2});
        });
    });

    // 测试路由
    $('#test-route').click(function(){
        console.log('Testing route...');
        $.post('{:url("test")}', {}, function(res){
            console.log('Route test response:', res);
            if (res.code === 200) {
                layer.msg(res.msg, {icon: 1});
            } else {
                layer.msg(res.msg, {icon: 2});
            }
        }).fail(function(xhr, status, error) {
            console.error('Route test failed:', xhr, status, error);
            layer.msg('路由测试失败：' + error, {icon: 2});
        });
    });

    // 提交表单
    form.on('submit(save)', function(data){
        layer.load(2);
        $.post(data.form.action, data.field, function(res){
            layer.closeAll('loading');
            if (res.code === 200) {
                layer.msg('保存成功', {icon: 1});
            } else {
                layer.msg(res.msg, {icon: 2});
            }
        });
        return false;
    });
});
</script>

<style>
.provider-config {
    margin-top: 20px;
}
.card {
    margin-bottom: 20px;
}
</style>
