{include file='public/meta' /}

<main class="layui-layout">
	<div class="container-fluid">
		<div class="row">
			<div class="col-md-12">
				<div class="card">
					<div class="card-header">
						<!--<h4>-->
						<!--	{if $operation == 'add'}添加视频{else/}编辑视频{/if}-->
						<!--</h4>-->
					</div>
					<div class="card-body">
						<form class="layui-form" id="loginfrom">
						    <input type="hidden" id="id" name="id" value="{:isset($info.id)?$info.id:''}">
					        <div class="form-group">
								<label>
									视频名称(越南语)
								</label>
								<input class="form-control" type="text" id="vod_name" name="vod_name"
								placeholder="请输入您的中文视频名称" value="{:isset($info.vod_name)?$info.vod_name:''}">
							</div>
								<!--	<div class="form-group">
								<label>
									视频名称(英文)
								</label>
								<input class="form-control" type="text" id="vod_name_en_us" name="vod_name_en_us"
								placeholder="请输入您的英文视频名称" value="{:isset($info.vod_name_en_us)?$info.vod_name_en_us:''}">
							</div>
							<div class="form-group">
								<label>
									视频名称(西班牙语)
								</label>
								<input class="form-control" type="text" id="vod_name_es_spa" name="vod_name_es_spa"
								placeholder="请输入您的西班牙语视频名称" value="{:isset($info.vod_name_es_spa)?$info.vod_name_es_spa:''}">
							</div>
							<div class="form-group">
								<label>
									视频名称(马来语)
								</label>
								<input class="form-control" type="text" id="vod_name_ms_my" name="vod_name_ms_my"
								placeholder="请输入您的马来语视频名称" value="{:isset($info.vod_name_ms_my)?$info.vod_name_ms_my:''}">
							</div>
							<div class="form-group">
								<label>
									视频名称(越南语)
								</label>
								<input class="form-control" type="text" id="vod_name_yn_yu" name="vod_name_yn_yu"
								placeholder="请输入您的越南语视频名称" value="{:isset($info.vod_name_yn_yu)?$info.vod_name_yn_yu:''}">
							</div>-->
							
							
							
							<div class="form-group">
								<label>封面图片(越南语)</label>
								<input class="form-control" type="text" id="vod_pic" name="vod_pic"
								placeholder="请输入您的封面图片" value="{:isset($info.vod_pic)?$info.vod_pic:''}">
							</div>
							<div class="form-group">
								<label>封面图片</label>
								<div class="form-group">
									<div class="layui-upload-drag" id="ico">
										<i class="layui-icon"></i>
										<p>点击上传，或将文件拖拽到此处</p>
										<div {if $operation=='add'}class="layui-hide"{/if} id="uploadDemoView">
										<hr>
										<input type="hidden" id="vod_pic1" name="vod_pic1" value="{:isset($info.vod_pic)?$info.vod_pic:''}">
										<img src="{:isset($info.vod_pic)?$info.vod_pic:''}" alt="头像" style="max-width: 130px;border-style: solid;width: 125px;height: 125px;border-radius: 50%;">
									</div>
								</div>
							</div>
							<!--<div class="form-group">-->
							<!--	<label>封面图片(英文)</label>-->
							<!--	<input class="form-control" type="text" id="vod_pic_en_us" name="vod_pic_en_us"-->
							<!--	placeholder="请输入您的封面图片" value="{:isset($info.vod_pic_en_us)?$info.vod_pic_en_us:''}">-->
							<!--</div>-->
							<!--<div class="form-group">-->
							<!--	<label>封面图片(英文)</label>-->
							<!--	<div class="form-group">-->
							<!--		<div class="layui-upload-drag" id="ico">-->
							<!--			<i class="layui-icon"></i>-->
							<!--			<p>点击上传，或将文件拖拽到此处</p>-->
							<!--			<div {if $operation=='add'}class="layui-hide"{/if} id="uploadDemoView">-->
							<!--			<hr>-->
							<!--			<input type="hidden" id="vod_pic1" name="vod_pic1" value="{:isset($info.vod_pic_en_us)?$info.vod_pic_en_us:''}">-->
							<!--			<img src="{:isset($info.vod_pic_en_us)?$info.vod_pic_en_us:''}" alt="头像" style="max-width: 130px;border-style: solid;width: 125px;height: 125px;border-radius: 50%;">-->
							<!--		</div>-->
							<!--	</div>-->
							<!--</div>-->
							<!--<div class="form-group">-->
							<!--	<label>封面图片(西班牙语)</label>-->
							<!--	<input class="form-control" type="text" id="vod_pic_es_spa" name="vod_pic_es_spa"-->
							<!--	placeholder="请输入您的封面图片" value="{:isset($info.vod_pic_es_spa)?$info.vod_pic_es_spa:''}">-->
							<!--</div>-->
							<!--<div class="form-group">-->
							<!--	<label>封面图片(西班牙语)</label>-->
							<!--	<div class="form-group">-->
							<!--		<div class="layui-upload-drag" id="ico">-->
							<!--			<i class="layui-icon"></i>-->
							<!--			<p>点击上传，或将文件拖拽到此处</p>-->
							<!--			<div {if $operation=='add'}class="layui-hide"{/if} id="uploadDemoView">-->
							<!--			<hr>-->
							<!--			<input type="hidden" id="vod_pic1" name="vod_pic_es_spa" value="{:isset($info.vod_pic_es_spa)?$info.vod_pic_es_spa:''}">-->
							<!--			<img src="{:isset($info.vod_pic_es_spa)?$info.vod_pic_es_spa:''}" alt="头像" style="max-width: 130px;border-style: solid;width: 125px;height: 125px;border-radius: 50%;">-->
							<!--		</div>-->
							<!--	</div>-->
							<!--</div>-->
							<div class="form-group">
								<label>视频链接</label>
								<input class="form-control" type="text" id="vod_play_url" name="vod_play_url"
								placeholder="请输入您的视频链接" value="{:isset($info.vod_play_url)?$info.vod_play_url:''}">
							</div>

							<!-- 视频上传区域 -->
							<div class="form-group">
								<label>视频文件上传</label>
								<div class="layui-upload">
									<button type="button" class="layui-btn layui-btn-normal" id="videoUpload">
										<i class="layui-icon">&#xe67c;</i>上传视频文件
									</button>
									<div class="layui-upload-list">
										<div id="videoPreview" style="margin-top: 10px; {if !isset($info.vod_play_url) || empty($info.vod_play_url)}display: none;{/if}">
											<video width="300" height="200" controls id="videoPlayer">
												<source src="{:isset($info.vod_play_url)?$info.vod_play_url:''}" type="video/mp4" id="videoSource">
												您的浏览器不支持视频播放。
											</video>
											<p id="videoInfo" style="margin-top: 5px; color: #666; font-size: 12px;">
												{if isset($info.vod_play_url) && !empty($info.vod_play_url)}
												当前视频已设置
												{/if}
											</p>
										</div>
									</div>
								</div>
								<div class="layui-form-mid layui-word-aux">支持格式：MP4、AVI、MOV、WMV、FLV、MKV，最大2GB</div>
							</div>
							<!--<div class="form-group">-->
							<!--	<label>视频链接(英文)</label>-->
							<!--	<input class="form-control" type="text" id="vod_play_url" name="vod_play_url"-->
							<!--	placeholder="请输入您的英文视频链接" value="{:isset($info.play_url_en_us)?$info.play_url_en_us:''}">-->
							<!--</div>-->
							<!--							<div class="form-group">-->
							<!--	<label>视频链接(西班牙语)</label>-->
							<!--	<input class="form-control" type="text" id="vod_play_url" name="vod_play_url"-->
							<!--	placeholder="请输入您的西班牙语视频链接" value="{:isset($info.vod_play_url_es_spa)?$info.vod_play_url_es_spa:''}">-->
							<!--</div>-->
							<div class="form-group">
                                <label>视频类型</label>
                                <select id="vod_class_id" name="vod_class_id">
                                  <option value="">请选择</option>
                                  {foreach $class as $key=>$vo }
                                      <option value="{$vo.id}" {if $operation == 'edit'}{if $vo.id == $info.vod_class_id} selected="" {/if}{/if}>{$vo.name}</option>
                                  {/foreach}
                                </select>
							</div>
							<div class="form-group">
								<button class="layui-btn" type="button" lay-submit lay-filter="save">
								    {if $operation == 'add'}提交{else/}更新{/if}
								</button>
							</div>
						</form>
					</div>
				</div>
			</div>
		</div>
	</div>
</main>
{include file='public/onlyfooter'/}
<script>

layui.use(['form', 'upload'], function(){
  var layer = layui.layer
  ,form = layui.form
  ,upload = layui.upload;

	//拖拽上传封面图片
	upload.render({
		elem: '#ico'
		,url: "{:url('doupload')}" //此处用的是第三方的 http 请求演示，实际使用时改成您自己的上传接口即可。
		,before: function(obj){
			layer.msg('上传中', {icon: 16, time: 0});
		}
		,done: function(res){
			if(res.code == 200){
				// 更新上方的输入框
				$("#vod_pic").val(res.data);
				// 更新下方的预览图片
				layui.$('#uploadDemoView').removeClass('layui-hide').find('img').attr('src', res.data);
				// 更新隐藏字段
				layui.$('#vod_pic1').val(res.data);
				layer.closeAll();
				layer.msg('上传成功');
			} else {
				layer.closeAll();
				layer.msg(res.msg || '上传失败');
			}
		}
		,error: function(){
			layer.closeAll();
			layer.msg('上传失败，请重试');
		}
	});

	//视频上传
	upload.render({
		elem: '#videoUpload'
		,url: "{:url('doVideoUpload')}"
		,accept: 'video'
		,acceptMime: 'video/mp4,video/avi,video/mov,video/wmv,video/flv,video/mkv,video/webm,video/m4v'
		,size: 2147483648 // 2GB
		,before: function(obj){
			layer.msg('视频上传中，请稍候...', {icon: 16, time: 0});
		}
		,done: function(res){
			layer.closeAll();
			if (res.code !== 200) {
				return layer.msg(res.msg || '上传失败');
			}

			// 更新视频链接输入框
			$('#vod_play_url').val(res.data.video_url);

			// 更新视频预览
			$('#videoSource').attr('src', res.data.video_url);
			$('#videoPlayer')[0].load(); // 重新加载视频
			$('#videoPreview').show();

			// 更新视频信息
			var infoText = '视频上传成功';
			if (res.data.duration) {
				infoText += ' | 时长: ' + res.data.duration + '秒';
			}
			if (res.data.size) {
				infoText += ' | 大小: ' + formatBytes(res.data.size);
			}
			$('#videoInfo').text(infoText);

			layer.msg('视频上传成功');
		}
		,error: function(){
			layer.closeAll();
			layer.msg('视频上传失败');
		}
	});

	// 格式化文件大小
	function formatBytes(bytes, decimals = 2) {
		if (bytes === 0) return '0 Bytes';
		const k = 1024;
		const dm = decimals < 0 ? 0 : decimals;
		const sizes = ['Bytes', 'KB', 'MB', 'GB'];
		const i = Math.floor(Math.log(bytes) / Math.log(k));
		return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
	}

	//监听提交
  form.on('submit(save)', function(data){
        var vod_name = $('#vod_name').val();
        var vod_play_url = $('#vod_play_url').val();
        var vod_pic = $('#vod_pic').val();
        var vod_class_id = $('#vod_class_id').val();
        if(vod_name == "" || vod_name == null || vod_name == undefined){
            layer.msg("请输入视频名称");
            return false;
        }
        if(vod_play_url == "" || vod_play_url == null || vod_play_url == undefined){
            layer.msg("请输入视频链接");
            return false;
        }
        if(vod_pic == "" || vod_pic == null || vod_pic == undefined){
        	layer.msg("请输入视频封面图片");
			return false;
        }
        if(vod_class_id == "" || vod_class_id == null || vod_class_id == undefined){
            layer.msg("请选择视频分类");
            return false;
        }
        var loading = layer.load(0, {shade: false});
        $.ajax({
            type: 'post',
            url: "{:url('doSave')}",
            data:$("#loginfrom").serialize(),
            dataType:"json",
            success: function(data) {
                if(data.code === 200){
                    layer.msg(data.msg, {
                      icon: 1,
                      time: 1500 //2秒关闭（如果不配置，默认是3秒）
                    }, function(){
                        layer.closeAll();
                        window.parent.location.reload();
                        var index = parent.layer.getFrameIndex(window.name);
                        parent.layer.close(index);

                    });
                }else{
                     layer.msg(data.msg);
                     return false;
                }
            },
            error: function (XMLHttpRequest, textStatus, errorThrown) {
                layer.closeAll();
                if (textStatus == "timeout") {
                    layer.msg('请求超时！', {
                      icon: 2,
                      time: 1500 //2秒关闭（如果不配置，默认是3秒）
                    }, function(){
                        return false;
                    });
                } else {
                    layer.msg('服务器错误！', {
                      icon: 2,
                      time: 1500 //2秒关闭（如果不配置，默认是3秒）
                    }, function(){
                        return false;
                    });
                }
            },
        });
    });
});

</script>