<?php
namespace app\admin\model;

use think\Model;
use think\facade\Session;
class XuanfeilistModel extends Model
{
    // 设置当前模型对应的完整数据表名称
    protected $table = 'xuanfei_list';
    // 模型初始化
    protected static function init()
    {
        //TODO:初始化内容
    }

    public function doEditHot($post){
        $info = $this->where(['id'=>$post['id']])->find();
        if($info){
             $data['vod_hot'] = $post['status'];
             $data['update_time'] = time();
             $this->save($data,['id'=>$info['id']]) ? json_exit(200,'修改成功！'):json_exit(200,'修改失败！');
        }else{
             json_exit(401,'数据不存在！');
        }
    }  
    public function saveData($post){
        if(!empty($post['id'])){
            $info = $this->where(['id'=>$post['id']])->find();
            if(empty($info)){
                json_exit(401,'数据不存在！');
            }else{
                $data['xuanfei_name'] = $post['xuanfei_name'] ?(string)trim($post['xuanfei_name']):$info['xuanfei_name'];
              /*  $data['zh_cn'] = $post['xuanfei_name'] ?(string)trim($post['xuanfei_name']):$info['xuanfei_name'];*/
               /* $data['en_us'] = $post['en_us'] ?(string)trim($post['en_us']):$info['en_us'];
                $data['es_spa'] = $post['es_spa'] ?(string)trim($post['es_spa']):$info['es_spa'];
                $data['ms_my'] = $post['ms_my'] ?(string)trim($post['ms_my']):$info['ms_my'];*/
                $data['yn_yu'] = $post['xuanfei_name'] ?(string)trim($post['xuanfei_name']):$info['xuanfei_name'];
                // 处理女生素材图片，允许为空
                $data['img_url'] = isset($post['pc_src']) && is_array($post['pc_src']) ? json_encode($post['pc_src']) : json_encode([]);

                // 处理视频相关字段
                if (isset($post['video_url'])) {
                    $data['video_url'] = trim($post['video_url']);
                }
                if (isset($post['video_preview'])) {
                    $data['video_preview'] = trim($post['video_preview']);
                }
                if (isset($post['video_duration'])) {
                    $data['video_duration'] = intval($post['video_duration']);
                }
                if (isset($post['video_size'])) {
                    $data['video_size'] = intval($post['video_size']);
                }

                // 处理男生素材图片
                if (isset($post['male_images'])) {
                    $data['male_images'] = json_encode($post['male_images']);
                }

                $data['class_id'] = $post['class_id'] ?(int)trim($post['class_id']):$info['class_id'];
                return $this->save($data,['id'=>$post['id']]) ? json_exit(200,'更新成功！',$data) : json_exit(401,'更新失败！');
            }            
        }else{
            $info = $this->where(['xuanfei_name'=>$post['xuanfei_name']])->find();
            if(!empty($info)){
               json_exit(401,'名称重复！');
            }
        
            $data['xuanfei_name'] = (string)trim($post['xuanfei_name']);
          /*  $data['zh_cn'] = (string)trim($post['xuanfei_name']);
            $data['en_us'] = (string)trim($post['en_us']);
            $data['es_spa'] = (string)trim($post['es_spa']);
            $data['ms_my'] = (string)trim($post['ms_my']);*/
            $data['yn_yu'] = (string)trim($post['xuanfei_name']);
            // 处理女生素材图片，允许为空
            $data['img_url'] = isset($post['pc_src']) && is_array($post['pc_src']) ? json_encode($post['pc_src']) : json_encode([]);

            // 处理视频相关字段
            $data['video_url'] = isset($post['video_url']) ? trim($post['video_url']) : '';
            $data['video_preview'] = isset($post['video_preview']) ? trim($post['video_preview']) : '';
            $data['video_duration'] = isset($post['video_duration']) ? intval($post['video_duration']) : 0;
            $data['video_size'] = isset($post['video_size']) ? intval($post['video_size']) : 0;

            // 处理男生素材图片
            $data['male_images'] = isset($post['male_images']) ? json_encode($post['male_images']) : '';

            $data['class_id'] = (int)trim($post['class_id']);
            $data['create_time'] = time();
            return $this->save($data) ? json_exit(200,'提交成功！') : json_exit(401,'提交失败！');
            
        }
    }
    public function selectList(){
        $XuanfeiAddressModel = new XuanfeiAddressModel;
        $selectList = $XuanfeiAddressModel->selectList();
        return $selectList;
    }


    public function getxuanfeilist($get){
        $where = [];
        if(!empty($get['xuanfei_name'])){
            $where[] = ['a.xuanfei_name','like',"%".$get['xuanfei_name']."%"];
        }
        if(!empty($get['start_time']) && !empty($get['end_time'])){
            $count =$this->alias('a')->join('xuanfei_address b','a.class_id = b.id')->where($where)->whereTime('a.create_time', 'between', [strtotime($get['start_time']), strtotime($get['end_time'])])->count();
           $list = $this->alias('a')->join('xuanfei_address b','a.class_id = b.id')->where($where)->whereTime('a.create_time', 'between', [strtotime($get['start_time']), strtotime($get['end_time'])])->limit($get['limit'])->page($get['page'])->order('a.id','desc')->field('a.*,b.name')->select();
        }else {
           $count = $this->where($where)->count();
           $list = $this->alias('a')->join('xuanfei_address b','a.class_id = b.id')->where($where)->limit($get['limit'])->page($get['page'])->order('a.id','desc')->field('a.*,b.name')->select();
        }   
        foreach ($list as $k=>&$v){
            $v['create_time'] = date("Y-m-d H:i",$v['create_time']);

            // 安全处理女生素材图片显示
            $img_urls = json_decode($v['img_url'], true);
            if (is_array($img_urls) && count($img_urls) > 0) {
                $v['vod_pic'] = '../../../'. $img_urls[0];
            } else {
                $v['vod_pic'] = ''; // 没有女生素材时设为空
            }

            // 添加视频字段处理
            $v['video_url'] = isset($v['video_url']) ? $v['video_url'] : '';
            $v['video_preview'] = isset($v['video_preview']) ? $v['video_preview'] : '';
            $v['video_duration'] = isset($v['video_duration']) ? intval($v['video_duration']) : 0;
            $v['video_size'] = isset($v['video_size']) ? intval($v['video_size']) : 0;
        }
        return [
            'data'=>$list,
            'count'=>$count
            ];        
    }

//  获取单条数据
    public function getOneData($id){
        $info = $this->where(['id'=>$id])->find();
        if(empty($info)){
            json_exit(401,'数据不存在！');
        }else{
            $info['vod_pic'] = json_decode($info['img_url'],true);
            return $info;
        }
    }

// 删除
    public function delData($id){
        $info = $this->where(['id'=>$id])->find();
        if(empty($info)){
            json_exit(401,'数据不存在！');
        }else{
            return $this->where(['id'=>$id])->delete() ? json_exit(200,'删除成功！') : json_exit(401,'删除失败！');
        }
    }
}