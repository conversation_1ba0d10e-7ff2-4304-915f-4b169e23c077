<?php
namespace app\admin\model;

use think\Model;
use think\facade\Session;
class BlackIpModel extends Model
{
    // 设置当前模型对应的完整数据表名称
    protected $table = 'black_ip';
    // 模型初始化
    protected static function init()
    {
        //TODO:初始化内容
    }
    
  // 删除
    public function delData($id){
        $info = $this->where(['id'=>$id])->find();
        if(empty($info)){
            json_exit(401,'数据不存在！');
        }else{
            return $this->where(['id'=>$id])->delete() ? json_exit(200,'删除成功！') : json_exit(401,'删除失败！');
        }
    }
   
}