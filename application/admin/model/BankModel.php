<?php
namespace app\admin\model;

use think\Model;
use think\facade\Session;
class BankModel extends Model
{
    protected $table = 'bank';
    // 模型初始化
    protected static function init()
    {
        //TODO:初始化内容
    }
    public function selectList(){
        $selectList = $this->select();
        return $selectList;
    }
    public function saveData($post){
        if(!empty($post['id'])){
            $info = $this->where(['id'=>$post['id']])->find();
            if(empty($info)){
                json_exit(401,'数据不存在！');
            }else{
              
                $data['title'] = $post['title'] ?$post['title']:$info['title'];
                $data['thumb'] = $post['thumb'] ?$post['thumb']:$info['thumb'];
                return $this->save($data,['id'=>$post['id']]) ? json_exit(200,'更新成功！',$data) : json_exit(401,'更新失败！');
            }            
        }else{
         
            $data['title'] = $post['title'] ?$post['title']:$info['title'];
            $data['thumb'] = $post['thumb'] ?$post['thumb']:$info['thumb'];
            return $this->save($data) ? json_exit(200,'提交成功！') : json_exit(401,'提交失败！');
            
        }        
    }    
    public function getOneData($id){
        $info = $this->where(['id'=>$id])->find();
        if(empty($info)){
            json_exit(401,'数据不存在！');
        }else{
            return $info;
        }
    }
    public function getList($get){
        $where = [];
       $count = $this->where($where)->count();
       $list = $this->where($where)->limit($get['limit'])->page($get['page'])->order('id','desc')->select();
        return [
            'data'=>$list,
            'count'=>$count
        ];        
    }
    
  // 删除
    public function delData($id){
        $info = $this->where(['id'=>$id])->find();
        if(empty($info)){
            json_exit(401,'数据不存在！');
        }else{
            return $this->where(['id'=>$id])->delete() ? json_exit(200,'删除成功！') : json_exit(401,'删除失败！');
        }
    }
   
}