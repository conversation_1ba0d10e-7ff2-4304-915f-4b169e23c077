<?php
namespace app\admin\controller;
use think\Request;
use think\Controller;
use app\admin\controller\Base;

class EpusdtConfig extends Base
{
    public function index()
    {
        // 获取EPUSDT配置
        $epusdtConfig = $this->SystemModel->getConfig("epusdt");

        // 如果没有配置，设置默认值
        if (empty($epusdtConfig)) {
            $epusdtConfig = [
                'api_url' => 'https://pay.jsdao.cc',
                'api_key' => '',
                'enabled' => 0,
                'min_amount' => 10,
                'max_amount' => 10000,
                'wallet_addresses' => [''],
            ];
        }

        $this->assign('epusdtConfig', $epusdtConfig);
        return $this->fetch();
    }

    /**
     * 配置指南页面
     */
    public function guide()
    {
        return $this->fetch();
    }

    /**
     * 保存EPUSDT配置
     */
    public function doSave()
    {
        $post = $this->request->param();

        // 验证必填字段
        if (empty($post['api_url'])) {
            json_exit(401, 'API地址不能为空');
        }

        if (empty($post['api_key'])) {
            json_exit(401, 'API密钥不能为空');
        }

        if (!isset($post['enabled'])) {
            json_exit(401, '请选择是否启用EPUSDT支付');
        }

        if (empty($post['min_amount']) || $post['min_amount'] <= 0) {
            json_exit(401, '最小充值金额必须大于0');
        }

        if (empty($post['max_amount']) || $post['max_amount'] <= 0) {
            json_exit(401, '最大充值金额必须大于0');
        }

        if ($post['max_amount'] <= $post['min_amount']) {
            json_exit(401, '最大充值金额必须大于最小充值金额');
        }

        // 处理收款地址
        if (isset($post['wallet_addresses']) && is_array($post['wallet_addresses'])) {
            $addresses = array_filter($post['wallet_addresses'], function($addr) {
                return !empty(trim($addr));
            });

            if (empty($addresses)) {
                json_exit(401, '请至少配置一个收款地址');
            }

            // 验证地址格式（简单验证）
            foreach ($addresses as $address) {
                $address = trim($address);
                if (strlen($address) < 26 || strlen($address) > 42) {
                    json_exit(401, '收款地址格式不正确：' . $address);
                }
            }

            $post['wallet_addresses'] = array_values($addresses);
        } else {
            json_exit(401, '请配置收款地址');
        }

        // 保存配置
        $this->SystemModel->saveData($post, "epusdt");

        // 同步数据库中的地址状态
        $this->syncAddressesToDatabase($post['wallet_addresses']);

        json_exit(200, '保存成功');
    }

    /**
     * 测试EPUSDT连接
     */
    public function testConnection()
    {
        $post = $this->request->param();

        if (empty($post['api_url']) || empty($post['api_key'])) {
            json_exit(401, '请填写API地址和密钥');
        }

        $api_url = rtrim($post['api_url'], '/');
        $api_key = $post['api_key'];

        // 构建测试请求 - 使用正确的EPUSDT API格式
        $data = [
            'timestamp' => time()
        ];

        // 生成签名 - 使用正确的EPUSDT签名算法
        $signString = 'timestamp=' . $data['timestamp'] . $api_key;
        $data['signature'] = md5($signString);

        // 发送测试请求 - 使用正确的端点和格式
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $api_url . '/api/v1/order/create-transaction');
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data)); // 使用JSON格式
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'Accept: application/json'
        ]);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        if ($response === false) {
            json_exit(401, '连接失败：' . $error);
        }

        if ($httpCode !== 200) {
            json_exit(401, '连接失败，HTTP状态码：' . $httpCode);
        }

        $result = json_decode($response, true);
        if ($result && isset($result['status_code'])) {
            if ($result['status_code'] == 200) {
                json_exit(200, '连接成功！EPUSDT系统运行正常');
            } else {
                json_exit(401, '连接成功但API返回错误：' . (isset($result['message']) ? $result['message'] : '未知错误'));
            }
        } else {
            json_exit(401, '连接成功但响应格式异常: ' . json_encode($result));
        }
    }

    /**
     * 验证收款地址配置
     */
    public function validateWalletAddresses()
    {
        $epusdtConfig = $this->SystemModel->getConfig("epusdt");

        if (empty($epusdtConfig) || empty($epusdtConfig['wallet_addresses'])) {
            json_exit(401, '没有配置收款地址');
        }

        $addresses = array_filter($epusdtConfig['wallet_addresses'], function($addr) {
            return !empty(trim($addr));
        });

        if (empty($addresses)) {
            json_exit(401, '没有有效的收款地址');
        }

        $validCount = 0;
        $invalidAddresses = [];

        foreach ($addresses as $address) {
            $address = trim($address);
            // 简单的USDT地址格式验证
            if (preg_match('/^[13][a-km-zA-HJ-NP-Z1-9]{25,34}$/', $address) || // Bitcoin格式
                preg_match('/^0x[a-fA-F0-9]{40}$/', $address) || // Ethereum格式
                preg_match('/^T[A-Za-z1-9]{33}$/', $address)) { // Tron格式
                $validCount++;
            } else {
                $invalidAddresses[] = $address;
            }
        }

        if (!empty($invalidAddresses)) {
            json_exit(401, '以下地址格式不正确：' . implode(', ', $invalidAddresses));
        }

        json_exit(200, "验证成功！共有 {$validCount} 个有效的收款地址");
    }

    /**
     * 删除收款地址
     */
    public function deleteWalletAddress()
    {
        $post = $this->request->param();

        if (empty($post['address'])) {
            json_exit(401, '地址参数不能为空');
        }

        $address = trim($post['address']);

        try {
            // 从数据库中删除地址（软删除）
            $result = \think\Db::name('wallet_address')
                ->where('token', $address)
                ->where('deleted_at', null)
                ->update([
                    'deleted_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ]);

            if ($result) {
                json_exit(200, '地址删除成功');
            } else {
                json_exit(401, '地址不存在或已被删除');
            }
        } catch (\Exception $e) {
            json_exit(401, '删除失败：' . $e->getMessage());
        }
    }

    /**
     * 同步收款地址到数据库
     */
    public function syncWalletAddresses()
    {
        $epusdtConfig = $this->SystemModel->getConfig("epusdt");

        if (empty($epusdtConfig['wallet_addresses'])) {
            json_exit(401, '没有配置收款地址');
        }

        // 验证地址格式
        $validAddresses = [];
        $invalidAddresses = [];

        foreach ($epusdtConfig['wallet_addresses'] as $address) {
            $address = trim($address);
            if (empty($address)) continue;

            // 验证USDT地址格式
            if (preg_match('/^[13][a-km-zA-HJ-NP-Z1-9]{25,34}$/', $address) || // Bitcoin格式
                preg_match('/^0x[a-fA-F0-9]{40}$/', $address) || // Ethereum格式
                preg_match('/^T[A-Za-z1-9]{33}$/', $address)) { // Tron格式
                $validAddresses[] = $address;
            } else {
                $invalidAddresses[] = $address;
            }
        }

        if (!empty($invalidAddresses)) {
            json_exit(401, '以下地址格式不正确：' . implode(', ', $invalidAddresses));
        }

        if (empty($validAddresses)) {
            json_exit(401, '没有有效的收款地址');
        }

        // 直接同步到数据库
        $addedCount = 0;
        $existingCount = 0;
        $errorMessages = [];

        foreach ($validAddresses as $address) {
            try {
                // 检查地址是否已存在
                $existing = \think\Db::name('wallet_address')
                    ->where('token', $address)
                    ->where('deleted_at', null)
                    ->find();

                if ($existing) {
                    // 如果存在但被禁用，则启用它
                    if ($existing['status'] != 1) {
                        \think\Db::name('wallet_address')
                            ->where('id', $existing['id'])
                            ->update([
                                'status' => 1,
                                'updated_at' => date('Y-m-d H:i:s')
                            ]);
                    }
                    $existingCount++;
                } else {
                    // 插入新地址
                    \think\Db::name('wallet_address')->insert([
                        'token' => $address,
                        'status' => 1,
                        'created_at' => date('Y-m-d H:i:s'),
                        'updated_at' => date('Y-m-d H:i:s')
                    ]);
                    $addedCount++;
                }
            } catch (\Exception $e) {
                $errorMessages[] = "地址 {$address}: " . $e->getMessage();
            }
        }

        // 生成结果消息
        $message = "同步完成！";
        if ($addedCount > 0) {
            $message .= " 新增 {$addedCount} 个地址";
        }
        if ($existingCount > 0) {
            $message .= " 已存在 {$existingCount} 个地址";
        }
        if (!empty($errorMessages)) {
            $message .= " 错误：" . implode('; ', $errorMessages);
        }

        json_exit(200, $message);
    }

    /**
     * 同步地址到数据库（私有方法）
     */
    private function syncAddressesToDatabase($configAddresses)
    {
        try {
            // 获取当前配置中的所有有效地址
            $validAddresses = array_filter($configAddresses, function($addr) {
                return !empty(trim($addr));
            });
            $validAddresses = array_map('trim', $validAddresses);

            // 获取数据库中所有未删除的地址
            $dbAddresses = \think\Db::name('wallet_address')
                ->where('deleted_at', null)
                ->column('token');

            // 找出需要删除的地址（在数据库中但不在配置中）
            $addressesToDelete = array_diff($dbAddresses, $validAddresses);

            // 软删除不在配置中的地址
            if (!empty($addressesToDelete)) {
                \think\Db::name('wallet_address')
                    ->where('token', 'in', $addressesToDelete)
                    ->where('deleted_at', null)
                    ->update([
                        'deleted_at' => date('Y-m-d H:i:s'),
                        'updated_at' => date('Y-m-d H:i:s')
                    ]);
            }

            // 添加或更新配置中的地址
            foreach ($validAddresses as $address) {
                // 检查地址是否已存在（包括已删除的）
                $existing = \think\Db::name('wallet_address')
                    ->where('token', $address)
                    ->find();

                if ($existing) {
                    // 如果地址存在但被删除了，恢复它
                    if ($existing['deleted_at'] !== null) {
                        \think\Db::name('wallet_address')
                            ->where('id', $existing['id'])
                            ->update([
                                'status' => 1,
                                'deleted_at' => null,
                                'updated_at' => date('Y-m-d H:i:s')
                            ]);
                    } else if ($existing['status'] != 1) {
                        // 如果存在但被禁用，启用它
                        \think\Db::name('wallet_address')
                            ->where('id', $existing['id'])
                            ->update([
                                'status' => 1,
                                'updated_at' => date('Y-m-d H:i:s')
                            ]);
                    }
                } else {
                    // 插入新地址
                    \think\Db::name('wallet_address')->insert([
                        'token' => $address,
                        'status' => 1,
                        'created_at' => date('Y-m-d H:i:s'),
                        'updated_at' => date('Y-m-d H:i:s')
                    ]);
                }
            }
        } catch (\Exception $e) {
            // 记录错误但不影响配置保存
            \think\Log::error('同步地址到数据库失败：' . $e->getMessage());
        }
    }

    /**
     * 检查EPUSDT API端点
     */
    public function checkApiEndpoints()
    {
        $epusdtConfig = $this->SystemModel->getConfig("epusdt");

        if (empty($epusdtConfig) || empty($epusdtConfig['api_url']) || empty($epusdtConfig['api_key'])) {
            json_exit(401, 'EPUSDT配置不完整');
        }

        $api_url = rtrim($epusdtConfig['api_url'], '/');
        $api_key = $epusdtConfig['api_key'];

        // 测试不同的API端点
        $endpoints = [
            '/api/v1/wallet/get-wallet-list',
            '/api/v1/wallet/create-wallet-address',
            '/api/v1/order/get-order-list',
            '/api/v1/system/info'
        ];

        $results = [];

        foreach ($endpoints as $endpoint) {
            $data = [
                'timestamp' => time()
            ];

            // 生成签名 - 使用正确的EPUSDT签名算法
            $signString = 'timestamp=' . $data['timestamp'] . $api_key;
            $data['signature'] = md5($signString);

            // 发送请求
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $api_url . $endpoint);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Content-Type: application/json',
                'Accept: application/json'
            ]);
            curl_setopt($ch, CURLOPT_TIMEOUT, 10);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            curl_close($ch);

            $result = [
                'endpoint' => $endpoint,
                'http_code' => $httpCode,
                'success' => false,
                'message' => ''
            ];

            if ($response === false) {
                $result['message'] = '网络错误: ' . $error;
            } else {
                $responseData = json_decode($response, true);
                if ($responseData) {
                    $result['success'] = isset($responseData['status_code']);
                    $result['message'] = isset($responseData['message']) ? $responseData['message'] : '响应正常';
                    $result['response'] = $responseData;
                } else {
                    $result['message'] = '响应格式错误: ' . substr($response, 0, 100);
                }
            }

            $results[] = $result;
        }

        json_exit(200, '端点检查完成', $results);
    }

    /**
     * 检查502错误问题
     */
    public function check502Error()
    {
        $epusdtConfig = $this->SystemModel->getConfig("epusdt");

        if (empty($epusdtConfig) || empty($epusdtConfig['api_url'])) {
            json_exit(401, 'EPUSDT配置不完整');
        }

        $api_url = rtrim($epusdtConfig['api_url'], '/');
        $results = [];

        // 检查EPUSDT官方API端点（根据官方文档，不包含状态检查接口）
        $critical_endpoints = [
            '/' => 'GET',
            '/api/v1/order/create-transaction' => 'POST'
        ];

        foreach ($critical_endpoints as $endpoint => $method) {
            $full_url = $api_url . $endpoint;

            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $full_url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 15);
            curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (compatible; EPUSDT-502-Checker/1.0)');
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
            curl_setopt($ch, CURLOPT_MAXREDIRS, 3);

            if ($method === 'POST') {
                curl_setopt($ch, CURLOPT_POST, true);
                curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode(['test' => 'connectivity']));
                curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
            }

            $start_time = microtime(true);
            $response = curl_exec($ch);
            $end_time = microtime(true);
            $response_time = round(($end_time - $start_time) * 1000, 2);

            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            $info = curl_getinfo($ch);
            curl_close($ch);

            $status = 'success';
            $message = 'OK';
            $severity = 'info';

            if ($response === false) {
                $status = 'error';
                $message = '连接失败: ' . $error;
                $severity = 'error';
            } elseif ($httpCode == 502) {
                $status = 'error';
                $message = '502 Bad Gateway - 这是导致支付页面错误的原因';
                $severity = 'critical';
            } elseif ($httpCode >= 500) {
                $status = 'error';
                $message = '服务器错误 (HTTP ' . $httpCode . ')';
                $severity = 'error';
            } elseif ($httpCode >= 400) {
                $status = 'warning';
                $message = '客户端错误 (HTTP ' . $httpCode . ')';
                $severity = 'warning';
            } elseif ($response_time > 5000) {
                $status = 'warning';
                $message = '响应时间过长 (' . $response_time . 'ms)';
                $severity = 'warning';
            }

            $results[] = [
                'endpoint' => $endpoint,
                'method' => $method,
                'url' => $full_url,
                'http_code' => $httpCode,
                'response_time' => $response_time . 'ms',
                'status' => $status,
                'message' => $message,
                'severity' => $severity,
                'connect_time' => round($info['connect_time'] * 1000, 2) . 'ms'
            ];
        }

        // 生成诊断报告
        $has_502 = false;
        $has_errors = false;
        foreach ($results as $result) {
            if ($result['http_code'] == 502) {
                $has_502 = true;
            }
            if ($result['severity'] === 'error' || $result['severity'] === 'critical') {
                $has_errors = true;
            }
        }

        $diagnosis = [];
        $diagnosis[] = '根据EPUSDT官方文档，系统没有提供状态检查接口。';
        $diagnosis[] = '502错误是因为支付页面尝试访问不存在的 /pay/check-status/ 接口。';
        $diagnosis[] = '这不影响实际支付处理！支付成功后会通过异步回调更新订单状态。';

        if ($has_502) {
            $diagnosis[] = '确认：检测到502错误，这是预期的结果。';
        } elseif ($has_errors) {
            $diagnosis[] = '检测到其他服务器错误，可能影响支付功能。';
        } else {
            $diagnosis[] = 'EPUSDT核心功能运行正常。';
        }

        $diagnosis[] = '解决方案：引导用户使用我们的订单状态查询页面确认支付结果。';

        json_exit(200, '502错误检查完成', [
            'results' => $results,
            'diagnosis' => $diagnosis,
            'has_502_error' => $has_502,
            'status_checker_url' => 'https://jsdao.cc/epusdt_payment_status_checker.html'
        ]);
    }
}