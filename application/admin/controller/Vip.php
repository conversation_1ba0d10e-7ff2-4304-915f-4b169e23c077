<?php
namespace app\admin\controller;
use think\Request;
use think\Controller;
use app\admin\controller\Base;

class Vip extends Base
{
    public function index()
    {
        // 获取VIP配置
        $vipConfig = $this->SystemModel->getConfig("vip");
        
        // 如果没有配置，设置默认值
        if (empty($vipConfig)) {
            $vipConfig = [
                'VIP0' => 0,   // 非会员
                'VIP1' => 30,
                'VIP2' => 60,
                'VIP3' => 90,
                'VIP4' => 180,
                'VIP5' => 365,
            ];
        }
        
        $this->assign('vipConfig', $vipConfig);
        return $this->fetch();
    }

    public function doSave()
    {
        $post = $this->request->param();
        
        // 验证输入数据
        $vipConfig = [];
        foreach ($post as $key => $value) {
            if (strpos($key, 'VIP') === 0) {
                $days = intval($value);
                // VIP0允许为0天，其他VIP等级必须大于0
                if ($key !== 'VIP0' && $days < 1) {
                    json_exit(401, $key . ' 天数必须大于0！');
                }
                if ($key === 'VIP0' && $days !== 0) {
                    json_exit(401, 'VIP0必须为0天（非会员状态）！');
                }
                $vipConfig[$key] = $days;
            }
        }
        
        if (empty($vipConfig)) {
            json_exit(401, '请至少配置一个VIP等级！');
        }
        
        // 保存配置
        $this->SystemModel->saveData($vipConfig, "vip");
    }
}
