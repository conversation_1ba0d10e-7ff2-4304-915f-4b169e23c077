<?php
namespace app\admin\controller;
use think\Request;
use think\Controller;
use app\admin\controller\Base;
class System extends Base
{
    public function index()
    {
       $data = $this->SystemModel->getConfig("base");
       
       $this->assign('base',$data);
       return $this->fetch();
    }
    public function doupload(){
        try {
            // 检查文件是否上传
            $file = request()->file('file');
            if (!$file) {
                json_exit(401, '未上传文件');
            }

            // 使用绝对路径确保文件保存到正确位置
            $path = ROOT_PATH . "public" . DS . "base" . DS . "ico" . DS . date('Y/m/d') . DS;

            // 检查OssModel是否存在
            if (!$this->OssModel) {
                json_exit(500, "OssModel未初始化");
            }

            $this->OssModel->doupload($path);

        } catch (\Exception $e) {
            json_exit(500, "上传失败：" . $e->getMessage());
        } catch (\Error $e) {
            json_exit(500, "致命错误：" . $e->getMessage());
        }
    }

    public function doSave(){
        $post = $this->request->param();
        $this->SystemModel->saveData($post,"base");
    }

}
