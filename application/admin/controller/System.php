<?php
namespace app\admin\controller;
use think\Request;
use think\Controller;
use app\admin\controller\Base;
class System extends Base
{
    public function index()
    {
       $data = $this->SystemModel->getConfig("base");
       
       $this->assign('base',$data);
       return $this->fetch();
    }
    public function doupload(){
        try {
            // 直接输出到浏览器
            echo "System doupload start\n";
            
            // 检查文件是否上传
            $file = request()->file('file');
            if (!$file) {
                echo "No file uploaded\n";
                json_exit(401, '未上传文件');
            }
            
            echo "File uploaded: " . json_encode($file->getInfo()) . "\n";
            
            $path = "base/ico/" . date('Y/m/d') . '/';
            echo "Upload path: " . $path . "\n";
            
            // 检查OssModel是否存在
            if (!$this->OssModel) {
                echo "OssModel not found\n";
                json_exit(500, "OssModel未初始化");
            }
            
            echo "Calling OssModel->doupload\n";
            $this->OssModel->doupload($path);
            
        } catch (\Exception $e) {
            echo "System doupload error: " . $e->getMessage() . "\n";
            echo "Stack trace: " . $e->getTraceAsString() . "\n";
            json_exit(500, "上传失败：" . $e->getMessage());
        } catch (\Error $e) {
            echo "Fatal error: " . $e->getMessage() . "\n";
            json_exit(500, "致命错误：" . $e->getMessage());
        }
    }

    public function doSave(){
        $post = $this->request->param();
        $this->SystemModel->saveData($post,"base");
    }

}
