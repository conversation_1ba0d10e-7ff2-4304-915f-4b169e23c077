<?php
namespace app\admin\controller;
use think\Request;
use think\Controller;
use app\admin\controller\Base;
class Video extends Base
{
    public function index()
    {
       return $this->fetch();
    }


    public function list(){
        $get['page'] = $this->request->get('page') ?: 1;
        $get['limit'] = $this->request->get('limit') ?: 10;
        $get['vod_name'] = $this->request->get('vod_name') ?: "";
//        $get['cid'] = $this->request->get('cid') ?: "";
//        $get['key'] = $this->request->get('key') ?: "";
        $get['status'] = $this->request->get('vod_status') === "" ? "":$this->request->get('vod_status');
        $get['start_time'] = $this->request->get('start_time') ?: "";
        $get['end_time'] = $this->request->get('end_time') ?: "";
        $list = $this->VideoModel->getList($get);

        return json_table(0, "",$list['count'],$list['data']);
    }

    public function doEditHot(){
        $post = $this->request->param();
        $this->VideoModel->doEditHot($post);
    }

    public function doEditState(){
        $post = $this->request->param();
        $this->VideoModel->doEditState($post);
    }
    public function play(){
        $url = $this->request->get('url');
        $this->assign('url',$url);
        return $this->fetch();
    }
    public function operation($operation = null, $id = null){
        if(!empty($operation)){
            if($operation != "add"){
                if(!empty($id) || $id == 0){
                    $info = $this->VideoModel->getOneData($id);
                    $this->assign('info',$info);
                }else{
                    $this->error('编辑错误ID不能为空！');
                }
            }
            $class = $this->VideoclassModel->selectList();
            $this->assign('class',$class);
            $this->assign('operation',$operation);
            return $this->fetch();
        } else {
            $this->error('操作类型错误！');
        }
    }


//保存
    public function doSave(){
        $post = $this->request->param();
        if (empty($post['vod_pic'])){
            $post['vod_pic'] = $post['vod_pic1'];
        }
        unset($post['vod_pic1']);
         $this->VideoModel->saveData($post);
    }

// 删除
    public function doDel(){
        $post = $this->request->param();

        // 检查参数
        if(empty($post['id'])){
            json_exit(401, '参数错误');
        }

        // 调用删除方法（该方法内部已经处理JSON响应）
        $this->VideoModel->delData($post['id']);
    }

    // 批量删除
    public function doBatchDel(){
        $post = $this->request->param();

        // 检查参数
        if(empty($post['ids'])){
            json_exit(401, '参数错误');
        }

        // 调用批量删除方法
        $this->VideoModel->batchDelData($post['ids']);
    }

    //上传封面 - 使用本地存储
    public function doupload(){
        try {
            // 获取上传的文件
            $file = request()->file('file');
            if (!$file) {
                json_exit(401, '未上传文件');
            }

            // 获取文件信息
            $fileInfo = $file->getInfo();
            if (!$fileInfo) {
                json_exit(401, '文件信息获取失败');
            }

            // 验证文件类型
            $allowedTypes = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'];
            $extension = strtolower(pathinfo($fileInfo['name'], PATHINFO_EXTENSION));
            if (!in_array($extension, $allowedTypes)) {
                json_exit(401, '只支持图片格式：' . implode(', ', $allowedTypes));
            }

            // 设置上传路径
            $uploadPath = 'video/fmimg/';
            $rootPath = $_SERVER['DOCUMENT_ROOT'] ?: './public';
            $fullUploadPath = rtrim($rootPath, '/') . '/' . $uploadPath;

            // 确保目录存在
            if (!is_dir($fullUploadPath)) {
                if (!mkdir($fullUploadPath, 0755, true)) {
                    json_exit(401, '创建上传目录失败');
                }
            }

            // 生成文件名
            $fileName = date('YmdHis') . uniqid() . '.' . $extension;
            $targetFile = $fullUploadPath . $fileName;

            // 移动文件
            if (move_uploaded_file($fileInfo['tmp_name'], $targetFile)) {
                // 构建完整URL路径
                $fullUrl = '/' . $uploadPath . $fileName;
                json_exit(200, "上传成功", $fullUrl);
            } else {
                json_exit(401, "文件移动失败");
            }

        } catch (\Exception $e) {
            json_exit(401, "上传失败：" . $e->getMessage());
        }
    }

    //上传视频
    public function doVideoUpload(){
        $this->OssModel->upload_video("video/files");
    }

    // 测试路由方法
    public function testUpload(){
        json_exit(200, "路由测试成功 - doVideoUpload方法可访问");
    }

    // 测试批量删除路由
    public function testBatchDel(){
        json_exit(200, "路由测试成功 - doBatchDel方法可访问");
    }

    // 测试视频URL构建
    public function testVideoUrl(){
        // 模拟OSS配置和文件信息
        $ossConfig = [
            'endpoint' => 'oss-na-south-1.aliyuncs.com',
            'bucket' => 'tcyp2',
            'domain' => '' // 空表示使用默认构建逻辑
        ];

        $objectName = 'video/files/2025/06/30/20250630061847686.mp4';

        // 模拟URL构建逻辑
        if (!empty($ossConfig['domain'])) {
            $domain = $ossConfig['domain'];
            if (!preg_match('/^https?:\/\//', $domain)) {
                $domain = 'https://' . $domain;
            }
        } else {
            $endpoint = $ossConfig['endpoint'];
            $endpoint = preg_replace('/^https?:\/\//', '', $endpoint);
            $domain = 'https://tcyp2.' . $endpoint;
        }

        $videoUrl = rtrim($domain, '/') . '/' . ltrim($objectName, '/');

        $result = [
            'oss_config' => $ossConfig,
            'object_name' => $objectName,
            'constructed_domain' => $domain,
            'final_video_url' => $videoUrl,
            'expected_format' => 'https://tcyp2.oss-na-south-1.aliyuncs.com/video/files/2025/06/30/filename.mp4'
        ];

        json_exit(200, '视频URL构建测试', $result);
    }

    // 测试当前OSS配置和URL构建
    public function debugOssUrl(){
        try {
            $systemModel = new \app\admin\model\SystemModel();
            $ossConfig = $systemModel->getConfig('oss');

            if (empty($ossConfig)) {
                json_exit(401, 'OSS配置为空');
            }

            // 模拟URL构建过程
            $objectName = 'video/files/2025/06/30/test123456.mp4';

            if (!empty($ossConfig['domain'])) {
                $domain = $ossConfig['domain'];
                if (!preg_match('/^https?:\/\//', $domain)) {
                    $domain = 'https://' . $domain;
                }
            } else {
                $endpoint = $ossConfig['endpoint'];
                $endpoint = preg_replace('/^https?:\/\//', '', $endpoint);
                $domain = 'https://tcyp2.' . $endpoint;
            }

            $finalUrl = rtrim($domain, '/') . '/' . ltrim($objectName, '/');

            $result = [
                'oss_config' => [
                    'enable' => $ossConfig['enable'] ?? false,
                    'provider' => $ossConfig['provider'] ?? 'none',
                    'endpoint' => $ossConfig['endpoint'] ?? 'none',
                    'bucket' => $ossConfig['bucket'] ?? 'none',
                    'domain' => $ossConfig['domain'] ?? 'none'
                ],
                'url_construction' => [
                    'original_endpoint' => $ossConfig['endpoint'] ?? 'none',
                    'cleaned_endpoint' => preg_replace('/^https?:\/\//', '', $ossConfig['endpoint'] ?? ''),
                    'constructed_domain' => $domain,
                    'object_name' => $objectName,
                    'final_url' => $finalUrl
                ],
                'expected_format' => 'https://tcyp2.oss-na-south-1.aliyuncs.com/video/files/2025/06/30/test123456.mp4'
            ];

            json_exit(200, 'OSS配置和URL构建调试', $result);

        } catch (\Exception $e) {
            json_exit(401, '调试失败: ' . $e->getMessage());
        }
    }

    // 测试OSS配置
    public function testOssConfig(){
        $systemModel = new \app\admin\model\SystemModel();
        $ossConfig = $systemModel->getConfig('oss');

        $result = [
            'database_config' => $ossConfig,
            'config_exists' => !empty($ossConfig),
            'oss_enabled' => isset($ossConfig['enable']) && $ossConfig['enable'],
            'provider' => $ossConfig['provider'] ?? 'none'
        ];

        json_exit(200, 'OSS配置状态', $result);
    }
}
