<?php
namespace app\admin\controller;
use think\Request;
use think\Controller;
use app\admin\controller\Base;
class <PERSON>an<PERSON>ilist extends Base
{
    /*选妃列表*/
    public function xuanfeilist()
    {
        return $this->fetch();
    }
    public function xuanfeilistdata()
    {
        $get['page'] = $this->request->get('page') ?: 1;
        $get['limit'] = $this->request->get('limit') ?: 10;
        $get['xuanfei_name'] = $this->request->get('xuanfei_name') ?: "";
        $get['start_time'] = $this->request->get('start_time') ?: "";
        $get['end_time'] = $this->request->get('end_time') ?: "";
        $list = $this->XuanfeilistModel->getxuanfeilist($get);
        return json_table(0, "",$list['count'],$list['data']);
    }
    public function operation($operation = null, $id = null){
        if(!empty($operation)){
            if($operation != "add"){
                if(!empty($id) || $id == 0){
                    $info = $this->XuanfeilistModel->getOneData($id);

                    // 处理视频字段，确保它们存在且有默认值
                    $info['video_url'] = isset($info['video_url']) ? $info['video_url'] : '';
                    $info['video_preview'] = isset($info['video_preview']) ? $info['video_preview'] : '';
                    $info['video_duration'] = isset($info['video_duration']) ? intval($info['video_duration']) : 0;
                    $info['video_size'] = isset($info['video_size']) ? intval($info['video_size']) : 0;

                    // 格式化视频文件大小用于显示
                    if ($info['video_size'] > 0) {
                        $info['video_size_formatted'] = $this->formatBytes($info['video_size']);
                    } else {
                        $info['video_size_formatted'] = '0 Bytes';
                    }

                    $this->assign('info',$info);
                }else{
                    $this->error('编辑错误ID不能为空！');
                }
            }
            $class = $this->XuanfeilistModel->selectList();
            $this->assign('class',$class);
            $this->assign('operation',$operation);
            return $this->fetch();
        } else {
            $this->error('操作类型错误！');
        }
    }
    public function doupload(){
        try {
            // 设置更长的执行时间和内存限制
            set_time_limit(300); // 5分钟
            ini_set('memory_limit', '256M');

            // 获取上传的文件
            $file = request()->file('file');
            if (!$file) {
                json_exit(401, '未上传文件');
            }

            // 获取文件信息
            $fileInfo = $file->getInfo();
            if (!$fileInfo) {
                json_exit(401, '文件信息获取失败');
            }

            // 验证文件类型
            $allowedTypes = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'];
            $extension = strtolower(pathinfo($fileInfo['name'], PATHINFO_EXTENSION));
            if (!in_array($extension, $allowedTypes)) {
                json_exit(401, '只支持图片格式：' . implode(', ', $allowedTypes));
            }

            // 验证文件大小 (10MB限制)
            $maxSize = 10 * 1024 * 1024; // 10MB
            if ($fileInfo['size'] > $maxSize) {
                json_exit(401, '文件过大，最大支持10MB');
            }

            // 验证文件是否损坏
            if ($fileInfo['error'] !== UPLOAD_ERR_OK) {
                $errorMessages = [
                    UPLOAD_ERR_INI_SIZE => '文件超过了php.ini中upload_max_filesize的限制',
                    UPLOAD_ERR_FORM_SIZE => '文件超过了表单中MAX_FILE_SIZE的限制',
                    UPLOAD_ERR_PARTIAL => '文件只有部分被上传',
                    UPLOAD_ERR_NO_FILE => '没有文件被上传',
                    UPLOAD_ERR_NO_TMP_DIR => '找不到临时文件夹',
                    UPLOAD_ERR_CANT_WRITE => '文件写入失败',
                    UPLOAD_ERR_EXTENSION => '文件上传被扩展程序阻止'
                ];
                $errorMsg = $errorMessages[$fileInfo['error']] ?? '未知上传错误';
                json_exit(401, $errorMsg);
            }

            // 设置上传路径
            $uploadPath = 'xuanfei/';
            $rootPath = $_SERVER['DOCUMENT_ROOT'] ?: './public';
            $fullUploadPath = rtrim($rootPath, '/') . '/' . $uploadPath;

            // 确保目录存在
            if (!is_dir($fullUploadPath)) {
                if (!mkdir($fullUploadPath, 0755, true)) {
                    json_exit(401, '创建上传目录失败');
                }
            }

            // 检查目录权限
            if (!is_writable($fullUploadPath)) {
                json_exit(401, '上传目录没有写入权限');
            }

            // 生成文件名
            $fileName = date('YmdHis') . '_' . uniqid() . '.' . $extension;
            $targetFile = $fullUploadPath . $fileName;

            // 移动文件
            if (move_uploaded_file($fileInfo['tmp_name'], $targetFile)) {
                // 验证文件是否真的上传成功
                if (!file_exists($targetFile)) {
                    json_exit(401, "文件上传后验证失败");
                }

                // 构建完整URL路径
                $fullUrl = '/' . $uploadPath . $fileName;
                json_exit(200, "上传成功", $fullUrl);
            } else {
                json_exit(401, "文件移动失败，请检查服务器权限");
            }

        } catch (\Exception $e) {
            // 记录详细错误信息
            error_log("Upload error: " . $e->getMessage() . " in " . $e->getFile() . " on line " . $e->getLine());
            json_exit(401, "上传失败：" . $e->getMessage());
        }
    }

    public function doVideoUpload(){
        $this->OssModel->upload_video("xuanfei/video");
    }

    public function doSave(){
        $post = $this->request->param();

        // 检查是否至少上传了女生素材或男生素材中的一种
        $hasFemaleImages = !empty($post['pc_src']) && is_array($post['pc_src']) && count(array_filter($post['pc_src'])) > 0;
        $hasMaleImages = !empty($post['male_images']) && is_array($post['male_images']) && count(array_filter($post['male_images'])) > 0;

        if (!$hasFemaleImages && !$hasMaleImages) {
            $this->error('请至少上传女生素材或男生素材中的一种！');
        }

        $this->XuanfeilistModel->saveData($post);
    }
    
    public function doDel(){
        $post = $this->request->param();
        $this->XuanfeilistModel->delData($post['id']);
    }

    /**
     * 格式化文件大小
     * @param int $bytes 字节数
     * @param int $precision 小数位数
     * @return string 格式化后的文件大小
     */
    private function formatBytes($bytes, $precision = 2) {
        if ($bytes == 0) return '0 Bytes';

        $units = array('Bytes', 'KB', 'MB', 'GB', 'TB');
        $base = log($bytes, 1024);

        return round(pow(1024, $base - floor($base)), $precision) . ' ' . $units[floor($base)];
    }

    /**
     * 测试视频文件访问
     */
    public function testVideo() {
        $videoPath = $this->request->get('path');
        if (!$videoPath) {
            json_exit(400, "请提供视频路径");
        }

        // 移除开头的斜杠
        $videoPath = ltrim($videoPath, '/');

        // 检查文件是否存在
        if (file_exists($videoPath)) {
            $fileSize = filesize($videoPath);
            $mimeType = mime_content_type($videoPath);
            json_exit(200, "文件存在", [
                'path' => $videoPath,
                'size' => $fileSize,
                'mime_type' => $mimeType,
                'readable' => is_readable($videoPath)
            ]);
        } else {
            json_exit(404, "文件不存在: " . $videoPath);
        }
    }
}
