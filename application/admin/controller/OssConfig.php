<?php
namespace app\admin\controller;
use app\admin\controller\Base;

class OssConfig extends Base
{
    /**
     * OSS配置页面
     */
    public function index()
    {
        // 获取当前OSS配置
        $ossConfig = $this->SystemModel->getConfig("oss");
        
        // 如果没有配置，使用默认配置
        if (empty($ossConfig)) {
            $ossConfig = [
                'enable' => false,
                'provider' => 'aliyun',
                'aliyun' => [
                    'access_key_id' => '',
                    'access_key_secret' => '',
                    'endpoint' => '',
                    'bucket' => '',
                    'domain' => '',
                ],
                'tencent' => [
                    'secret_id' => '',
                    'secret_key' => '',
                    'region' => '',
                    'bucket' => '',
                    'domain' => '',
                ],
                'qiniu' => [
                    'access_key' => '',
                    'secret_key' => '',
                    'bucket' => '',
                    'domain' => '',
                ],
            ];
        }
        
        $this->assign('ossConfig', $ossConfig);
        return $this->fetch();
    }
    
    /**
     * 保存OSS配置
     */
    public function doSave()
    {
        $post = $this->request->param();
        
        // 验证必填字段
        if (isset($post['enable']) && $post['enable'] == '1') {
            $provider = $post['provider'];
            
            switch ($provider) {
                case 'aliyun':
                    if (empty($post['aliyun_access_key_id']) || empty($post['aliyun_access_key_secret']) || 
                        empty($post['aliyun_endpoint']) || empty($post['aliyun_bucket'])) {
                        json_exit(401, '阿里云OSS配置信息不完整！');
                    }
                    break;
                case 'tencent':
                    if (empty($post['tencent_secret_id']) || empty($post['tencent_secret_key']) || 
                        empty($post['tencent_region']) || empty($post['tencent_bucket'])) {
                        json_exit(401, '腾讯云COS配置信息不完整！');
                    }
                    break;
                case 'qiniu':
                    if (empty($post['qiniu_access_key']) || empty($post['qiniu_secret_key']) || 
                        empty($post['qiniu_bucket']) || empty($post['qiniu_domain'])) {
                        json_exit(401, '七牛云配置信息不完整！');
                    }
                    break;
            }
        }
        
        // 构建配置数组
        $ossConfig = [
            'enable' => isset($post['enable']) && $post['enable'] == '1',
            'provider' => $post['provider'] ?? 'aliyun',
            'aliyun' => [
                'access_key_id' => trim($post['aliyun_access_key_id'] ?? ''),
                'access_key_secret' => trim($post['aliyun_access_key_secret'] ?? ''),
                'endpoint' => trim($post['aliyun_endpoint'] ?? ''),
                'bucket' => trim($post['aliyun_bucket'] ?? ''),
                'domain' => trim($post['aliyun_domain'] ?? ''),
            ],
            'tencent' => [
                'secret_id' => trim($post['tencent_secret_id'] ?? ''),
                'secret_key' => trim($post['tencent_secret_key'] ?? ''),
                'region' => trim($post['tencent_region'] ?? ''),
                'bucket' => trim($post['tencent_bucket'] ?? ''),
                'domain' => trim($post['tencent_domain'] ?? ''),
            ],
            'qiniu' => [
                'access_key' => trim($post['qiniu_access_key'] ?? ''),
                'secret_key' => trim($post['qiniu_secret_key'] ?? ''),
                'bucket' => trim($post['qiniu_bucket'] ?? ''),
                'domain' => trim($post['qiniu_domain'] ?? ''),
            ],
        ];
        
        // 保存配置
        $result = $this->SystemModel->saveData($ossConfig, "oss");

        if ($result) {
            json_exit(200, 'OSS配置保存成功！');
        } else {
            json_exit(401, 'OSS配置保存失败！');
        }
    }
    
    /**
     * 测试OSS连接
     */
    public function testConnection()
    {
        // 添加调试信息
        error_log('testConnection called');

        $provider = $this->request->param('provider');
        $config = $this->request->param();

        // 记录接收到的参数
        error_log('Provider: ' . $provider);
        error_log('Config: ' . json_encode($config));
        
        try {
            switch ($provider) {
                case 'aliyun':
                    $this->testAliyunOss($config);
                    break;
                case 'tencent':
                    $this->testTencentCos($config);
                    break;
                case 'qiniu':
                    $this->testQiniuOss($config);
                    break;
                default:
                    json_exit(401, '不支持的OSS服务商');
            }
        } catch (\Exception $e) {
            json_exit(401, '连接测试失败：' . $e->getMessage());
        }
    }
    
    /**
     * 测试阿里云OSS连接
     */
    private function testAliyunOss($config)
    {
        $accessKeyId = $config['aliyun_access_key_id'] ?? '';
        $accessKeySecret = $config['aliyun_access_key_secret'] ?? '';
        $endpoint = $config['aliyun_endpoint'] ?? '';
        $bucket = $config['aliyun_bucket'] ?? '';

        if (empty($accessKeyId) || empty($accessKeySecret) || empty($endpoint) || empty($bucket)) {
            json_exit(401, '阿里云OSS配置信息不完整');
        }
        
        // 简单的连接测试 - 由于没有OSS SDK，这里只做基本验证
        try {
            // 验证endpoint格式
            // if (!filter_var($endpoint, FILTER_VALIDATE_URL)) {
            //     json_exit(401, 'Endpoint格式不正确');
            // }

            // // 验证必填字段
            // if (strlen($accessKeyId) < 10 || strlen($accessKeySecret) < 10) {
            //     json_exit(401, 'AccessKey格式不正确');
            // }

            // 如果有OSS SDK，可以在这里进行真实的连接测试
            $ossClient = new \OSS\OssClient($accessKeyId, $accessKeySecret, $endpoint);
            $exists = $ossClient->doesBucketExist($bucket);

            json_exit(200, '阿里云OSS配置验证通过！（注意：这是基础验证，实际连接需要OSS SDK）');

        } catch (\Exception $e) {
            json_exit(401, 'OSS连接测试失败：' . $e->getMessage());
        }
    }

    /**
     * 测试腾讯云COS连接
     */
    private function testTencentCos($config)
    {
        $secretId = $config['tencent_secret_id'] ?? '';
        $secretKey = $config['tencent_secret_key'] ?? '';
        $region = $config['tencent_region'] ?? '';
        $bucket = $config['tencent_bucket'] ?? '';

        if (empty($secretId) || empty($secretKey) || empty($region) || empty($bucket)) {
            json_exit(401, '腾讯云COS配置信息不完整');
        }

        // 简单的连接测试 - 由于没有COS SDK，这里只做基本验证
        try {
            // 验证必填字段
            if (strlen($secretId) < 10 || strlen($secretKey) < 10) {
                json_exit(401, 'SecretId或SecretKey格式不正确');
            }

            // 验证region格式
            if (!preg_match('/^[a-z]+-[a-z]+(-\d+)?$/', $region)) {
                json_exit(401, '地域格式不正确，例如：ap-beijing');
            }

            // 如果有COS SDK，可以在这里进行真实的连接测试
            // $cosClient = new Qcloud\Cos\Client(['region' => $region, 'credentials' => ['secretId' => $secretId, 'secretKey' => $secretKey]]);
            // $result = $cosClient->headBucket(['Bucket' => $bucket]);

            json_exit(200, '腾讯云COS配置验证通过！（注意：这是基础验证，实际连接需要COS SDK）');

        } catch (\Exception $e) {
            json_exit(401, 'COS连接测试失败：' . $e->getMessage());
        }
    }

    /**
     * 测试七牛云连接
     */
    private function testQiniuOss($config)
    {
        $accessKey = $config['qiniu_access_key'] ?? '';
        $secretKey = $config['qiniu_secret_key'] ?? '';
        $bucket = $config['qiniu_bucket'] ?? '';
        $domain = $config['qiniu_domain'] ?? '';

        if (empty($accessKey) || empty($secretKey) || empty($bucket) || empty($domain)) {
            json_exit(401, '七牛云配置信息不完整');
        }

        // 简单的连接测试 - 由于没有七牛云SDK，这里只做基本验证
        try {
            // 验证必填字段
            if (strlen($accessKey) < 10 || strlen($secretKey) < 10) {
                json_exit(401, 'AccessKey或SecretKey格式不正确');
            }

            // 验证域名格式
            if (!filter_var($domain, FILTER_VALIDATE_URL)) {
                json_exit(401, '访问域名格式不正确');
            }

            // 如果有七牛云SDK，可以在这里进行真实的连接测试
            // $auth = new \Qiniu\Auth($accessKey, $secretKey);
            // $bucketManager = new \Qiniu\Storage\BucketManager($auth);
            // $bucketManager->stat($bucket, 'test');

            json_exit(200, '七牛云配置验证通过！（注意：这是基础验证，实际连接需要七牛云SDK）');

        } catch (\Exception $e) {
            json_exit(401, '七牛云连接测试失败：' . $e->getMessage());
        }
    }

    /**
     * 简单的测试方法
     */
    public function test()
    {
        json_exit(200, '测试成功！路由工作正常');
    }
}
