<?php
namespace app\admin\controller;
use think\Request;
use think\Controller;
use app\admin\controller\Base;

class Company extends Base
{
    public function index()
    {
        // 获取公司简历配置
        $companyConfig = $this->SystemModel->getConfig("company");
        
        // 如果没有配置，设置默认值
        if (empty($companyConfig)) {
            $companyConfig = [
                'company_name' => '',
                'company_description' => '',
                'company_history' => '',
                'company_vision' => '',
                'company_mission' => '',
                'company_values' => '',
                'company_address' => '',
                'company_phone' => '',
                'company_email' => '',
                'company_website' => '',
                'company_logo' => '',
                'company_founded' => '',
                'company_employees' => '',
                'company_industry' => '',
            ];
        }
        
        $this->assign('companyConfig', $companyConfig);
        return $this->fetch();
    }

    public function doSave()
    {
        $post = $this->request->param();
        
        // 验证必填字段
        if (empty($post['company_name'])) {
            json_exit(401, '公司名称不能为空！');
        }
        
        // 构建配置数据
        $companyConfig = [
            'company_name' => trim($post['company_name']),
            'company_description' => trim($post['company_description'] ?? ''),
            'company_history' => trim($post['company_history'] ?? ''),
            'company_vision' => trim($post['company_vision'] ?? ''),
            'company_mission' => trim($post['company_mission'] ?? ''),
            'company_values' => trim($post['company_values'] ?? ''),
            'company_address' => trim($post['company_address'] ?? ''),
            'company_phone' => trim($post['company_phone'] ?? ''),
            'company_email' => trim($post['company_email'] ?? ''),
            'company_website' => trim($post['company_website'] ?? ''),
            'company_logo' => trim($post['company_logo'] ?? ''),
            'company_founded' => trim($post['company_founded'] ?? ''),
            'company_employees' => trim($post['company_employees'] ?? ''),
            'company_industry' => trim($post['company_industry'] ?? ''),
        ];
        
        // 保存配置
        $this->SystemModel->saveData($companyConfig, "company");
    }
    
    public function doupload(){
        $this->OssModel->upload_img("company/logo/");
    }
}
