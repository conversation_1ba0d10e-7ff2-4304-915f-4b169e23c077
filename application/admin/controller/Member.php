<?php
namespace app\admin\controller;
use app\admin\model\MemberModel;
use app\admin\model\UserModel;
use think\Request;
use think\Controller;
use app\admin\controller\Base;
class Member extends Base
{
    public function index()
    {   
        
        
        
        $uids = MemberModel::field(['uid'])->group('uid')->select()->toArray();
        $uis = array_column($uids,'uid');
        $userList = UserModel::field(['id','username'])->whereIn('id',$uis)->select();
        $this->assign('userlist',$userList);
       return $this->fetch();
    }
    
    
    
//保存
    public function doSave(){
        $post = $this->request->param();
        $this->MemberModel->saveData($post);
    }
    
    
    public function list(){
        $get['page'] = $this->request->get('page') ?: 1;
        $get['uid'] = $this->request->get('uid') ?: '';
        $get['ip'] = $this->request->get('ip') ?: '';
        $get['limit'] = $this->request->get('limit') ?: 10;
        $get['username'] = $this->request->get('username') ?: "";
        $get['status'] = $this->request->get('status') === "" ? "":$this->request->get('status');
        $get['start_time'] = $this->request->get('start_time') ?: "";
        $get['end_time'] = $this->request->get('end_time') ?: "";
        $list = $this->MemberModel->getList($get,$this->userinfo['rid'],$this->userinfo['id']);

        return json_table(0, "",$list['count'],$list['data']);
    }
    public function doEditStatus(){
        $post = $this->request->param();
        $this->MemberModel->editStatus($post);
    }
    public function doupload(){
        $this->OssModel->doupload("user/headimg/");
    }
    
    public function doAddMoney(){
        $post = $this->request->param();
        $this->MemberModel->addMoney($post);
    }       
    public function addmoney(){
        $id = $this->request->get('id') ?: "";
        $userinfo = $this->MemberModel->where(['id'=>$id])->find();
        if(empty($userinfo)){
            $this->error('用户信息不存在！');
        }else{
            $this->assign('info',$userinfo);
            return $this->fetch();
        }
    }
    public function addmoney2(){
        $id = $this->request->get('id') ?: "";
        $userinfo = $this->MemberModel->where(['id'=>$id])->find();
        if(empty($userinfo)){
            $this->error('用户信息不存在！');
        }else{
            $this->assign('info',$userinfo);
            return $this->fetch();
        }
    }
    public function operation($operation = null, $id = null){
        if(!empty($operation)){
            if($operation == "add"){

            } else {
                if(!empty($id) || $id == 0){
                   $info = $this->MemberModel->getOneData($id);
                   $this->assign('info',$info);
                }else{
                   $this->error('编辑错误ID不能为空！');
                }
            }
            $class = $this->UserModel->selectList(2);
            $this->assign('class',$class);

            // 获取VIP配置
            $vipConfig = $this->SystemModel->getConfig("vip");
            if (empty($vipConfig)) {
                // 如果没有配置，使用默认配置
                $vipConfig = [
                    'VIP0' => 0,   // 非会员
                    'VIP1' => 30,
                    'VIP2' => 60,
                    'VIP3' => 90,
                    'VIP4' => 180,
                    'VIP5' => 365,
                ];
            }
            $this->assign('vipConfig', $vipConfig);

            $this->assign('operation',$operation);
            return $this->fetch();
        } else {
            $this->error('操作类型错误！');
        }
    }
    
    
        
    // 删除
    public function doDel(){
        $post = $this->request->param();
        $this->MemberModel->delData($post['id']);
    }

    public function addAmountCode(){
        $id = $this->request->get('id') ?: "";
        $userinfo = $this->MemberModel->where(['id'=>$id])->find();
        if(empty($userinfo)){
            $this->error('用户信息不存在！');
        }else{
            $this->assign('info',$userinfo);
            return $this->fetch();
        }
    }
    public function doAddAmountCode(){
        $post = $this->request->param();
        $this->MemberModel->addAmountCode($post);
    }

    // 删除
    public function doTi(){
        $post = $this->request->param();
        $this->MemberModel->editTi($post);
    }
    
    // 拉黑ip
    public function pBlackIp(){
        $post = $this->request->param();
        $b_id=$this->BlackIpModel->where('value',$post['ip'])->value('id');
        if($b_id>0)  json_exit(200,'拉黑成功！');
        $this->BlackIpModel->save(['value'=>$post['ip'],'create_time'=>time()]);
        json_exit(200,'拉黑成功！');
    }
    
    // 解除ip
    public function jBlackIp(){
        $post = $this->request->param();
        $this->BlackIpModel->where('value',$post['ip'])->delete();
        json_exit(200,'解除成功！');
    }
}
