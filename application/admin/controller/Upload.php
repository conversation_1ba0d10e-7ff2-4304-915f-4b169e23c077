<?php
namespace app\admin\controller;
use app\admin\controller\Base;

class Upload extends Base
{
    /**
     * 通用文件上传接口
     */
    public function file()
    {
        $type = $this->request->param('type', 'image'); // 文件类型：image, video, document
        $path = $this->request->param('path', ''); // 上传路径
        
        // 默认路径配置
        $defaultPaths = [
            'image' => 'uploads/images/',
            'video' => 'uploads/videos/',
            'document' => 'uploads/documents/',
        ];
        
        if (empty($path)) {
            $path = $defaultPaths[$type] ?? 'uploads/';
        }
        
        // 添加日期子目录
        $path = rtrim($path, '/') . '/' . date('Y/m/d') . '/';
        
        return $this->OssModel->uploadFile($path, $type);
    }
    
    /**
     * 图片上传
     */
    public function image()
    {
        $path = $this->request->param('path', 'uploads/images/');
        $path = rtrim($path, '/') . '/' . date('Y/m/d') . '/';
        return $this->OssModel->uploadFile($path, 'image');
    }
    
    /**
     * 视频上传
     */
    public function video()
    {
        $path = $this->request->param('path', 'uploads/videos/');
        $path = rtrim($path, '/') . '/' . date('Y/m/d') . '/';
        return $this->OssModel->upload_video($path, 'video');
    }
    
    /**
     * 文档上传
     */
    public function document()
    {
        $path = $this->request->param('path', 'uploads/documents/');
        $path = rtrim($path, '/') . '/' . date('Y/m/d') . '/';
        return $this->OssModel->uploadFile($path, 'document');
    }
    
    /**
     * 获取上传配置信息
     */
    public function getConfig()
    {
        $ossConfig = config('oss');
        
        $config = [
            'oss_enabled' => $ossConfig['enable'] ?? false,
            'provider' => $ossConfig['provider'] ?? 'local',
            'max_size' => $ossConfig['max_size'] ?? [],
            'allowed_types' => $ossConfig['allowed_types'] ?? [],
        ];
        
        json_exit(200, '获取配置成功', $config);
    }
    
    /**
     * 批量上传
     */
    public function batch()
    {
        $files = request()->file('files');
        $type = $this->request->param('type', 'image');
        $path = $this->request->param('path', 'uploads/batch/');
        
        if (empty($files)) {
            json_exit(401, '未选择文件');
        }
        
        $results = [];
        $errors = [];
        
        foreach ($files as $index => $file) {
            try {
                // 临时设置单个文件到request
                request()->file = ['file' => $file];
                
                $result = $this->OssModel->uploadFile($path . date('Y/m/d') . '/', $type);
                $results[] = [
                    'index' => $index,
                    'success' => true,
                    'url' => $result['data'] ?? '',
                ];
            } catch (\Exception $e) {
                $errors[] = [
                    'index' => $index,
                    'error' => $e->getMessage(),
                ];
            }
        }
        
        json_exit(200, '批量上传完成', [
            'success_count' => count($results),
            'error_count' => count($errors),
            'results' => $results,
            'errors' => $errors,
        ]);
    }
}
