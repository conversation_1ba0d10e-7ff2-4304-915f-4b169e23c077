# 多语言国际化使用说明

## 概述
本项目已实现多语言国际化处理，支持韩语、中文、越南语等多种语言。

## 支持的语言
- `ko-kr`: 韩语 (默认)
- `zh-cn`: 中文简体
- `vi-vn`: 越南语
- `en-us`: 英语 (待添加)

## 使用方法

### 1. 在控制器中使用
```php
use app\common\Lang;

// 获取翻译文本
$message = Lang::get('auth_error');

// 带参数的翻译
$message = Lang::get('withdraw_amount_min_error', ['{min}' => 100]);

// 指定语言
$message = Lang::get('success', [], 'zh-cn');
```

### 2. 在API返回中使用
```php
// 使用新的多语言函数
json_exit_lang(200, 'register_success', $data);

// 带参数
json_exit_lang(401, 'withdraw_amount_min_error', [], ['{min}' => $role['min']]);
```

### 3. 语言检测
系统会按以下优先级检测语言：
1. HTTP请求头 `lang` 参数
2. POST参数 `lang`
3. GET参数 `lang`
4. 默认使用韩语 (`ko-kr`)

### 4. 客户端设置语言
```javascript
// 在请求头中设置
headers: {
    'lang': 'ko-kr'  // 或 'zh-cn', 'vi-vn'
}

// 或在POST/GET参数中
{
    "lang": "ko-kr",
    "other_params": "..."
}
```

## 语言包结构
每个语言包文件位于 `application/lang/` 目录下，格式为：
```php
<?php
return [
    'key' => '翻译文本',
    'key_with_param' => '包含{param}的文本',
    // ...
];
```

## 添加新语言
1. 在 `application/lang/` 目录下创建新的语言文件，如 `en-us.php`
2. 在 `Lang.php` 的 `$supportedLangs` 数组中添加新语言映射
3. 翻译所有必要的文本

## 常用语言键
- `auth_error`: 认证错误
- `network_error`: 网络异常
- `register_success`: 注册成功
- `login_success`: 登录成功
- `password_updated`: 密码更新成功
- `withdraw_success`: 提现成功
- `balance_insufficient`: 余额不足
- `bet_success`: 投注成功

## 注意事项
1. 所有新的错误信息都应该使用语言键，避免硬编码
2. 参数替换使用 `{param}` 格式
3. 语言键应该具有描述性，便于理解
4. 建议定期检查和更新语言包的完整性
