<?php
/**
 * 测试银行列表API接口
 * 访问地址：http://your-domain.com/test_bank_list_api.php
 */

// 测试API接口
function testBankListApi() {
    // 假设你的域名是 localhost 或者你的实际域名
    $baseUrl = 'http://localhost'; // 请根据实际情况修改
    
    // 测试获取银行列表的API
    $apiUrl = $baseUrl . '/api/system/getBankList';
    
    echo "<h2>测试银行列表API</h2>";
    echo "<p>API地址: {$apiUrl}</p>";
    
    // 使用curl发送请求
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $apiUrl);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    echo "<h3>响应结果:</h3>";
    echo "<p>HTTP状态码: {$httpCode}</p>";
    echo "<p>响应内容:</p>";
    echo "<pre>" . htmlspecialchars($response) . "</pre>";
    
    // 解析JSON响应
    $data = json_decode($response, true);
    if ($data && isset($data['data'])) {
        echo "<h3>银行列表:</h3>";
        echo "<table border='1' cellpadding='5' cellspacing='0'>";
        echo "<tr><th>ID</th><th>银行名称</th><th>银行代码</th><th>状态</th></tr>";
        
        $hasMercadoPago = false;
        foreach ($data['data'] as $bank) {
            $bankName = isset($bank['text']) ? $bank['text'] : (isset($bank['name']) ? $bank['name'] : 'Unknown');
            $bankValue = isset($bank['value']) ? $bank['value'] : (isset($bank['code']) ? $bank['code'] : 'N/A');
            $bankId = isset($bank['id']) ? $bank['id'] : 'N/A';
            $bankStatus = isset($bank['status']) ? $bank['status'] : 'N/A';
            
            if ($bankName === 'Mercado Pago') {
                $hasMercadoPago = true;
                echo "<tr style='background-color: #d4edda;'>";
            } else {
                echo "<tr>";
            }
            
            echo "<td>{$bankId}</td>";
            echo "<td>{$bankName}</td>";
            echo "<td>{$bankValue}</td>";
            echo "<td>{$bankStatus}</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        echo "<h3>验证结果:</h3>";
        if ($hasMercadoPago) {
            echo "<p style='color: green;'>✅ Mercado Pago 已成功添加到银行列表中！</p>";
        } else {
            echo "<p style='color: red;'>❌ Mercado Pago 未在银行列表中找到</p>";
        }
        
        echo "<p>总银行数量: " . count($data['data']) . "</p>";
    }
}

// 执行测试
testBankListApi();

echo "<hr>";
echo "<h2>使用说明</h2>";
echo "<p>现在 <strong>/api/system/getBankList</strong> 接口会自动包含 Mercado Pago 银行。</p>";
echo "<p>如果数据库中没有 Mercado Pago，系统会动态添加到返回结果中。</p>";

echo "<h3>银行列表包含:</h3>";
echo "<ul>";
echo "<li>BBVA México</li>";
echo "<li>Banamex</li>";
echo "<li>Banorte</li>";
echo "<li>Santander México</li>";
echo "<li>HSBC México</li>";
echo "<li>Banco Azteca</li>";
echo "<li><strong>Mercado Pago</strong> (新增)</li>";
echo "</ul>";

echo "<h3>API响应格式:</h3>";
echo "<pre>{
    \"code\": 200,
    \"msg\": \"获取成功\",
    \"data\": [
        {
            \"id\": 1,
            \"value\": \"BBVA_MX\",
            \"text\": \"BBVA México\",
            \"status\": 1
        },
        ...
        {
            \"id\": 999,
            \"value\": \"MERCADO_PAGO\",
            \"text\": \"Mercado Pago\",
            \"status\": 1
        }
    ]
}</pre>";
?>
