# 大文件上传配置说明

## 问题描述
当上传大于默认限制的视频文件时，可能会遇到 "413 Content Too Large" 错误。这个错误通常是由于Web服务器和PHP的配置限制引起的。

## 解决方案

### 1. PHP配置 (已自动配置)
我们已经在 `public/.user.ini` 文件中添加了以下配置：
```ini
upload_max_filesize = 2048M    # 单个文件最大上传大小
post_max_size = 2048M          # POST数据最大大小
max_execution_time = 300       # 脚本最大执行时间(秒)
max_input_time = 300           # 输入解析时间(秒)
memory_limit = 512M            # 内存限制
```

### 2. Web服务器配置

#### 如果您使用的是 Nginx：
请在您的Nginx站点配置文件中添加以下配置：
```nginx
server {
    # 其他配置...
    
    # 设置客户端请求体的最大大小
    client_max_body_size 2048M;
    
    # 设置超时时间
    client_body_timeout 300s;
    client_header_timeout 300s;
    send_timeout 300s;
    
    # 其他配置...
}
```

#### 如果您使用的是 Apache：
我们已经在 `public/.htaccess` 文件中添加了相应配置。

### 3. 重启服务
配置完成后，请重启您的Web服务器：
- Nginx: `sudo systemctl restart nginx`
- Apache: `sudo systemctl restart apache2` 或 `sudo systemctl restart httpd`

### 4. 验证配置
您可以创建一个PHP文件来检查当前配置：
```php
<?php
echo "upload_max_filesize: " . ini_get('upload_max_filesize') . "\n";
echo "post_max_size: " . ini_get('post_max_size') . "\n";
echo "max_execution_time: " . ini_get('max_execution_time') . "\n";
echo "memory_limit: " . ini_get('memory_limit') . "\n";
?>
```

### 5. 常见问题

#### 如果仍然遇到413错误：
1. 检查您的Web服务器错误日志
2. 确认您有权限修改服务器配置
3. 联系您的主机提供商，他们可能需要在服务器级别进行配置

#### 如果是共享主机：
- 联系主机提供商请求增加上传限制
- 或者考虑使用云存储服务（OSS）来处理大文件上传

### 6. 应用程序配置
应用程序层面的配置已经完成：
- 前端JavaScript: 支持2GB文件上传
- 后端PHP: 支持2GB文件验证
- 配置文件: 已设置2GB限制

## 注意事项
- 大文件上传会消耗更多服务器资源
- 建议在生产环境中监控服务器性能
- 考虑使用进度条来改善用户体验
- 对于超大文件，建议使用分片上传技术
