<?php
/**
 * 完全复制eppsost.php demo的所有逻辑和细节
 */

// 完全复制demo的curl_request函数，包括拼写错误
function curl_request($url, $data=null, $method='psot', $header = array("content-type: application/json"), $https=true, $timeout = 5){
        $method = strtoupper($method);
        $ch = curl_init();//初始化
        curl_setopt($ch, CURLOPT_URL, $url);//访问的URL
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);//只获取页面内容，但不输出
        if($https){
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);//https请求 不验证证书
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);//https请求 不验证HOST
        }
        if ($method != "GET") {
            if($method == 'POST'){
                curl_setopt($ch, CURLOPT_POST, true);//请求方式为post请求
            }
            if ($method == 'PUT' || strtoupper($method) == 'DELETE') {
                curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $method); //设置请求方式
            }
            curl_setopt($ch, CURLOPT_POSTFIELDS, $data);//请求数据
        }
        curl_setopt($ch, CURLOPT_TIMEOUT, $timeout);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $header); //模拟的header头
        //curl_setopt($ch, CURLOPT_HEADER, false);//设置不需要头信息
        $result = curl_exec($ch);//执行请求
        curl_close($ch);//关闭curl，释放资源
        return $result;
    }

// 完全复制demo的token函数
function token($length){
 $str = md5(time());
 $token = substr($str,15,$length);
 return $token;
}//随机数生成函数

echo "=== 完全复制eppsost.php demo的所有逻辑 ===\n\n";

// 完全按照demo的变量定义和注释
$amount = (double)100.5;
$notify_url='https://jsdao.cc/api/callback/pay?gateway=Epusdt';//Epusdt的异步回调地址，随便，无需管理的话
$redirect_url='https://jsdao.cc/api/my/index';//Epusdt的同步跳转地址,付款成功后跳转到这里
$order_id=(string)token(10);//生成随机数用于订单号
$key='B81BA11A6528EC298C7DD88C144B8D882568BC1D';//Epusdt的自定义密钥

echo "测试参数:\n";
echo "amount: " . $amount . "\n";
echo "notify_url: " . $notify_url . "\n";
echo "redirect_url: " . $redirect_url . "\n";
echo "order_id: " . $order_id . "\n";
echo "key: " . $key . "\n\n";

// 完全按照demo的拼接方式和注释
$str = 'amount='.$amount.'&notify_url='.$notify_url.'&order_id='.$order_id.'&redirect_url='.$redirect_url.$key;//拼接字符串用于MD5计算
echo "签名字符串: " . $str . "\n";

$signature = md5($str);//用MD5算法计算签名
echo "MD5签名: " . $signature . "\n\n";

// 完全按照demo的数据包生成方式和注释
$data=json_encode(array( 'order_id' => $order_id,//生成数据包，用到了的数组转json的jsonencode
'amount' => $amount,
'notify_url' => $notify_url,
'redirect_url' => $redirect_url,
'signature' => $signature));

echo "请求数据包:\n";
echo $data . "\n\n";

// 完全按照demo的请求方式和注释
$res=curl_request('https://pay.jsdao.cc/api/v1/order/create-transaction',$data,'post');//发起Curl请求并获取返回数据到变量

echo "原始响应:\n";
echo $res . "\n\n";

// 完全按照demo的响应处理和注释
$arr = json_decode($res, true);//对返回数据进行json到数组的转换，用到了jsondecode

if ($arr && isset($arr['data'])) {
    $resdata=$arr['data'];//提取返回数据的数组中的data段落
    $payurl= $resdata['payment_url'];//提取返回数据的数组中的data段落中的支付链接
    $payamount=$resdata['actual_amount'];//提取返回数据的数组中的data段落中的转换后数值

    // 完全按照demo的输出方式
    echo '你的支付链接是 ';
    echo $payurl;
    echo '<br/>你的计划支付金额是';
    echo $amount;
    echo 'CNY';
    echo '<br/>你的实际交易是';
    echo $payamount;
    echo 'USD';
    echo '<br/>若上述为空则你没有填URL中的?n=*的参数且缺少Post数据';
    echo '<br/>本站基准汇率为1USD=6.25CNY。请勿使用ERC/DEX/BSC进行转账发起。';//部分交易所的地址有黑名单机制，用钱包可解。汇率在Epusdt那边设置。
    echo '<br/>为了辨识客户订单，请注意实际金额中的尾数不可忽略';//Epusdt使用了尾数来确认订单。注意本版本对接的是V0.0.1的Epusdt
    echo '<br/><a href="'.$payurl.'">请点击这里跳转到支付页面</a>';
    
    echo "\n\n✅ 创建支付订单成功！\n";
} else {
    echo "❌ 响应数据格式错误或请求失败\n";
    echo "完整响应: " . $res . "\n";
}

// 仅供学习PHP对JSON的处理和PHP对接HTTP API的应用，请在24小时内删除。
// 不得用于中国大陆或其他限制加密货币的地区，不得用于企业、商业用途。
// 不得违反PRC、USA的相关法律法规。

echo "\n=== 测试数据（用于Postman） ===\n";
echo "URL: https://pay.jsdao.cc/api/v1/order/create-transaction\n";
echo "Method: POST\n";
echo "Content-Type: application/json\n";
echo "Body:\n";
echo $data . "\n";

?>
