-- 添加 Mercado Pago 银行到 bank_list 表
-- 执行此SQL来添加 Mercado Pago 银行

-- 1. 查看当前 bank_list 表结构
-- DESCRIBE `bank_list`;

-- 2. 查看当前数据（了解现有银行）
-- SELECT * FROM `bank_list` WHERE `status` = 1 ORDER BY `id`;

-- 3. 添加 Mercado Pago 银行
-- 根据现有的墨西哥银行列表，添加 Mercado Pago
INSERT INTO `bank_list` (`value`, `text`, `status`) VALUES ('MERCADO_PAGO', 'Mercado Pago', 1);

-- 如果表结构不同，可以尝试以下几种变体：

-- 变体1：如果字段名是 name 而不是 text
-- INSERT INTO `bank_list` (`value`, `name`, `status`) VALUES ('MERCADO_PAGO', 'Mercado Pago', 1);

-- 变体2：如果没有 value 字段，只有 name/text 字段
-- INSERT INTO `bank_list` (`text`, `status`) VALUES ('Mercado Pago', 1);
-- 或者
-- INSERT INTO `bank_list` (`name`, `status`) VALUES ('Mercado Pago', 1);

-- 变体3：如果有时间戳字段
-- INSERT INTO `bank_list` (`value`, `text`, `status`, `create_time`, `update_time`) VALUES ('MERCADO_PAGO', 'Mercado Pago', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

-- 4. 验证添加结果
SELECT * FROM `bank_list` WHERE `text` = 'Mercado Pago' OR `name` = 'Mercado Pago';

-- 5. 查看所有启用的银行
SELECT * FROM `bank_list` WHERE `status` = 1 ORDER BY `id`;

-- 注意：
-- 1. 请根据实际的表结构选择合适的INSERT语句
-- 2. 如果需要同时更新 bank 表（用于图标等），请执行以下语句：

-- 添加到 bank 表（如果需要）
-- INSERT INTO `bank` (`title`, `name`, `status`) VALUES ('Mercado Pago', 'Mercado Pago', 1);
-- 或者带图标的版本：
-- INSERT INTO `bank` (`title`, `name`, `thumb`, `status`) VALUES ('Mercado Pago', 'Mercado Pago', '/static/images/banks/mercadopago.png', 1);

-- 执行完成后，API /api/system/getBankList 将返回包含 Mercado Pago 在内的银行列表
