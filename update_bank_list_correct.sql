-- 根据实际表结构更新 bank_list 表
-- 表字段：id, value(银行编号), text(银行名称), status(状态)

-- 1. 禁用所有现有银行
UPDATE `bank_list` SET `status` = 0;

-- 2. 插入墨西哥银行数据
INSERT INTO `bank_list` (`value`, `text`, `status`) VALUES
('BBVA_MX', 'BBVA México', 1),
('BANAMEX', 'Banamex', 1),
('BANORTE', 'Banorte', 1),
('SANTANDER_MX', 'Santander México', 1),
('HSBC_MX', 'HSBC México', 1),
('BANCO_AZTECA', 'Banco Azteca', 1);

-- 3. 验证结果
SELECT * FROM `bank_list` WHERE `status` = 1 ORDER BY `id`;

-- 4. 查看所有数据（包括禁用的）
SELECT `id`, `value`, `text`, `status` FROM `bank_list` ORDER BY `status` DESC, `id`;
