<?php
/**
 * 添加 Mercado Pago 银行到数据库
 * 访问地址：http://your-domain.com/add_mercado_pago.php
 * 执行一次后请删除此文件
 */

// 引入ThinkPHP框架
require_once __DIR__ . '/thinkphp/start.php';

use think\Db;

try {
    echo "<h2>添加 Mercado Pago 银行</h2>";
    
    // 1. 查看当前 bank_list 表结构
    echo "<h3>1. 查看表结构</h3>";
    $columns = Db::query("DESCRIBE `bank_list`");
    echo "<pre>";
    print_r($columns);
    echo "</pre>";
    
    // 2. 查看当前数据
    echo "<h3>2. 当前银行列表</h3>";
    $currentBanks = Db::name('bank_list')->where('status', 1)->select();
    echo "<pre>";
    print_r($currentBanks);
    echo "</pre>";
    
    // 3. 检查 Mercado Pago 是否已存在
    $existingMercadoPago = Db::name('bank_list')
        ->where('text', 'Mercado Pago')
        ->whereOr('name', 'Mercado Pago')
        ->find();
    
    if ($existingMercadoPago) {
        echo "<p style='color: orange;'>Mercado Pago 已存在，更新状态为启用</p>";
        $result = Db::name('bank_list')
            ->where('id', $existingMercadoPago['id'])
            ->update(['status' => 1]);
        echo "<p>更新结果: " . ($result ? "成功" : "失败") . "</p>";
    } else {
        echo "<h3>3. 添加 Mercado Pago</h3>";
        
        // 根据表结构动态构建插入数据
        $insertData = ['status' => 1];
        
        // 检查字段并设置相应的值
        $hasValueField = false;
        $hasTextField = false;
        $hasNameField = false;
        
        foreach ($columns as $column) {
            switch ($column['Field']) {
                case 'value':
                    $insertData['value'] = 'MERCADO_PAGO';
                    $hasValueField = true;
                    break;
                case 'text':
                    $insertData['text'] = 'Mercado Pago';
                    $hasTextField = true;
                    break;
                case 'name':
                    $insertData['name'] = 'Mercado Pago';
                    $hasNameField = true;
                    break;
                case 'create_time':
                    $insertData['create_time'] = time();
                    break;
                case 'update_time':
                    $insertData['update_time'] = time();
                    break;
            }
        }
        
        echo "<p>准备插入的数据:</p>";
        echo "<pre>";
        print_r($insertData);
        echo "</pre>";
        
        // 执行插入
        $result = Db::name('bank_list')->insert($insertData);
        
        if ($result) {
            echo "<p style='color: green;'>✅ Mercado Pago 添加成功！</p>";
        } else {
            echo "<p style='color: red;'>❌ Mercado Pago 添加失败！</p>";
        }
    }
    
    // 4. 验证结果
    echo "<h3>4. 验证结果</h3>";
    $updatedBanks = Db::name('bank_list')->where('status', 1)->order('id', 'asc')->select();
    echo "<p>当前启用的银行数量: " . count($updatedBanks) . "</p>";
    echo "<pre>";
    foreach ($updatedBanks as $bank) {
        $bankName = isset($bank['text']) ? $bank['text'] : (isset($bank['name']) ? $bank['name'] : 'Unknown');
        echo "ID: {$bank['id']}, 银行: {$bankName}, 状态: {$bank['status']}\n";
    }
    echo "</pre>";
    
    // 5. 测试API接口
    echo "<h3>5. 测试API接口</h3>";
    echo "<p>现在可以访问 <a href='/api/system/getBankList' target='_blank'>/api/system/getBankList</a> 查看结果</p>";
    
    echo "<hr>";
    echo "<p style='color: red;'><strong>重要：执行完成后请删除此文件！</strong></p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>错误: " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>
