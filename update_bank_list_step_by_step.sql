-- 逐步更新 bank_list 表 - 单条执行版本

-- 步骤1：查看当前数据
SELECT * FROM `bank_list`;

-- 步骤2：禁用所有现有银行
UPDATE `bank_list` SET `status` = 0;

-- 步骤3：逐个插入墨西哥银行（单条执行，更安全）
INSERT INTO `bank_list` (`value`, `text`, `status`) VALUES ('BBVA_MX', 'BBVA México', 1);

INSERT INTO `bank_list` (`value`, `text`, `status`) VALUES ('BANAMEX', 'Banamex', 1);

INSERT INTO `bank_list` (`value`, `text`, `status`) VALUES ('BANORTE', 'Banorte', 1);

INSERT INTO `bank_list` (`value`, `text`, `status`) VALUES ('SANTANDER_MX', 'Santander México', 1);

INSERT INTO `bank_list` (`value`, `text`, `status`) VALUES ('HSBC_MX', 'HSBC México', 1);

INSERT INTO `bank_list` (`value`, `text`, `status`) VALUES ('BANCO_AZTECA', 'Banco Azteca', 1);

-- 步骤4：验证结果
SELECT * FROM `bank_list` WHERE `status` = 1 ORDER BY `id`;

-- 步骤5：检查总数
SELECT 
    COUNT(*) as total_banks,
    SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as active_banks,
    SUM(CASE WHEN status = 0 THEN 1 ELSE 0 END) as inactive_banks
FROM `bank_list`;

-- 预期结果：应该有6家启用的墨西哥银行
-- 1. BBVA México (BBVA_MX)
-- 2. Banamex (BANAMEX)  
-- 3. Banorte (BANORTE)
-- 4. Santander México (SANTANDER_MX)
-- 5. HSBC México (HSBC_MX)
-- 6. Banco Azteca (BANCO_AZTECA)
