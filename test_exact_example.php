<?php
/**
 * 完全按照您提供的示例代码进行测试
 */

echo "=== 完全按照您的示例代码测试 ===\n\n";

function curl_request($url, $data=null, $method='post', $header = array("content-type: application/json"), $https=true, $timeout = 5){
    $method = strtoupper($method);
    $ch = curl_init();//初始化
    curl_setopt($ch, CURLOPT_URL, $url);//访问的URL
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);//只获取页面内容，但不输出
    if($https){
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);//https请求 不验证证书
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);//https请求 不验证HOST
    }
    if ($method != "GET") {
        if($method == 'POST'){
            curl_setopt($ch, CURLOPT_POST, true);//请求方式为post请求
        }
        if ($method == 'PUT' || strtoupper($method) == 'DELETE') {
            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $method); //设置请求方式
        }
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);//请求数据
    }
    curl_setopt($ch, CURLOPT_TIMEOUT, $timeout);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $header); //模拟的header头
    $result = curl_exec($ch);//执行请求
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);//关闭curl，释放资源
    return "HTTP " . $httpCode . " | " . $result;
}

function token($length){
    $str = md5(time());
    $token = substr($str,15,$length);
    return $token;
}

// 测试参数
$amount = 100.5;
$notify_url = 'https://jsdao.cc/api/callback/pay?gateway=Epusdt';
$redirect_url = 'https://jsdao.cc/api/my/index';
$order_id = 'TEST_ORDER_' . time();
$key = 'B81BA11A6528EC298C7DD88C144B8D882568BC1D';

// 完全按照您的示例代码拼接字符串
$str = 'amount='.$amount.'&notify_url='.$notify_url.'&order_id='.$order_id.'&redirect_url='.$redirect_url.$key;
$signature = md5($str);

echo "测试参数:\n";
echo "amount: " . $amount . "\n";
echo "notify_url: " . $notify_url . "\n";
echo "redirect_url: " . $redirect_url . "\n";
echo "order_id: " . $order_id . "\n";
echo "key: " . $key . "\n\n";

echo "签名字符串: " . $str . "\n";
echo "MD5签名: " . $signature . "\n\n";

// 构建数据包
$data = json_encode(array(
    'order_id' => $order_id,
    'amount' => $amount,
    'notify_url' => $notify_url,
    'redirect_url' => $redirect_url,
    'signature' => $signature
));

echo "请求数据包:\n";
echo $data . "\n\n";

// 发送请求
$res = curl_request('https://pay.jsdao.cc/api/v1/order/create-transaction', $data, 'post');
echo "响应结果:\n";
echo $res . "\n\n";

// 额外测试：尝试不同的签名方式
echo "=== 测试不同的签名方式 ===\n";

// 方式1：按照官方文档的ksort方式
$test_data = [
    'order_id' => $order_id,
    'amount' => $amount,
    'notify_url' => $notify_url,
    'redirect_url' => $redirect_url
];

ksort($test_data);
$sign_string = '';
foreach ($test_data as $k => $v) {
    if ($v === '' || $v === null) continue;
    if ($sign_string !== '') $sign_string .= '&';
    $sign_string .= $k . '=' . $v;
}
$sign_string .= $key;
$official_signature = strtolower(md5($sign_string));

echo "官方文档方式:\n";
echo "签名字符串: " . $sign_string . "\n";
echo "MD5签名: " . $official_signature . "\n";

$official_data = json_encode(array(
    'order_id' => $order_id,
    'amount' => $amount,
    'notify_url' => $notify_url,
    'redirect_url' => $redirect_url,
    'signature' => $official_signature
));

echo "请求数据: " . $official_data . "\n";
$official_res = curl_request('https://pay.jsdao.cc/api/v1/order/create-transaction', $official_data, 'post');
echo "响应结果: " . $official_res . "\n\n";

// 比较两种签名
echo "=== 签名比较 ===\n";
echo "示例代码签名: " . $signature . "\n";
echo "官方文档签名: " . $official_signature . "\n";
echo "签名是否相同: " . ($signature === $official_signature ? '是' : '否') . "\n";

?>
