<?php
/**
 * 检查EPUSDT配置
 */

// 引入必要的文件
require_once 'thinkphp/base.php';
require_once 'thinkphp/convention.php';

// 数据库配置
$config = [
    'type' => 'mysql',
    'hostname' => '127.0.0.1',
    'database' => 'tcyp',
    'username' => 'root',
    'password' => '',
    'hostport' => '3306',
    'charset' => 'utf8'
];

try {
    $pdo = new PDO(
        "mysql:host={$config['hostname']};port={$config['hostport']};dbname={$config['database']};charset={$config['charset']}",
        $config['username'],
        $config['password'],
        [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
    );

    echo "=== EPUSDT配置检查 ===\n";

    // 检查系统配置表
    $stmt = $pdo->query("SELECT * FROM system WHERE name = 'epusdt'");
    $epusdt_config = $stmt->fetch(PDO::FETCH_ASSOC);

    if ($epusdt_config) {
        echo "✅ 找到EPUSDT配置\n";
        $config_data = json_decode($epusdt_config['value'], true);
        echo "配置内容:\n";
        echo "- API URL: " . ($config_data['api_url'] ?? '未设置') . "\n";
        echo "- API Key: " . (isset($config_data['api_key']) ? substr($config_data['api_key'], 0, 10) . '...' : '未设置') . "\n";
        echo "- 启用状态: " . ($config_data['enable'] ?? '未设置') . "\n";
    } else {
        echo "❌ 未找到EPUSDT配置\n";
    }

    // 检查钱包地址配置
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM wallet_address WHERE status = 1");
    $wallet_count = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "\n钱包地址数量: " . $wallet_count['count'] . "\n";

    if ($wallet_count['count'] > 0) {
        $stmt = $pdo->query("SELECT * FROM wallet_address WHERE status = 1 LIMIT 3");
        $wallets = $stmt->fetchAll(PDO::FETCH_ASSOC);
        echo "钱包地址示例:\n";
        foreach ($wallets as $wallet) {
            echo "- " . $wallet['token'] . "\n";
        }
    }

    // 检查充值记录
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM recharge WHERE status = 1");
    $pending_count = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "\n待处理充值订单: " . $pending_count['count'] . "\n";

    $stmt = $pdo->query("SELECT COUNT(*) as count FROM recharge WHERE status = 2");
    $success_count = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "成功充值订单: " . $success_count['count'] . "\n";

    // 检查最近的充值记录
    $stmt = $pdo->query("SELECT * FROM recharge ORDER BY create_time DESC LIMIT 5");
    $recent_orders = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($recent_orders)) {
        echo "\n最近的充值记录:\n";
        foreach ($recent_orders as $order) {
            echo "- 订单号: {$order['order_no']}, 金额: {$order['money']}, 状态: {$order['status']}, 时间: " . date('Y-m-d H:i:s', $order['create_time']) . "\n";
        }
    }

    echo "\n=== 建议的解决方案 ===\n";
    echo "1. 确保EPUSDT系统配置了正确的回调URL: https://jsdao.cc/api/callback/pay?gateway=Epusdt\n";
    echo "2. 检查服务器防火墙是否允许EPUSDT服务器访问\n";
    echo "3. 确保SSL证书配置正确\n";
    echo "4. 检查EPUSDT系统的日志，看是否有回调发送记录\n";
    echo "5. 可以先使用测试页面验证: https://jsdao.cc/test_epusdt_callback_simple.php\n";

} catch (Exception $e) {
    echo "数据库连接失败: " . $e->getMessage() . "\n";
}
?>
