# APP下载链接功能实现说明

## 功能概述
在基本配置中添加了APP下载链接的配置项，并提供API接口供前端获取该链接。

## 实现的功能

### 1. 后台管理配置
- **位置**: 系统管理 -> 基本配置
- **新增字段**: APP下载链接
- **功能**: 管理员可以在后台配置APP的下载链接

### 2. API接口
- **接口地址**: `/api/system/getAppDownloadUrl`
- **请求方式**: GET
- **功能**: 前端可以通过此接口获取APP下载链接

## 修改的文件

### 1. 后台管理页面
**文件**: `application/admin/view/system/index.html`
- 在基本配置表单中添加了APP下载链接的输入框
- 位置：在"是否启用首页弹窗"配置项之后

```html
<div class="form-group">
    <label>
        APP下载链接
    </label>
    <input class="form-control" type="text" id="app_download_url" name="app_download_url"
        placeholder="请输入APP下载链接" value="{:isset($base.app_download_url)?$base.app_download_url:''}">
    <small class="help-block">用户下载APP的链接地址</small>
</div>
```

### 2. API控制器
**文件**: `application/api/controller/System.php`
- 新增了 `getAppDownloadUrl()` 方法
- 返回JSON格式的APP下载链接数据

```php
/**
 * 获取APP下载链接
 */
public function getAppDownloadUrl(){
    $data = $this->SystemModel->getConfig("base");
    
    $appDownloadUrl = isset($data['app_download_url']) ? $data['app_download_url'] : '';
    
    return json_exit_lang(200,'info_get_success',['app_download_url' => $appDownloadUrl]);
}
```

## 使用方法

### 后台配置
1. 登录后台管理系统
2. 进入 **系统管理** -> **基本配置**
3. 找到 **APP下载链接** 配置项
4. 输入APP的下载链接（例如：`https://example.com/app.apk`）
5. 点击 **保存** 按钮

### 前端调用API
```javascript
// 获取APP下载链接
async function getAppDownloadUrl() {
    try {
        const response = await fetch('/api/system/getAppDownloadUrl');
        const data = await response.json();
        
        if (data.code === 200) {
            const downloadUrl = data.data.app_download_url;
            if (downloadUrl) {
                // 使用下载链接
                window.location.href = downloadUrl;
            } else {
                console.log('暂未配置APP下载链接');
            }
        } else {
            console.error('获取下载链接失败：', data.msg);
        }
    } catch (error) {
        console.error('网络错误：', error.message);
    }
}
```

### API响应格式
```json
{
    "code": 200,
    "msg": "获取成功",
    "data": {
        "app_download_url": "https://example.com/app.apk"
    }
}
```

## 数据存储
- APP下载链接配置存储在 `system` 表中
- `key` 字段值为 `base`
- `value` 字段为JSON格式，包含 `app_download_url` 字段

## 测试文件
项目中包含了以下测试文件：
- `test_app_download_api.php` - API接口测试脚本
- `frontend_example.html` - 前端使用示例

## 注意事项
1. 如果未配置APP下载链接，API会返回空字符串
2. 前端应该检查返回的链接是否为空，并给用户适当的提示
3. 建议在配置链接时验证URL的有效性
4. 支持各种格式的下载链接（APK文件、应用商店链接等）

## 兼容性
- 与现有系统完全兼容
- 不影响其他功能的正常使用
- 使用现有的配置存储机制
