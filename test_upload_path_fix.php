<?php
/**
 * 测试上传路径修复
 */

echo "=== 上传路径修复测试 ===\n\n";

// 1. 模拟修复后的逻辑
echo "1. 修复后的路径处理:\n";

// System控制器现在传递的路径
$systemPath = "public/base/ico/" . date('Y/m/d') . '/';
echo "System控制器传递的路径: $systemPath\n";

// OssModel直接使用这个路径
$ossPath = $systemPath;
echo "OssModel使用的路径: $ossPath\n";

// 检查路径
echo "路径是否存在: " . (is_dir($ossPath) ? "是" : "否") . "\n";

if (!is_dir($ossPath)) {
    echo "创建目录...\n";
    if (mkdir($ossPath, 0755, true)) {
        echo "目录创建成功\n";
    } else {
        echo "目录创建失败\n";
    }
}

echo "目录是否可写: " . (is_writable($ossPath) ? "是" : "否") . "\n";

// 2. 模拟文件保存
echo "\n2. 模拟文件保存:\n";
$testFileName = date('YmdHis') . '_fix_test.jpg';
$testFilePath = $ossPath . '/' . $testFileName;

// 创建测试文件
file_put_contents($testFilePath, 'test content for path fix');
echo "测试文件创建: $testFilePath\n";
echo "文件是否存在: " . (file_exists($testFilePath) ? "是" : "否") . "\n";

// 3. 生成URL路径
echo "\n3. URL路径生成:\n";
$urlPath = str_replace('public/', '', $ossPath);
$relativePath = '/' . trim($urlPath, '/') . '/' . $testFileName;
echo "生成的URL路径: $relativePath\n";
echo "对应的完整URL: https://jsdao.cc$relativePath\n";

// 4. 验证文件访问
echo "\n4. 文件访问验证:\n";
$publicFilePath = str_replace('public/', '', $testFilePath);
echo "public目录下的相对路径: $publicFilePath\n";

// 检查文件是否可以通过public路径访问
$webAccessPath = "public/" . ltrim($publicFilePath, '/');
echo "Web访问路径: $webAccessPath\n";
echo "Web路径文件存在: " . (file_exists($webAccessPath) ? "是" : "否") . "\n";

// 5. 清理测试文件
if (file_exists($testFilePath)) {
    unlink($testFilePath);
    echo "测试文件已清理\n";
}

// 6. 检查之前错误保存的文件
echo "\n5. 检查之前错误保存的文件:\n";
$wrongPath = "public/public/base/ico/20250827/";
if (is_dir($wrongPath)) {
    $wrongFiles = glob($wrongPath . "*");
    echo "错误路径文件数量: " . count($wrongFiles) . "\n";
    
    if (!empty($wrongFiles)) {
        echo "建议：将这些文件移动到正确位置\n";
        $correctPath = "public/base/ico/20250827/";
        
        if (!is_dir($correctPath)) {
            mkdir($correctPath, 0755, true);
            echo "已创建正确目录: $correctPath\n";
        }
        
        foreach ($wrongFiles as $wrongFile) {
            $fileName = basename($wrongFile);
            $correctFile = $correctPath . $fileName;
            echo "建议移动: $wrongFile -> $correctFile\n";
        }
    }
}

echo "\n=== 修复测试完成 ===\n";
echo "\n总结:\n";
echo "✓ System控制器传递: public/base/ico/日期/\n";
echo "✓ OssModel直接使用该路径\n";
echo "✓ 文件保存到: public/base/ico/日期/文件名\n";
echo "✓ URL生成: /base/ico/日期/文件名\n";
echo "✓ 访问地址: https://jsdao.cc/base/ico/日期/文件名\n";
?>
