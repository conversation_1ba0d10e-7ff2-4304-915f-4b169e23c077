# 前端APP调用EPUSDT充值接口示例

## 1. 获取充值配置

**接口：** `GET /api/recharge/getRechargeConfig`

**请求头：**
```
Content-Type: application/json
token: base64编码的用户ID
```

**响应示例：**
```json
{
    "code": 200,
    "message": "获取信息成功",
    "data": {
        "min_amount": 10,
        "max_amount": 10000,
        "payment_methods": [
            {
                "code": "epusdt",
                "name": "USDT支付",
                "icon": "/static/images/payment/usdt.png",
                "enabled": true
            }
        ]
    }
}
```

## 2. 创建充值订单

**接口：** `POST /api/recharge/createOrder`

**请求头：**
```
Content-Type: application/json
token: base64编码的用户ID
```

**请求参数：**
```json
{
    "amount": 100.5,
    "pay_way": "Epusdt"
}
```

**响应示例：**
```json
{
    "code": 200,
    "message": "充值成功",
    "data": {
        "pay_url": "https://pay.jsdao.cc/pay/checkout-counter/202506291751150897818610"
    }
}
```

## 3. 查询订单状态

**接口：** `POST /api/recharge/getOrderStatus`

**请求参数：**
```json
{
    "order_no": "R2025062917511508"
}
```

**响应示例：**
```json
{
    "code": 200,
    "message": "获取信息成功",
    "data": {
        "order_no": "R2025062917511508",
        "amount": 100.5,
        "status": 2,
        "status_text": "支付成功",
        "create_time": "2025-06-29 17:51:15",
        "pay_time": "2025-06-29 17:52:30"
    }
}
```

## 4. 获取充值记录

**接口：** `GET /api/recharge/getRechargeHistory?page=1&limit=10`

**响应示例：**
```json
{
    "code": 200,
    "message": "获取信息成功",
    "data": {
        "list": [
            {
                "order_no": "R2025062917511508",
                "amount": 100.5,
                "status": 2,
                "status_text": "支付成功",
                "create_time": "2025-06-29 17:51:15",
                "pay_time": "2025-06-29 17:52:30"
            }
        ],
        "total": 1,
        "page": 1,
        "limit": 10
    }
}
```

## 5. 前端处理流程

### JavaScript示例：

```javascript
// 1. 创建充值订单
async function createRechargeOrder(amount) {
    try {
        const response = await fetch('/api/recharge/createOrder', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'token': localStorage.getItem('userToken') // base64编码的用户ID
            },
            body: JSON.stringify({
                amount: amount,
                pay_way: 'Epusdt'
            })
        });
        
        const result = await response.json();
        
        if (result.code === 200) {
            // 跳转到支付页面
            window.open(result.data.pay_url, '_blank');
            
            // 开始轮询订单状态
            pollOrderStatus(orderNo);
        } else {
            alert(result.message);
        }
    } catch (error) {
        console.error('创建订单失败:', error);
    }
}

// 2. 轮询订单状态
async function pollOrderStatus(orderNo) {
    const maxAttempts = 60; // 最多轮询60次（5分钟）
    let attempts = 0;
    
    const poll = async () => {
        try {
            const response = await fetch('/api/recharge/getOrderStatus', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'token': localStorage.getItem('userToken')
                },
                body: JSON.stringify({
                    order_no: orderNo
                })
            });
            
            const result = await response.json();
            
            if (result.code === 200) {
                const order = result.data;
                
                if (order.status === 2) {
                    // 支付成功
                    alert('支付成功！');
                    // 刷新用户余额
                    refreshUserBalance();
                    return;
                } else if (order.status === 3) {
                    // 支付失败
                    alert('支付失败！');
                    return;
                }
            }
            
            attempts++;
            if (attempts < maxAttempts) {
                // 5秒后再次查询
                setTimeout(poll, 5000);
            } else {
                alert('支付状态查询超时，请手动刷新页面查看');
            }
        } catch (error) {
            console.error('查询订单状态失败:', error);
        }
    };
    
    poll();
}

// 3. 刷新用户余额
async function refreshUserBalance() {
    // 调用获取用户信息接口，更新页面显示的余额
    // 具体实现根据你的用户信息接口而定
}
```

## 6. 状态码说明

- **status = 0**: 待支付
- **status = 1**: 支付中（已废弃）
- **status = 2**: 支付成功
- **status = 3**: 支付失败

## 7. 错误处理

常见错误码：
- **401**: 参数错误或用户认证失败
- **402**: 金额超出限制
- **403**: 支付方式不支持
- **500**: 服务器内部错误
