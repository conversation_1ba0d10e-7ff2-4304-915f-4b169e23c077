<?php
/**
 * 修复错误上传路径的文件
 */

echo "=== 修复错误上传的文件 ===\n\n";

// 1. 查找错误路径的文件
$wrongBasePath = "public/public/base/ico/";
$correctBasePath = "public/base/ico/";

echo "1. 扫描错误路径: $wrongBasePath\n";

if (!is_dir($wrongBasePath)) {
    echo "错误路径不存在，无需修复\n";
    exit;
}

// 递归查找所有文件
function findFiles($dir) {
    $files = [];
    if (is_dir($dir)) {
        $items = scandir($dir);
        foreach ($items as $item) {
            if ($item == '.' || $item == '..') continue;
            
            $fullPath = $dir . '/' . $item;
            if (is_dir($fullPath)) {
                $files = array_merge($files, findFiles($fullPath));
            } else {
                $files[] = $fullPath;
            }
        }
    }
    return $files;
}

$wrongFiles = findFiles($wrongBasePath);
echo "找到错误位置的文件数量: " . count($wrongFiles) . "\n\n";

if (empty($wrongFiles)) {
    echo "没有需要修复的文件\n";
    exit;
}

// 2. 移动文件到正确位置
echo "2. 开始移动文件:\n";
$movedCount = 0;
$errorCount = 0;

foreach ($wrongFiles as $wrongFile) {
    // 计算正确的目标路径
    $relativePath = str_replace($wrongBasePath, '', $wrongFile);
    $correctFile = $correctBasePath . $relativePath;
    $correctDir = dirname($correctFile);
    
    echo "处理文件: " . basename($wrongFile) . "\n";
    echo "  源路径: $wrongFile\n";
    echo "  目标路径: $correctFile\n";
    
    // 确保目标目录存在
    if (!is_dir($correctDir)) {
        if (mkdir($correctDir, 0755, true)) {
            echo "  创建目录: $correctDir\n";
        } else {
            echo "  ❌ 创建目录失败: $correctDir\n";
            $errorCount++;
            continue;
        }
    }
    
    // 检查目标文件是否已存在
    if (file_exists($correctFile)) {
        echo "  ⚠️  目标文件已存在，跳过\n";
        continue;
    }
    
    // 移动文件
    if (rename($wrongFile, $correctFile)) {
        echo "  ✅ 移动成功\n";
        $movedCount++;
    } else {
        echo "  ❌ 移动失败\n";
        $errorCount++;
    }
    
    echo "\n";
}

// 3. 清理空目录
echo "3. 清理空目录:\n";
function removeEmptyDirs($dir) {
    if (!is_dir($dir)) return false;
    
    $items = scandir($dir);
    $hasFiles = false;
    
    foreach ($items as $item) {
        if ($item == '.' || $item == '..') continue;
        
        $fullPath = $dir . '/' . $item;
        if (is_dir($fullPath)) {
            if (!removeEmptyDirs($fullPath)) {
                $hasFiles = true;
            }
        } else {
            $hasFiles = true;
        }
    }
    
    if (!$hasFiles) {
        if (rmdir($dir)) {
            echo "删除空目录: $dir\n";
            return true;
        }
    }
    
    return false;
}

removeEmptyDirs($wrongBasePath);

// 4. 统计结果
echo "\n4. 修复结果:\n";
echo "成功移动文件: $movedCount 个\n";
echo "移动失败文件: $errorCount 个\n";

if ($movedCount > 0) {
    echo "\n✅ 文件修复完成！现在上传的图片应该可以正常访问了。\n";
}

if ($errorCount > 0) {
    echo "\n⚠️  有 $errorCount 个文件移动失败，请手动检查。\n";
}

// 5. 验证修复结果
echo "\n5. 验证修复结果:\n";
$todayPath = $correctBasePath . date('Y/m/d') . '/';
if (is_dir($todayPath)) {
    $todayFiles = glob($todayPath . '*');
    echo "今日上传目录文件数量: " . count($todayFiles) . "\n";
    
    if (!empty($todayFiles)) {
        echo "示例文件URL:\n";
        $sampleFile = $todayFiles[0];
        $relativePath = str_replace('public/', '', $sampleFile);
        echo "https://jsdao.cc/$relativePath\n";
    }
}

echo "\n=== 修复完成 ===\n";
?>
