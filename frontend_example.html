<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>APP下载链接示例</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .download-section {
            background: #f5f5f5;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .download-btn {
            display: inline-block;
            background: #007bff;
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 5px;
            font-weight: bold;
        }
        .download-btn:hover {
            background: #0056b3;
        }
        .download-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .error {
            color: red;
            margin: 10px 0;
        }
        .success {
            color: green;
            margin: 10px 0;
        }
        pre {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>APP下载链接功能示例</h1>
    
    <div class="download-section">
        <h2>APP下载</h2>
        <p>点击下面的按钮下载我们的APP：</p>
        <a href="#" id="downloadBtn" class="download-btn" style="display: none;">下载APP</a>
        <div id="loadingMsg">正在获取下载链接...</div>
        <div id="errorMsg" class="error" style="display: none;"></div>
    </div>

    <div>
        <h2>API调用示例</h2>
        <p>以下是JavaScript调用APP下载链接API的示例代码：</p>
        <pre><code>// 获取APP下载链接
async function getAppDownloadUrl() {
    try {
        const response = await fetch('/api/system/getAppDownloadUrl');
        const data = await response.json();
        
        if (data.code === 200) {
            const downloadUrl = data.data.app_download_url;
            if (downloadUrl) {
                // 设置下载按钮链接
                document.getElementById('downloadBtn').href = downloadUrl;
                document.getElementById('downloadBtn').style.display = 'inline-block';
                document.getElementById('loadingMsg').style.display = 'none';
            } else {
                showError('暂未配置APP下载链接');
            }
        } else {
            showError('获取下载链接失败：' + data.msg);
        }
    } catch (error) {
        showError('网络错误：' + error.message);
    }
}

// 显示错误信息
function showError(message) {
    document.getElementById('errorMsg').textContent = message;
    document.getElementById('errorMsg').style.display = 'block';
    document.getElementById('loadingMsg').style.display = 'none';
}

// 页面加载时获取下载链接
getAppDownloadUrl();</code></pre>
    </div>

    <div>
        <h2>后台配置说明</h2>
        <ol>
            <li>登录后台管理系统</li>
            <li>进入 系统管理 -> 基本配置</li>
            <li>找到 "APP下载链接" 配置项</li>
            <li>输入APP的下载链接（如：https://example.com/app.apk）</li>
            <li>点击保存</li>
        </ol>
    </div>

    <script>
        // 获取APP下载链接
        async function getAppDownloadUrl() {
            try {
                const response = await fetch('/api/system/getAppDownloadUrl');
                const data = await response.json();
                
                if (data.code === 200) {
                    const downloadUrl = data.data.app_download_url;
                    if (downloadUrl) {
                        // 设置下载按钮链接
                        document.getElementById('downloadBtn').href = downloadUrl;
                        document.getElementById('downloadBtn').style.display = 'inline-block';
                        document.getElementById('loadingMsg').style.display = 'none';
                    } else {
                        showError('暂未配置APP下载链接');
                    }
                } else {
                    showError('获取下载链接失败：' + data.msg);
                }
            } catch (error) {
                showError('网络错误：' + error.message);
            }
        }

        // 显示错误信息
        function showError(message) {
            document.getElementById('errorMsg').textContent = message;
            document.getElementById('errorMsg').style.display = 'block';
            document.getElementById('loadingMsg').style.display = 'none';
        }

        // 页面加载时获取下载链接
        getAppDownloadUrl();
    </script>
</body>
</html>
