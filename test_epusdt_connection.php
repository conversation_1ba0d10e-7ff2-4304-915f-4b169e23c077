<?php
/**
 * EPUSDT连接测试脚本
 */

echo "=== EPUSDT连接测试 ===\n\n";

$api_url = 'https://pay.jsdao.cc';
$api_key = 'B81BA11A6528EC298C7DD88C144B8D882568BC1D';

// 测试数据
$test_data = [
    'order_id' => 'TEST_ORDER_' . time(),
    'amount' => 100.5,
    'notify_url' => 'https://jsdao.cc/api/callback/pay?gateway=Epusdt',
    'redirect_url' => 'https://jsdao.cc/api/my/index'
];

// 生成签名 - 根据EPUSDT官方文档
function epusdtSign(array $parameter, string $signKey) {
    ksort($parameter);
    reset($parameter);
    $sign = '';

    foreach ($parameter as $key => $val) {
        if ($val == '') continue;
        if ($key != 'signature') {
            if ($sign != '') {
                $sign .= "&";
            }
            $sign .= "$key=$val";
        }
    }

    $sign = md5($sign . $signKey);
    return strtolower($sign);
}

$test_data['signature'] = epusdtSign($test_data, $api_key);

echo "1. 测试数据:\n";
echo json_encode($test_data, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES) . "\n\n";

echo "2. 签名字符串: " . $signString . "\n";
echo "3. MD5签名: " . $test_data['signature'] . "\n\n";

// 测试不同的连接方法
$methods = [
    'method1' => '标准cURL请求',
    'method2' => '带User-Agent的cURL请求',
    'method3' => '使用file_get_contents',
    'method4' => '简化的cURL请求'
];

foreach ($methods as $method => $description) {
    echo "=== {$description} ===\n";
    
    switch ($method) {
        case 'method1':
            testMethod1($api_url, $test_data);
            break;
        case 'method2':
            testMethod2($api_url, $test_data);
            break;
        case 'method3':
            testMethod3($api_url, $test_data);
            break;
        case 'method4':
            testMethod4($api_url, $test_data);
            break;
    }
    
    echo "\n";
}

function testMethod1($api_url, $data) {
    $url = $api_url . '/api/v1/order/create-transaction';
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Accept: application/json'
    ]);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($response === false) {
        echo "错误: " . $error . "\n";
    } else {
        echo "HTTP状态码: " . $httpCode . "\n";
        echo "响应: " . substr($response, 0, 200) . "\n";
    }
}

function testMethod2($api_url, $data) {
    $url = $api_url . '/api/v1/order/create-transaction';
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Accept: application/json',
        'User-Agent: Mozilla/5.0 (compatible; EPUSDT-Client/1.0)',
        'Connection: close'
    ]);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (compatible; EPUSDT-Client/1.0)');
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($response === false) {
        echo "错误: " . $error . "\n";
    } else {
        echo "HTTP状态码: " . $httpCode . "\n";
        echo "响应: " . substr($response, 0, 200) . "\n";
    }
}

function testMethod3($api_url, $data) {
    $url = $api_url . '/api/v1/order/create-transaction';
    
    $context = stream_context_create([
        'http' => [
            'method' => 'POST',
            'header' => "Content-Type: application/json\r\n" .
                       "Accept: application/json\r\n" .
                       "User-Agent: Mozilla/5.0 (compatible; EPUSDT-Client/1.0)\r\n",
            'content' => json_encode($data),
            'timeout' => 10
        ],
        'ssl' => [
            'verify_peer' => false,
            'verify_peer_name' => false
        ]
    ]);
    
    $response = @file_get_contents($url, false, $context);
    
    if ($response === false) {
        echo "错误: file_get_contents失败\n";
    } else {
        echo "响应: " . substr($response, 0, 200) . "\n";
    }
}

function testMethod4($api_url, $data) {
    // 尝试先测试根路径
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $api_url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 5);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (compatible; EPUSDT-Client/1.0)');
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($response === false) {
        echo "根路径测试失败: " . $error . "\n";
    } else {
        echo "根路径HTTP状态码: " . $httpCode . "\n";
        echo "根路径响应: " . substr($response, 0, 100) . "\n";
    }
}

?>
