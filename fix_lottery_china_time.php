<?php
// 修复彩票数据 - 中国时间版本
echo "<h1>🔧 修复彩票数据（中国时间）</h1>";

// 设置中国时区
date_default_timezone_set('Asia/Shanghai');

// 获取当前时间信息
$china_time = time();
$china_date = date('Ymd', $china_time);
$current_time_str = date('Y-m-d H:i:s', $china_time);

echo "<h2>📊 当前状态</h2>";
echo "<p><strong>当前中国时间:</strong> {$current_time_str}</p>";
echo "<p><strong>当前日期:</strong> {$china_date}</p>";

// 计算当前期号（假设3分钟一期）
$rule = 3; // 3分钟一期
$second = $rule * 60; // 180秒
$now_time = (date('H')*60*60 + date('i') * 60); // 当日已过秒数
$current_period = intval($now_time / $second); // 当前期号（从0开始）
$current_expect = $china_date . $rule . str_pad($current_period, 3, '0', STR_PAD_LEFT);

echo "<p><strong>计算的当前期号:</strong> {$current_expect}</p>";
echo "<p><strong>每期时长:</strong> {$second} 秒 ({$rule} 分钟)</p>";
echo "<p><strong>当前是第:</strong> {$current_period} 期</p>";

echo "<h2>🛠️ 修复操作</h2>";

if (isset($_GET['action'])) {
    switch ($_GET['action']) {
        case 'clear_old_data':
            echo "<h3>调用补充开奖API...</h3>";
            $url = "https://jsdao.cc/api/lotteryapi/updateNewKj";
            $result = @file_get_contents($url);
            if ($result !== false) {
                echo "<p><strong>API响应:</strong></p>";
                echo "<pre style='background:#f0f0f0;padding:10px;'>" . htmlspecialchars($result) . "</pre>";
                echo "<p style='color:green;'>✅ 补充开奖API调用完成</p>";
            } else {
                echo "<p style='color:red;'>❌ API调用失败</p>";
            }
            echo "<p><a href='?' style='background:blue;color:white;padding:5px;text-decoration:none;'>返回主页</a></p>";
            break;
            
        case 'manual_kj_current':
            echo "<h3>调用开奖API...</h3>";
            $url = "https://jsdao.cc/api/lotteryapi/kj";
            $result = @file_get_contents($url);
            if ($result !== false) {
                echo "<p><strong>API响应:</strong></p>";
                echo "<pre style='background:#f0f0f0;padding:10px;'>" . htmlspecialchars($result) . "</pre>";
                echo "<p style='color:green;'>✅ 开奖API调用完成</p>";
            } else {
                echo "<p style='color:red;'>❌ API调用失败</p>";
            }
            echo "<p><a href='?' style='background:blue;color:white;padding:5px;text-decoration:none;'>返回主页</a></p>";
            break;
            
        case 'call_api_kj':
            echo "<h3>调用开奖API...</h3>";
            $url = "https://jsdao.cc/api/lotteryapi/kj";
            $result = @file_get_contents($url);
            if ($result !== false) {
                echo "<p><strong>API响应:</strong></p>";
                echo "<pre style='background:#f0f0f0;padding:10px;'>" . htmlspecialchars($result) . "</pre>";
            } else {
                echo "<p style='color:red;'>❌ API调用失败</p>";
            }
            echo "<p><a href='?' style='background:blue;color:white;padding:5px;text-decoration:none;'>返回主页</a></p>";
            break;
            
        case 'test_frontend':
            echo "<h3>测试前端接口...</h3>";
            $url = "https://jsdao.cc/api/Lottery/getLotteryInfo?key=game1";
            
            // 使用 curl 获取响应
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'token: ' . base64_encode('1') // 假设用户ID为1
            ]);
            $result = curl_exec($ch);
            $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);
            
            echo "<p><strong>HTTP状态码:</strong> {$http_code}</p>";
            echo "<p><strong>接口响应:</strong></p>";
            echo "<pre style='background:#f0f0f0;padding:10px;'>" . htmlspecialchars($result) . "</pre>";
            
            // 尝试解析JSON
            $data = json_decode($result, true);
            if ($data && isset($data['data'])) {
                echo "<h4>解析后的数据:</h4>";
                echo "<p><strong>当前期号:</strong> " . ($data['data']['now_expect'] ?? '未知') . "</p>";
                echo "<p><strong>下期期号:</strong> " . ($data['data']['next_expect'] ?? '未知') . "</p>";
                echo "<p><strong>倒计时:</strong> " . ($data['data']['second'] ?? '未知') . " 秒</p>";
                if (isset($data['data']['opencode'])) {
                    echo "<p><strong>开奖号码:</strong> " . implode(',', $data['data']['opencode']) . "</p>";
                }
            }
            echo "<p><a href='?' style='background:blue;color:white;padding:5px;text-decoration:none;'>返回主页</a></p>";
            break;
    }
    
} else {
    echo "<div style='margin:20px 0;'>";
    echo "<a href='?action=clear_old_data' style='background:orange;color:white;padding:10px;margin:5px;text-decoration:none;display:inline-block;'>� 补充开奖数据</a>";
    echo "<a href='?action=manual_kj_current' style='background:green;color:white;padding:10px;margin:5px;text-decoration:none;display:inline-block;'>🎲 调用开奖API</a>";
    echo "<a href='?action=call_api_kj' style='background:purple;color:white;padding:10px;margin:5px;text-decoration:none;display:inline-block;'>🔄 调用开奖API(备用)</a>";
    echo "<a href='?action=test_frontend' style='background:blue;color:white;padding:10px;margin:5px;text-decoration:none;display:inline-block;'>🧪 测试前端接口</a>";
    echo "</div>";
    
    echo "<h3>📋 操作说明</h3>";
    echo "<ul>";
    echo "<li><strong>补充开奖数据:</strong> 调用补充开奖API，生成缺失的开奖记录</li>";
    echo "<li><strong>调用开奖API:</strong> 调用系统的开奖接口</li>";
    echo "<li><strong>调用开奖API(备用):</strong> 备用的开奖接口</li>";
    echo "<li><strong>测试前端接口:</strong> 检查前端接口是否正常返回数据</li>";
    echo "</ul>";

    echo "<h3>🔍 建议操作顺序</h3>";
    echo "<ol>";
    echo "<li>先点击 <strong>补充开奖数据</strong> 生成缺失的开奖记录</li>";
    echo "<li>然后点击 <strong>调用开奖API</strong> 确保当前期开奖</li>";
    echo "<li>最后点击 <strong>测试前端接口</strong> 检查是否正常</li>";
    echo "</ol>";
}

echo "<hr>";
echo "<p><small>脚本执行时间: " . date('Y-m-d H:i:s') . "</small></p>";
?>
