<?php
/**
 * 详细诊断EPUSDT连接问题
 */

// 增强版curl_request函数，包含详细错误信息
function curl_request_debug($url, $data=null, $method='post', $header = array("content-type: application/json"), $https=true, $timeout = 30){
    echo "=== cURL 请求详情 ===\n";
    echo "URL: " . $url . "\n";
    echo "Method: " . $method . "\n";
    echo "Headers: " . implode(", ", $header) . "\n";
    echo "Data: " . $data . "\n";
    echo "Timeout: " . $timeout . "s\n\n";
    
    $method = strtoupper($method);
    $ch = curl_init();
    
    // 基本设置
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_VERBOSE, true); // 启用详细输出
    
    // 创建临时文件来捕获详细输出
    $verbose = fopen('php://temp', 'w+');
    curl_setopt($ch, CURLOPT_STDERR, $verbose);
    
    if($https){
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    }
    
    if ($method != "GET") {
        if($method == 'POST'){
            curl_setopt($ch, CURLOPT_POST, true);
        }
        if ($method == 'PUT' || strtoupper($method) == 'DELETE') {
            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $method);
        }
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
    }
    
    curl_setopt($ch, CURLOPT_TIMEOUT, $timeout);
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
    curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (compatible; EPUSDT-Test/1.0)');
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_MAXREDIRS, 3);
    
    $result = curl_exec($ch);
    
    // 获取详细信息
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    $errno = curl_errno($ch);
    $info = curl_getinfo($ch);
    
    // 获取详细输出
    rewind($verbose);
    $verboseLog = stream_get_contents($verbose);
    fclose($verbose);
    
    curl_close($ch);
    
    // 输出诊断信息
    echo "=== cURL 响应详情 ===\n";
    echo "HTTP Code: " . $httpCode . "\n";
    echo "Error Number: " . $errno . "\n";
    echo "Error Message: " . ($error ? $error : 'None') . "\n";
    echo "Total Time: " . $info['total_time'] . "s\n";
    echo "Connect Time: " . $info['connect_time'] . "s\n";
    echo "DNS Lookup Time: " . $info['namelookup_time'] . "s\n";
    echo "Content Type: " . ($info['content_type'] ? $info['content_type'] : 'Unknown') . "\n";
    echo "Content Length: " . ($info['download_content_length'] ? $info['download_content_length'] : 'Unknown') . "\n";
    
    if ($verboseLog) {
        echo "\n=== cURL 详细日志 ===\n";
        echo $verboseLog . "\n";
    }
    
    echo "\n=== 响应内容 ===\n";
    if ($result === false) {
        echo "❌ 请求失败\n";
        return false;
    } else if (empty($result)) {
        echo "⚠️  响应为空\n";
        return '';
    } else {
        echo "✅ 收到响应 (" . strlen($result) . " 字节)\n";
        echo $result . "\n";
        return $result;
    }
}

function token($length){
    $str = md5(time());
    $token = substr($str,15,$length);
    return $token;
}

echo "=== EPUSDT 连接诊断测试 ===\n\n";

// 首先测试基本连接
echo "1. 测试基本网络连接...\n";
$ping_result = shell_exec('ping -c 3 pay.jsdao.cc 2>&1');
echo "Ping 结果:\n" . $ping_result . "\n";

echo str_repeat("-", 60) . "\n";

// 测试DNS解析
echo "2. 测试DNS解析...\n";
$ip = gethostbyname('pay.jsdao.cc');
echo "pay.jsdao.cc 解析到: " . $ip . "\n\n";

echo str_repeat("-", 60) . "\n";

// 测试简单HTTP连接
echo "3. 测试简单HTTP连接...\n";
$simple_result = curl_request_debug('https://pay.jsdao.cc', null, 'GET', array(), true, 10);

echo str_repeat("-", 60) . "\n";

// 测试EPUSDT API
echo "4. 测试EPUSDT API...\n";

$amount = (double)100.5;
$notify_url = 'https://jsdao.cc/api/callback/pay?gateway=Epusdt';
$redirect_url = 'https://jsdao.cc/api/my/index';
$order_id = (string)token(10);
$key = 'B81BA11A6528EC298C7DD88C144B8D882568BC1D';

$str = 'amount='.$amount.'&notify_url='.$notify_url.'&order_id='.$order_id.'&redirect_url='.$redirect_url.$key;
$signature = md5($str);

$data = json_encode(array(
    'order_id' => $order_id,
    'amount' => $amount,
    'notify_url' => $notify_url,
    'redirect_url' => $redirect_url,
    'signature' => $signature
));

echo "测试参数:\n";
echo "order_id: " . $order_id . "\n";
echo "amount: " . $amount . "\n";
echo "signature: " . $signature . "\n\n";

$api_result = curl_request_debug('https://pay.jsdao.cc/api/v1/order/create-transaction', $data, 'post', array("content-type: application/json"), true, 30);

echo str_repeat("-", 60) . "\n";

// 测试不同的超时设置
echo "5. 测试更长超时时间...\n";
$long_timeout_result = curl_request_debug('https://pay.jsdao.cc/api/v1/order/create-transaction', $data, 'post', array("content-type: application/json"), true, 60);

echo str_repeat("-", 60) . "\n";

// 测试不同的User-Agent
echo "6. 测试不同的请求头...\n";
$different_headers = array(
    "content-type: application/json",
    "User-Agent: PostmanRuntime/7.32.3",
    "Accept: */*",
    "Cache-Control: no-cache",
    "Host: pay.jsdao.cc",
    "Accept-Encoding: gzip, deflate, br",
    "Connection: keep-alive"
);

$header_result = curl_request_debug('https://pay.jsdao.cc/api/v1/order/create-transaction', $data, 'post', $different_headers, true, 30);

echo "\n=== 诊断总结 ===\n";
echo "如果所有测试都失败，可能的原因:\n";
echo "1. 网络连接问题 - 检查防火墙和网络设置\n";
echo "2. DNS解析问题 - 尝试使用不同的DNS服务器\n";
echo "3. SSL/TLS问题 - 服务器可能要求特定的SSL配置\n";
echo "4. 服务器限制 - 可能有IP白名单或地理位置限制\n";
echo "5. API服务暂时不可用 - 联系EPUSDT技术支持\n";

?>
