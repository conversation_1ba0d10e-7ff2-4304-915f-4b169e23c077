# 选妃素材API接口设计说明

## 设计原则

为了保持向后兼容性，我们采用以下设计原则：

1. **原有接口保持不变** - 现有的API接口继续返回女生素材，不包含男生素材
2. **新增专门接口** - 为女生素材和男生素材分别提供专门的API接口
3. **明确区分** - 在后台管理界面明确标识女生素材和男生素材

## API接口列表

### 原有接口（保持兼容）

#### 1. 获取选妃详情
```
POST /api/xuanfei/xuanfeidata
参数: id (选妃ID)
返回: 选妃详细信息，img_url字段包含女生素材
```

#### 2. 获取选妃列表
```
POST /api/xuanfei/xuanfeilist
参数: id (地区ID)
返回: 选妃列表，img_url字段包含女生素材
```

### 新增专门接口

#### 3. 获取女生素材
```
POST /api/xuanfei/femaleImages
参数: id (选妃ID)
返回: 女生素材图片URL数组
```

#### 4. 获取男生素材
```
POST /api/xuanfei/maleImages
参数: id (选妃ID)
返回: 男生素材图片URL数组
```

## 数据库设计

### xuanfei_list表字段

| 字段名 | 类型 | 说明 |
|--------|------|------|
| img_url | TEXT | 女生素材图片JSON数据（原有字段） |
| male_images | TEXT | 男生素材图片JSON数据（新增字段） |

### 数据格式示例

```json
{
  "img_url": "[\"uploads/female/image1.jpg\", \"uploads/female/image2.jpg\"]",
  "male_images": "[\"uploads/male/image1.jpg\", \"uploads/male/image2.jpg\"]"
}
```

## 前端APP集成方案

### 方案一：使用原有接口（推荐现有APP）

```javascript
// 继续使用原有接口，只获取女生素材
fetch('/api/xuanfei/xuanfeidata', {
    method: 'POST',
    body: 'id=1'
})
.then(response => response.json())
.then(data => {
    // data.data.img_url 包含女生素材
    console.log('女生素材:', data.data.img_url);
});
```

### 方案二：使用专门接口（推荐新APP）

```javascript
// 分别获取女生和男生素材
Promise.all([
    fetch('/api/xuanfei/femaleImages', {method: 'POST', body: 'id=1'}),
    fetch('/api/xuanfei/maleImages', {method: 'POST', body: 'id=1'})
])
.then(responses => Promise.all(responses.map(r => r.json())))
.then(([femaleData, maleData]) => {
    console.log('女生素材:', femaleData.data);
    console.log('男生素材:', maleData.data);
});
```

## 后台管理界面

### 添加/编辑选妃页面

1. **女生素材多图片上传** - 对应原有的img_url字段
2. **男生素材多图片上传** - 对应新增的male_images字段

### 选妃列表页面

1. **女生素材列** - 显示女生素材图片数量和查看按钮
2. **男生素材列** - 显示男生素材图片数量和查看按钮

## 兼容性说明

### 现有APP无需修改

- 原有的API接口返回格式完全不变
- img_url字段继续包含女生素材
- 现有APP可以继续正常使用

### 新APP可选择使用新接口

- 可以使用专门的femaleImages和maleImages接口
- 获得更清晰的数据结构
- 支持分别处理女生和男生素材

## 升级路径

### 阶段1：保持兼容（当前）
- 原有接口不变，继续返回女生素材
- 新增男生素材接口
- 现有APP无需修改

### 阶段2：逐步迁移（可选）
- 新版本APP使用专门接口
- 旧版本APP继续使用原有接口
- 两套接口并行运行

### 阶段3：统一接口（未来）
- 所有APP迁移到新接口后
- 可以考虑统一接口设计
- 保持向后兼容

## 测试验证

使用 `test_male_images_api.php` 文件可以测试：

1. 原有接口的兼容性
2. 新增女生素材接口
3. 新增男生素材接口
4. 数据格式的正确性

## 总结

这种设计方案的优势：

1. **完全向后兼容** - 现有APP无需任何修改
2. **清晰的数据分离** - 女生和男生素材明确区分
3. **灵活的使用方式** - 可以选择使用原有接口或新接口
4. **平滑的升级路径** - 支持渐进式迁移
5. **明确的语义** - 接口名称清楚表达功能
