-- 更新银行数据为指定的墨西哥银行列表
-- 执行前请备份数据库！

-- 1. 清空现有的银行数据
DELETE FROM `bank_list`;
DELETE FROM `bank`;

-- 2. 插入新的墨西哥银行数据到 bank_list 表
INSERT INTO `bank_list` (`id`, `name`, `code`, `status`, `create_time`, `update_time`) VALUES
(1, 'BBVA México', 'BBVA_MX', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(2, 'Banamex', 'BANAMEX', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(3, 'Banorte', 'BANORTE', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(4, 'Santander México', 'SANTANDER_MX', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(5, 'HSBC México', 'HSBC_MX', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(6, 'Banco Azteca', 'BANCO_AZTECA', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

-- 3. 插入新的墨西哥银行数据到 bank 表（如果需要图标）
INSERT INTO `bank` (`id`, `title`, `name`, `code`, `thumb`, `status`, `create_time`, `update_time`) VALUES
(1, 'BBVA México', 'BBVA México', 'BBVA_MX', '/static/images/banks/bbva.png', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(2, 'Banamex', 'Banamex', 'BANAMEX', '/static/images/banks/banamex.png', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(3, 'Banorte', 'Banorte', 'BANORTE', '/static/images/banks/banorte.png', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(4, 'Santander México', 'Santander México', 'SANTANDER_MX', '/static/images/banks/santander.png', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(5, 'HSBC México', 'HSBC México', 'HSBC_MX', '/static/images/banks/hsbc.png', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(6, 'Banco Azteca', 'Banco Azteca', 'BANCO_AZTECA', '/static/images/banks/azteca.png', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

-- 4. 重置自增ID（可选）
ALTER TABLE `bank_list` AUTO_INCREMENT = 7;
ALTER TABLE `bank` AUTO_INCREMENT = 7;

-- 执行完成后，接口将返回这6家墨西哥银行：
-- 1. BBVA México
-- 2. Banamex  
-- 3. Banorte
-- 4. Santander México
-- 5. HSBC México
-- 6. Banco Azteca
