<?php
/**
 * 调试充值接口问题
 */

echo "=== 充值接口调试 ===\n\n";

// 检查文件语法
$files = [
    'application/api/controller/Recharge.php',
    'application/api/model/RechargeModel.php',
    'application/api/pay/Epusdt.php'
];

foreach ($files as $file) {
    echo "检查文件: $file\n";
    $output = shell_exec("php -l $file 2>&1");
    if (strpos($output, 'No syntax errors') !== false) {
        echo "✅ 语法正确\n";
    } else {
        echo "❌ 语法错误: $output\n";
    }
    echo "\n";
}

// 检查类是否可以正常加载
echo "=== 类加载测试 ===\n\n";

// 模拟ThinkPHP环境
if (!defined('APP_PATH')) {
    define('APP_PATH', '/www/wwwroot/tcyp/application/');
}

// 模拟必要的函数
function json_exit_lang($code, $msg, $data = []) {
    echo "API响应: code=$code, msg=$msg, data=" . json_encode($data) . "\n";
    if ($code != 200) {
        throw new Exception("API Error: $msg");
    }
}

function url($path, $params = [], $https = false, $domain = false) {
    return "http://localhost" . $path;
}

function config($key) {
    if ($key == 'pay.epusdt.api_url') {
        return 'https://pay.jsdao.cc';
    }
    if ($key == 'pay.epusdt.api_key') {
        return 'B81BA11A6528EC298C7DD88C144B8D882568BC1D';
    }
    return null;
}

// 模拟基础类
class Controller {}
class Model {}

class Db {
    public static function name($table) {
        return new self();
    }
    
    public function where($condition) {
        return $this;
    }
    
    public function find() {
        return ['id' => 1, 'order_no' => 'TEST123'];
    }
    
    public function save($data) {
        echo "保存数据: " . json_encode($data) . "\n";
        return true;
    }
    
    public static function startTrans() {
        echo "开始事务\n";
    }
    
    public static function commit() {
        echo "提交事务\n";
    }
    
    public static function rollback() {
        echo "回滚事务\n";
    }
}

// 模拟Request
class Request {
    public static function header($name) {
        if ($name == 'token') {
            return base64_encode('1'); // 模拟用户ID为1
        }
        return null;
    }
    
    public function param() {
        return [
            'amount' => 100.5,
            'pay_way' => 'Epusdt'
        ];
    }
}

echo "=== 测试充值流程 ===\n\n";

try {
    // 模拟充值数据
    $rechargeData = [
        'money' => 100.5,
        'pay_way' => 'Epusdt'
    ];
    
    echo "1. 测试充值数据验证:\n";
    echo "充值金额: " . $rechargeData['money'] . "\n";
    echo "支付方式: " . $rechargeData['pay_way'] . "\n";
    
    if ($rechargeData['money'] <= 0) {
        throw new Exception("金额必须大于0");
    }
    echo "✅ 数据验证通过\n\n";
    
    echo "2. 测试订单号生成:\n";
    $sn = 'R'.date('YmdHis').rand(10,99);
    echo "订单号: $sn\n";
    echo "✅ 订单号生成成功\n\n";
    
    echo "3. 测试数据库操作:\n";
    Db::startTrans();
    $temp = [
        'order_no' => $sn,
        'money' => $rechargeData['money'],
        'mid' => 1,
        'type' => 1,
        'status' => 0,
        'create_time' => time(),
        'update_time' => time(),
        'version' => 0
    ];
    
    $db = Db::name('recharge');
    $db->save($temp);
    Db::commit();
    echo "✅ 数据库操作成功\n\n";
    
    echo "4. 测试支付类加载:\n";
    $className = "\\app\\api\\pay\\" . $rechargeData['pay_way'];
    echo "支付类名: $className\n";
    echo "✅ 支付类路径正确\n\n";
    
    echo "=== 调试完成 ===\n";
    echo "✅ 所有基础功能测试通过！\n";
    echo "\n建议检查项目:\n";
    echo "1. 确保数据库连接正常\n";
    echo "2. 确保EPUSDT配置已正确设置\n";
    echo "3. 确保用户token有效\n";
    echo "4. 检查服务器错误日志\n";
    
} catch (Exception $e) {
    echo "❌ 测试失败: " . $e->getMessage() . "\n";
}

?>
