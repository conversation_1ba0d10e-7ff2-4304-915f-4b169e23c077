<?php
/**
 * 调试上传问题的脚本
 */

echo "=== 上传问题调试 ===\n\n";

// 1. 检查基本环境
echo "1. 基本环境检查:\n";
echo "PHP版本: " . PHP_VERSION . "\n";
echo "当前工作目录: " . getcwd() . "\n";
echo "脚本路径: " . __FILE__ . "\n";

// 2. 检查上传目录
echo "\n2. 上传目录检查:\n";
$uploadPath = "public/base/ico/" . date('Y/m/d') . '/';
echo "上传路径: $uploadPath\n";
echo "目录是否存在: " . (is_dir($uploadPath) ? "是" : "否") . "\n";
echo "目录是否可写: " . (is_writable(dirname($uploadPath)) ? "是" : "否") . "\n";

// 尝试创建目录
if (!is_dir($uploadPath)) {
    echo "尝试创建目录...\n";
    if (mkdir($uploadPath, 0755, true)) {
        echo "目录创建成功\n";
    } else {
        echo "目录创建失败\n";
    }
}

// 3. 检查ThinkPHP相关文件
echo "\n3. ThinkPHP文件检查:\n";
$files = [
    'application/admin/controller/System.php',
    'application/admin/controller/Base.php',
    'application/admin/model/OssModel.php',
    'application/common.php'
];

foreach ($files as $file) {
    echo "文件: $file - " . (file_exists($file) ? "存在" : "不存在") . "\n";
}

// 4. 检查类是否可以正常加载
echo "\n4. 类加载测试:\n";

// 模拟ThinkPHP环境
if (!defined('APP_PATH')) {
    define('APP_PATH', __DIR__ . '/application/');
}

// 检查composer自动加载
if (file_exists('vendor/autoload.php')) {
    require_once 'vendor/autoload.php';
    echo "Composer自动加载: 成功\n";
} else {
    echo "Composer自动加载: 失败\n";
}

// 检查ThinkPHP基础文件
if (file_exists('thinkphp/base.php')) {
    require_once 'thinkphp/base.php';
    echo "ThinkPHP基础文件: 成功\n";
} else {
    echo "ThinkPHP基础文件: 失败\n";
}

// 5. 模拟上传请求
echo "\n5. 模拟上传测试:\n";

// 检查json_exit函数
if (function_exists('json_exit')) {
    echo "json_exit函数: 存在\n";
} else {
    echo "json_exit函数: 不存在，定义临时函数\n";
    function json_exit($code, $msg, $data = []) {
        echo "API响应: code=$code, msg=$msg, data=" . json_encode($data) . "\n";
        if ($code != 200) {
            throw new Exception("API Error: $msg");
        }
    }
}

// 6. 检查OSS配置
echo "\n6. OSS配置检查:\n";
if (file_exists('config/oss.php')) {
    $ossConfig = include 'config/oss.php';
    echo "OSS配置文件: 存在\n";
    echo "OSS启用状态: " . (isset($ossConfig['enable']) ? ($ossConfig['enable'] ? "启用" : "禁用") : "未设置") . "\n";
} else {
    echo "OSS配置文件: 不存在\n";
}

// 7. 检查数据库配置
echo "\n7. 数据库配置检查:\n";
if (file_exists('config/database.php')) {
    echo "数据库配置文件: 存在\n";
} else {
    echo "数据库配置文件: 不存在\n";
}

// 8. 检查文件上传配置
echo "\n8. PHP上传配置:\n";
echo "file_uploads: " . (ini_get('file_uploads') ? "启用" : "禁用") . "\n";
echo "upload_max_filesize: " . ini_get('upload_max_filesize') . "\n";
echo "post_max_size: " . ini_get('post_max_size') . "\n";
echo "max_file_uploads: " . ini_get('max_file_uploads') . "\n";
echo "memory_limit: " . ini_get('memory_limit') . "\n";

// 9. 检查错误日志
echo "\n9. 错误日志检查:\n";
$logPath = 'runtime/log/' . date('Ym') . '/' . date('d') . '.log';
if (file_exists($logPath)) {
    echo "今日日志文件: 存在 ($logPath)\n";
    $logContent = file_get_contents($logPath);
    $errorCount = substr_count($logContent, '[ error ]');
    echo "错误数量: $errorCount\n";
} else {
    echo "今日日志文件: 不存在\n";
}

// 10. 模拟System控制器的doupload方法
echo "\n10. 模拟doupload方法:\n";

try {
    // 检查是否有文件上传
    if (empty($_FILES)) {
        echo "没有文件上传数据\n";
    } else {
        echo "文件上传数据:\n";
        print_r($_FILES);
    }
    
    // 模拟路径生成
    $path = "public/base/ico/" . date('Y/m/d') . '/';
    echo "生成的上传路径: $path\n";
    
    // 检查路径是否可写
    if (!is_dir($path)) {
        if (mkdir($path, 0755, true)) {
            echo "目录创建成功\n";
        } else {
            echo "目录创建失败\n";
        }
    }
    
    if (is_writable($path)) {
        echo "目录可写\n";
    } else {
        echo "目录不可写\n";
    }
    
} catch (Exception $e) {
    echo "模拟测试异常: " . $e->getMessage() . "\n";
}

echo "\n=== 调试完成 ===\n";
?>
