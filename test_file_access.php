<?php
/**
 * 测试文件访问问题
 */

echo "=== 文件访问测试 ===\n\n";

// 1. 检查文件是否真的存在
echo "1. 文件存在性检查:\n";
$testDir = "public/base/ico/2025/08/27/";
$files = glob($testDir . "*");

echo "目录: $testDir\n";
echo "文件数量: " . count($files) . "\n";

if (!empty($files)) {
    echo "文件列表:\n";
    foreach ($files as $file) {
        $fileName = basename($file);
        $fileSize = filesize($file);
        $isReadable = is_readable($file);
        echo "  - $fileName (大小: {$fileSize}字节, 可读: " . ($isReadable ? "是" : "否") . ")\n";
        
        // 生成对应的URL
        $relativePath = str_replace('public/', '', $file);
        $url = "https://jsdao.cc/$relativePath";
        echo "    对应URL: $url\n";
    }
}

// 2. 检查Web服务器配置问题
echo "\n2. Web服务器配置分析:\n";
echo "当前nginx.htaccess配置:\n";
if (file_exists('public/nginx.htaccess')) {
    echo "```\n";
    echo file_get_contents('public/nginx.htaccess');
    echo "```\n";
    
    echo "问题分析:\n";
    echo "- 这个配置会将所有不存在的请求重定向到index.php\n";
    echo "- 但对于静态文件，应该直接提供文件内容\n";
    echo "- 需要在nginx配置中添加静态文件处理规则\n";
}

// 3. 建议的nginx配置
echo "\n3. 建议的nginx配置:\n";
echo "```nginx\n";
echo "server {\n";
echo "    listen 80;\n";
echo "    server_name jsdao.cc;\n";
echo "    root /path/to/your/project/public;\n";
echo "    index index.php index.html;\n";
echo "\n";
echo "    # 静态文件处理\n";
echo "    location ~* \\.(jpg|jpeg|png|gif|ico|css|js|pdf|txt|svg|woff|woff2|ttf|eot)$ {\n";
echo "        expires 1y;\n";
echo "        add_header Cache-Control \"public, immutable\";\n";
echo "        try_files \$uri =404;\n";
echo "    }\n";
echo "\n";
echo "    # PHP文件处理\n";
echo "    location ~ \\.php$ {\n";
echo "        try_files \$uri =404;\n";
echo "        fastcgi_pass 127.0.0.1:9000;\n";
echo "        fastcgi_index index.php;\n";
echo "        fastcgi_param SCRIPT_FILENAME \$document_root\$fastcgi_script_name;\n";
echo "        include fastcgi_params;\n";
echo "    }\n";
echo "\n";
echo "    # ThinkPHP路由处理\n";
echo "    location / {\n";
echo "        try_files \$uri \$uri/ /index.php?\$query_string;\n";
echo "    }\n";
echo "}\n";
echo "```\n";

// 4. 临时解决方案
echo "\n4. 临时解决方案:\n";
echo "创建一个直接访问文件的PHP脚本\n";

// 创建临时访问脚本
$accessScript = 'public/file_access.php';
$scriptContent = '<?php
// 临时文件访问脚本
$path = $_GET["path"] ?? "";
if (empty($path)) {
    http_response_code(400);
    echo "Missing path parameter";
    exit;
}

// 安全检查：防止目录遍历攻击
$path = str_replace(["../", "..\\"], "", $path);
$fullPath = __DIR__ . "/" . $path;

if (!file_exists($fullPath)) {
    http_response_code(404);
    echo "File not found";
    exit;
}

// 检查文件类型
$mimeTypes = [
    "jpg" => "image/jpeg",
    "jpeg" => "image/jpeg", 
    "png" => "image/png",
    "gif" => "image/gif",
    "ico" => "image/x-icon"
];

$extension = strtolower(pathinfo($fullPath, PATHINFO_EXTENSION));
$mimeType = $mimeTypes[$extension] ?? "application/octet-stream";

// 输出文件
header("Content-Type: " . $mimeType);
header("Content-Length: " . filesize($fullPath));
header("Cache-Control: public, max-age=31536000");
readfile($fullPath);
?>';

file_put_contents($accessScript, $scriptContent);
echo "已创建临时访问脚本: $accessScript\n";

// 5. 测试临时访问
if (!empty($files)) {
    $testFile = $files[0];
    $relativePath = str_replace('public/', '', $testFile);
    $tempUrl = "https://jsdao.cc/file_access.php?path=" . urlencode($relativePath);
    echo "临时访问URL示例: $tempUrl\n";
}

echo "\n=== 测试完成 ===\n";
echo "\n解决步骤:\n";
echo "1. 联系服务器管理员修改nginx配置\n";
echo "2. 或者使用临时访问脚本\n";
echo "3. 或者修改上传逻辑，使用绝对URL路径\n";
?>
