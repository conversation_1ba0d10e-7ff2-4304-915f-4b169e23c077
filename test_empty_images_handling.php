<?php
/**
 * 测试空图片处理逻辑
 * 验证当女生素材为空时系统是否能正常工作
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 设置内容类型
header('Content-Type: text/html; charset=utf-8');

echo "<h1>空图片处理逻辑测试</h1>";

// 模拟不同的图片数据情况
$testCases = [
    [
        'name' => '正常情况 - 有女生素材',
        'img_url' => '["uploads/female1.jpg", "uploads/female2.jpg"]',
        'male_images' => '["uploads/male1.jpg"]'
    ],
    [
        'name' => '只有男生素材',
        'img_url' => '[]',
        'male_images' => '["uploads/male1.jpg", "uploads/male2.jpg"]'
    ],
    [
        'name' => '只有女生素材',
        'img_url' => '["uploads/female1.jpg"]',
        'male_images' => '[]'
    ],
    [
        'name' => '两种素材都为空',
        'img_url' => '[]',
        'male_images' => '[]'
    ],
    [
        'name' => '空字符串情况',
        'img_url' => '',
        'male_images' => ''
    ],
    [
        'name' => 'null情况',
        'img_url' => null,
        'male_images' => null
    ]
];

echo "<h2>测试结果</h2>";
echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th>测试用例</th><th>女生素材处理</th><th>男生素材处理</th><th>vod_pic结果</th><th>状态</th></tr>";

foreach ($testCases as $case) {
    echo "<tr>";
    echo "<td>" . $case['name'] . "</td>";
    
    // 测试女生素材处理
    $img_urls = json_decode($case['img_url'], true);
    $female_result = '';
    $vod_pic = '';
    
    if (is_array($img_urls) && count($img_urls) > 0) {
        $vod_pic = '../../../' . $img_urls[0];
        $female_result = "有素材 (" . count($img_urls) . "张)";
    } else {
        $vod_pic = '';
        $female_result = "无素材";
    }
    
    // 测试男生素材处理
    $male_urls = json_decode($case['male_images'], true);
    $male_result = '';
    
    if (is_array($male_urls) && count($male_urls) > 0) {
        $male_result = "有素材 (" . count($male_urls) . "张)";
    } else {
        $male_result = "无素材";
    }
    
    // 判断状态
    $status = '';
    if ($female_result === "无素材" && $male_result === "无素材") {
        $status = '<span style="color: red;">❌ 应该验证失败</span>';
    } else {
        $status = '<span style="color: green;">✅ 应该验证通过</span>';
    }
    
    echo "<td>" . $female_result . "</td>";
    echo "<td>" . $male_result . "</td>";
    echo "<td>" . htmlspecialchars($vod_pic) . "</td>";
    echo "<td>" . $status . "</td>";
    echo "</tr>";
}

echo "</table>";

echo "<h2>前端显示逻辑测试</h2>";
echo "<p>模拟前端列表页面的显示逻辑：</p>";

echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th>情况</th><th>女生素材列显示</th><th>男生素材列显示</th></tr>";

foreach ($testCases as $case) {
    echo "<tr>";
    echo "<td>" . $case['name'] . "</td>";
    
    // 模拟女生素材列显示
    $img_urls = json_decode($case['img_url'], true);
    if (is_array($img_urls) && count($img_urls) > 0) {
        $female_display = '<a class="layui-btn layui-btn-xs">查看</a>';
    } else {
        $female_display = '<span style="color:#ccc;">无素材</span>';
    }
    
    // 模拟男生素材列显示
    $male_urls = json_decode($case['male_images'], true);
    if (is_array($male_urls) && count($male_urls) > 0) {
        $male_display = '<a class="layui-btn layui-btn-xs layui-btn-warm">查看(' . count($male_urls) . '张)</a>';
    } else {
        $male_display = '<span style="color:#ccc;">无素材</span>';
    }
    
    echo "<td>" . $female_display . "</td>";
    echo "<td>" . $male_display . "</td>";
    echo "</tr>";
}

echo "</table>";

echo "<h2>验证逻辑测试</h2>";
echo "<p>模拟后端验证逻辑：</p>";

echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th>情况</th><th>验证结果</th><th>错误信息</th></tr>";

foreach ($testCases as $case) {
    echo "<tr>";
    echo "<td>" . $case['name'] . "</td>";
    
    // 模拟后端验证逻辑
    $pc_src = json_decode($case['img_url'], true);
    $male_images = json_decode($case['male_images'], true);
    
    $hasFemaleImages = is_array($pc_src) && count(array_filter($pc_src)) > 0;
    $hasMaleImages = is_array($male_images) && count(array_filter($male_images)) > 0;
    
    if (!$hasFemaleImages && !$hasMaleImages) {
        $validation_result = '<span style="color: red;">❌ 验证失败</span>';
        $error_message = '请至少上传女生素材或男生素材中的一种！';
    } else {
        $validation_result = '<span style="color: green;">✅ 验证通过</span>';
        $error_message = '-';
    }
    
    echo "<td>" . $validation_result . "</td>";
    echo "<td>" . $error_message . "</td>";
    echo "</tr>";
}

echo "</table>";

echo "<h2>修复说明</h2>";
echo "<ul>";
echo "<li><strong>后端模型修复：</strong> 安全处理 json_decode 结果，避免访问空数组的第0个元素</li>";
echo "<li><strong>前端列表修复：</strong> 检查 vod_pic 是否为空，为空时显示'无素材'</li>";
echo "<li><strong>事件处理修复：</strong> 点击查看时检查是否有素材，没有时显示提示信息</li>";
echo "<li><strong>验证逻辑修复：</strong> 允许只上传女生素材或只上传男生素材</li>";
echo "</ul>";

echo "<h2>使用建议</h2>";
echo "<ul>";
echo "<li>建议至少上传一种素材（女生或男生）</li>";
echo "<li>如果只上传男生素材，女生素材列会显示'无素材'</li>";
echo "<li>如果只上传女生素材，男生素材列会显示'无素材'</li>";
echo "<li>两种素材都可以单独使用，也可以同时使用</li>";
echo "</ul>";
?>
