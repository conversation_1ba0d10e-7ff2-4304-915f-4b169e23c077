<?php
/**
 * 测试回调URL生成
 */

// 简化测试，不依赖ThinkPHP框架

echo "=== 回调URL测试 ===\n";

// 手动构建URL
$domain = isset($_SERVER['HTTP_HOST']) ? $_SERVER['HTTP_HOST'] : 'jsdao.cc';
$protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'https'; // 强制使用https
$manual_url = $protocol . '://' . $domain . '/api/callback/pay?gateway=Epusdt';

echo "手动构建的URL: " . $manual_url . "\n";

// 测试当前域名
echo "当前域名: " . (isset($_SERVER['HTTP_HOST']) ? $_SERVER['HTTP_HOST'] : '未设置') . "\n";
echo "当前协议: " . (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'HTTPS' : 'HTTP') . "\n";

// 建议的回调URL
echo "\n=== 建议使用的回调URL ===\n";
echo "https://jsdao.cc/api/callback/pay?gateway=Epusdt\n";

echo "\n=== 测试回调接口是否可访问 ===\n";
$test_url = "https://jsdao.cc/api/callback/pay?gateway=Epusdt";
echo "测试URL: " . $test_url . "\n";

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $test_url);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, '{"test": "data"}');
curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 10);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

$response = curl_exec($ch);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

echo "HTTP状态码: " . $http_code . "\n";
echo "响应内容: " . $response . "\n";
if ($error) {
    echo "CURL错误: " . $error . "\n";
}

if ($http_code == 200) {
    echo "✅ 回调接口可以访问\n";
} else {
    echo "❌ 回调接口访问失败\n";
}
?>
