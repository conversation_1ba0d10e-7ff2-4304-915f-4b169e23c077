<?php
/**
 * 根据EPUSDT官方文档的测试脚本
 */

echo "=== EPUSDT官方文档签名算法测试 ===\n\n";

$api_url = 'https://pay.jsdao.cc';
$api_key = 'B81BA11A6528EC298C7DD88C144B8D882568BC1D';

// 官方文档示例验证
echo "1. 官方文档示例验证:\n";
$official_example = [
    'order_id' => '20220201030210321',
    'amount' => 42,
    'notify_url' => 'http://example.com/notify',
    'redirect_url' => 'http://example.com/redirect'
];

$official_key = 'epusdt_password_xasddawqe';
$official_signature = epusdtSign($official_example, $official_key);
echo "官方示例签名: " . $official_signature . "\n";
echo "预期签名: 1cd4b52df5587cfb1968b0c0c6e156cd\n";
echo "签名匹配: " . ($official_signature === '1cd4b52df5587cfb1968b0c0c6e156cd' ? '✅ 是' : '❌ 否') . "\n\n";

// 使用我们的API密钥测试
echo "2. 使用我们的API密钥测试:\n";
$test_data = [
    'order_id' => 'TEST_ORDER_' . time(),
    'amount' => 100.5,
    'notify_url' => 'https://jsdao.cc/api/callback/pay?gateway=Epusdt',
    'redirect_url' => 'https://jsdao.cc/api/my/index'
];

$signature = epusdtSign($test_data, $api_key);
$test_data['signature'] = $signature;

echo "测试数据:\n";
echo json_encode($test_data, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES) . "\n\n";

echo "签名计算过程:\n";
$sorted_data = $test_data;
unset($sorted_data['signature']);
ksort($sorted_data);

$sign_string = '';
foreach ($sorted_data as $key => $value) {
    if ($value === '' || $value === null) continue;
    if ($sign_string !== '') $sign_string .= '&';
    $sign_string .= $key . '=' . $value;
}
$sign_string .= $api_key;

echo "待签名字符串: " . $sign_string . "\n";
echo "MD5签名: " . $signature . "\n\n";

// 发送测试请求
echo "3. 发送API请求测试:\n";
$response = sendRequest($api_url . '/api/v1/order/create-transaction', $test_data);
echo "响应结果: " . $response . "\n\n";

// 测试不同的amount格式
echo "4. 测试不同的amount格式:\n";
$amount_tests = [
    'integer' => 100,
    'float' => 100.5,
    'string_integer' => '100',
    'string_float' => '100.5'
];

foreach ($amount_tests as $type => $amount) {
    echo "测试 {$type} 格式 (amount = " . var_export($amount, true) . "):\n";
    
    $test = [
        'order_id' => 'TEST_' . time() . '_' . $type,
        'amount' => $amount,
        'notify_url' => 'https://jsdao.cc/api/callback/pay?gateway=Epusdt',
        'redirect_url' => 'https://jsdao.cc/api/my/index'
    ];
    
    $test['signature'] = epusdtSign($test, $api_key);
    echo "签名: " . $test['signature'] . "\n";
    
    $response = sendRequest($api_url . '/api/v1/order/create-transaction', $test);
    echo "响应: " . $response . "\n";
    echo str_repeat("-", 50) . "\n";
}

/**
 * EPUSDT官方签名函数
 */
function epusdtSign(array $parameter, string $signKey) {
    ksort($parameter);
    reset($parameter);
    $sign = '';
    
    foreach ($parameter as $key => $val) {
        if ($val == '') continue;
        if ($key != 'signature') {
            if ($sign != '') {
                $sign .= "&";
            }
            $sign .= "$key=$val";
        }
    }
    
    $sign = md5($sign . $signKey); // 密码追加进入开始MD5签名
    return strtolower($sign); // 确保返回小写
}

/**
 * 发送HTTP请求
 */
function sendRequest($url, $data) {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Accept: application/json',
        'User-Agent: Mozilla/5.0 (compatible; EPUSDT-Client/1.0)'
    ]);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($response === false) {
        return "cURL错误: " . $error;
    }
    
    return "HTTP " . $httpCode . " | " . $response;
}

?>
