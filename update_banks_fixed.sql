-- 修正版：更新银行数据为指定的墨西哥银行列表
-- 先查看表结构，然后根据实际字段进行更新

-- 1. 查看表结构（请先执行这些查询了解表结构）
-- DESCRIBE `bank_list`;
-- DESCRIBE `bank`;

-- 2. 禁用所有现有银行（只更新 status 字段）
UPDATE `bank_list` SET `status` = 0;
UPDATE `bank` SET `status` = 0;

-- 3. 插入新的墨西哥银行数据到 bank_list 表（简化版，只包含基本字段）
INSERT INTO `bank_list` (`name`, `status`) VALUES
('BBVA México', 1),
('Banamex', 1),
('Banorte', 1),
('Santander México', 1),
('HSBC México', 1),
('Banco Azteca', 1);

-- 4. 插入新的墨西哥银行数据到 bank 表（简化版，只包含基本字段）
INSERT INTO `bank` (`title`, `name`, `status`) VALUES
('BBVA México', 'BBVA México', 1),
('Banamex', 'Banamex', 1),
('Banorte', 'Banorte', 1),
('Santander México', 'Santander México', 1),
('HSBC México', 'HSBC México', 1),
('Banco Azteca', 'Banco Azteca', 1);

-- 5. 查询验证结果
SELECT * FROM `bank_list` WHERE `status` = 1;
SELECT * FROM `bank` WHERE `status` = 1;
